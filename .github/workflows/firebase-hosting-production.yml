# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy Production to Firebase Hosting on BizCrunch
"on":
  push:
    branches:
      - main

env:
  REACT_APP_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
  REACT_APP_FIREBASE_MEASUREMENT_ID: ${{ secrets.FIREBASE_MEASUREMENT_ID }}
  REACT_APP_GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
  REACT_APP_MIXPANEL_TOKEN: ${{ secrets.MIXPANEL_TOKEN }}
  REACT_APP_STRIPE_PUBLIC_KEY: ${{ secrets.STRIPE_PUBLIC_KEY }}

  REACT_APP_BASEURL: ${{ vars.BASEURL }}
  REACT_APP_BASEURL2: ${{ vars.BASEURL2 }}

  REACT_APP_FIREBASE_APP_ID: ${{ vars.FIREBASE_APP_ID }}
  REACT_APP_FIREBASE_AUTH_DOMAIN: ${{ vars.FIREBASE_AUTH_DOMAIN }}
  REACT_APP_FIREBASE_MESSAGING_SENDER_ID: ${{ vars.FIREBASE_MESSAGING_SENDER_ID }}
  REACT_APP_FIREBASE_PROJECT_ID: ${{ vars.FIREBASE_PROJECT_ID }}
  REACT_APP_FIREBASE_STORAGE_BUCKET: ${{ vars.FIREBASE_STORAGE_BUCKET }}
  REACT_APP_CAMPAIGN_TRIAL_DOMAIN: ${{ vars.CAMPAIGN_TRIAL_DOMAIN }}

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    environment: PRODUCTION
    steps:
      - uses: actions/checkout@v2
      - run: npm ci && GENERATE_SOURCEMAP=false npm run build
      - run: |
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}' > /tmp/firebase-service-account.json
          export GOOGLE_APPLICATION_CREDENTIALS=/tmp/firebase-service-account.json
          npx firebase-tools projects:list
          npx firebase-tools use production
          npx firebase-tools deploy --only hosting:bizcrunch-live
