# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on merge
"on":
  push:
    branches:
      - main

env:
  REACT_APP_BASEURL: https://us-central1-silent-matter-386414.cloudfunctions.net/
  REACT_APP_BASEURL2: https://europe-west2-silent-matter-386414.cloudfunctions.net/
  REACT_APP_LOCALURL: http://localhost:5001/silent-matter-386414/us-central1/
  REACT_APP_LOCALURL2: http://localhost:5001/silent-matter-386414/europe-west2/
  REACT_APP_STRIPE_TEST: pk_test_51Ln0MqBpQudBufNNCGMwuTZ50eb2iwpykj3tO4c7Fvwexx8ZdXYjo4kGJJW4kgesH5hvXNZYtYEuB6rRyItVpQHs00ljEA7YZX
  REACT_APP_STRIPE_LIVE: pk_live_51Ln0MqBpQudBufNNdqRJFDDe3RB3ZXaReDvEd1kGxBxjVI31KwS7JsJTk5Vl8wzdWvkioaFfTF9ycMI4NZ0Z9gtY00a7aZEp4O
  REACT_APP_MIXPANEL_TOKEN: c2bfb21ff33e0336910627541531bffb
  REACT_APP_FIREBASE_API_KEY: AIzaSyDxBhq7tyB5fpdl-0EDWZyYkwFfuI3pICg
  REACT_APP_FIREBASE_AUTH_DOMAIN: silent-matter-386414.firebaseapp.com
  REACT_APP_FIREBASE_PROJECT_ID: silent-matter-386414
  REACT_APP_FIREBASE_STORAGE_BUCKET: silent-matter-386414.appspot.com
  REACT_APP_FIREBASE_MESSAGING_SENDER_ID: ************
  REACT_APP_FIREBASE_APP_ID: 1:************:web:237a8e514e5e086e4a3eb1

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - run: npm ci && npm run build
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: "${{ secrets.GITHUB_TOKEN }}"
          firebaseServiceAccount: "${{ secrets.FIREBASE_SERVICE_ACCOUNT_SILENT_MATTER_386414 }}"
          channelId: live
          projectId: silent-matter-386414
          expires: 30d
