import { test, expect } from '@playwright/test';
import { getUserQuota, loginasTestUser } from '../helpers/command';
test('Collect already collected company - quota shouldnt detect', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    let obj = await getUserQuota(page);
    await page.waitForResponse(response =>
    response.request().method() === 'GET' && response.url().includes('/api/collections') && response.status() === 200
    );
    await page.getByText('Collections').click();
    await page.getByText('Campaigns').click();
    await page.getByRole('cell', { name: 'BothCampaign' }).first().click();
    await page.getByRole('checkbox', { name: 'Select 2154 Companies' }).click();
    await expect(page.getByRole('heading', { name: 'Your selection exceeds your monthly quota limit' })).toBeVisible();
    await expect(page.getByText('Email Quota')).toBeVisible();
    await expect(page.getByText(`Total: ${obj.totalEmailQuota} | Available: ${obj.totalEmailQuota - obj.usedEmailQuota} | Required: ${Math.abs(obj.totalEmailQuota - obj.usedEmailQuota - 1201)}`)).toBeVisible();
    await expect(page.getByText('Letter Quota', { exact: true })).toBeVisible();
    await expect(page.getByText(`Total:${obj.totalLetterQuota} | Available: ${obj.totalLetterQuota - obj.usedLetterQuota} | Required: ${Math.abs(obj.totalLetterQuota - obj.usedLetterQuota - 2134)}`)).toBeVisible();
    await page.getByRole('button', { name: 'Upgrade' }).click();
    await expect(page.getByText('+0 Email Outreach')).toBeVisible();
    await expect(page.getByText('+0 Letter Outreach')).toBeVisible();
    await page.getByRole('button', { name: 'or Change your plan' }).click();
    await expect(page.getByRole('tab', { name: 'Monthly billing' })).toBeVisible();
});