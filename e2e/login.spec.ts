import { test, expect } from '@playwright/test';

test('Login with wrong email', {
    tag: '@regression',
  }, async ({ page}) => {
    await page.goto("/");
    await page.getByRole('button', { name: 'Login' }).click();
    await page.getByText('Sign in').click();
    await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill('<EMAIL>');
    await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill('Test@123');
    await page.getByRole('button', { name: 'Sign in' }).click({force:true});
    await expect(page.getByText('Invalid username or password')).toBeVisible();
});

test('Login with wrong password', {
    tag: '@regression',
  }, async ({ page}) => {
    await page.goto("/");
    await page.getByRole('button', { name: 'Login' }).click();
    await page.getByText('Sign in').click();
    await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill('<EMAIL>');
    await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill('Test@123');
    await page.getByRole('button', { name: 'Sign in' }).click({force:true});
    await expect(page.getByText('Invalid username or password')).toBeVisible();
});
