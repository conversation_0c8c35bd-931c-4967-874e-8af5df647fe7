import { test, expect } from '@playwright/test';
import { register, verifyEmail, login, 
  checkoutPlan, finalOnboarding, emptyStateMenu, 
  collectionExistsEmptyCampaign,
  getFutureCancelYear} from '../helpers/command';

test.describe.configure({ mode: "serial" });
const collectionName = 'Collection' + Math.floor(Math.random() * (1000000 - 0)).toString();
const campaignName = 'Campaign'+Math.floor(Math.random() * (1000000 - 0)).toString();

test('Register user, verify email', {
  tag: '@e2e',
},async ({ page, context }) => {
  await page.goto("/");
  await register(page);
  await verifyEmail(page, context);
  await page.goto("/verify-email");
});

test('Login, subscribe', {
  tag: '@e2e',
},async ({page}) => {
  await page.goto("/");
  await login(page);
  await expect(page.getByRole('button', { name: 'Start Your 7 Day Free Trial' }).first()).toBeVisible();
  await checkoutPlan(page, 'SearchMonthly-Trial');
  await expect(page.getByText('Collection (Trial): 0/50')).toBeVisible();
  await expect(page.getByText('Letter Outreach (Trial): 0/5')).toBeVisible();
  await expect(page.getByText('Email Outreach (Trial): 0/25')).toBeVisible();
  await emptyStateMenu(page);
});

test('Login, search, save to collection and check email campaign flow', {
  tag: '@e2e',
},async({page}) => { 
  await page.goto("/");
  await login(page);
  await page.getByText('Advanced').click();
  await page.getByText('Company', { exact: true }).click();
  await page.getByText('Company name').click();
  await page.getByRole('textbox', { name: 'Enter company name' }).fill('CY TRADING LTD');
  await page.getByRole('textbox', { name: 'Enter company name' }).press('Enter');
  await page.locator('div').filter({ hasText: /^Company name must contain CY TRADING LTD$/ }).first().click();
  await page.getByRole('button', { name: 'Done' }).click();
  await expect(page.getByText('1 entries')).toBeVisible();
  await page.getByRole('button', { name: 'Search' }).click();
  await finalOnboarding(page);
  await page.getByRole('button', { name: 'Save to Collection' }).click();
  await page.getByRole('combobox').click();
  await page.getByText('+ Create new one').click();
  await page.getByRole('textbox', { name: 'Enter new collection name' }).fill(collectionName);
  await page.getByRole('button', { name: 'Save' }).click();
  await page.getByRole('button', { name: 'Start Campaign' }).click();
  await collectionExistsEmptyCampaign(page, 'Search');
  await page.getByRole('button', { name: 'New Campaign' }).click(); 
  await page.getByRole('textbox', { name: 'New campaign' }).fill(campaignName);
  await page.getByRole('button', { name: 'Save' }).click();
  await page.getByRole('checkbox', { name: `Select ${collectionName}` }).first().click();
  await page.getByRole('button', { name: 'Save & Proceed' }).click();
  await page.waitForLoadState('load');
  await page.getByRole('textbox', { name: 'What\'s the name of your Hold' }).click();
  await page.getByRole('textbox', { name: 'What\'s the name of your Hold' }).fill('Test', {force:true});
  await page.getByRole('textbox', { name: 'Phone Number' }).fill('+44123456789', {force:true});
  await page.getByRole('textbox', { name: 'Your Current Main Website' }).fill('www.test.co', {force:true});
  await page.getByRole('textbox', { name: 'Address' }).fill('123 William Street, New York, NY, USA', {force:true});
  await page.getByText('123 William StreetNew York, NY, USA').click({force:true});
  await page.getByRole('button', { name: 'Save & Proceed' }).click();
  await page.getByRole('button', { name: 'Save & Proceed' }).click();
  await page.getByRole('button', { name: 'Save & Proceed' }).click();
  await page.waitForLoadState('load');
  await expect(page.getByRole('button', { name: 'Start Campaign' })).toBeEnabled();
  await expect(page.getByText('Collection (Trial): 1/50')).toBeVisible();
});

test('End trial, upgrade, topup, cancel plan', {
  tag: '@e2e',
},async({page}) => { 
  await page.goto("/");
  await login(page);
  await page.getByText('TU', { exact: true }).click();
  await page.locator('div').filter({ hasText: /^Billing$/ }).click();
  await page.getByRole('button', { name: 'Manage Plan' }).click();
  await page.getByRole('tab', { name: 'Monthly billing' }).click();
  await page.getByText('Unlock full quota now').click({force:true});
  await page.getByRole('button', { name: 'Activate Now' }).click();
  await expect(page.getByText('Email Outreach : 0/500')).toBeVisible();
  await expect(page.getByText('Letter Outreach : 0/5')).toBeVisible();
  await expect(page.getByText('Collection : 0/2000')).toBeVisible();
  if(await page.getByRole('button', { name: 'Close' }).isVisible())
    await page.getByRole('button', { name: 'Close' }).click();
  await page.getByRole('button', { name: 'Manage Plan' }).click();
  await page.locator('div').filter({ hasText: /^Collection\$00 Collection$/ }).getByLabel('Increase amount').click();
  await page.locator('div').filter({ hasText: /^Letter Outreach\$00 Letter Outreach$/ }).getByLabel('Increase amount').click();
  await page.locator('div').filter({ hasText: /^Email Outreach\$00 Email Outreach$/ }).getByLabel('Increase amount').click();
  await page.getByRole('button', { name: 'Top up' }).click();
  await expect(page.getByText('Collection : 0/2500')).toBeVisible();
  await expect(page.getByText('Letter Outreach : 0/105')).toBeVisible();
  await expect(page.getByText('Email Outreach : 0/1000')).toBeVisible();
  await page.getByRole('button', { name: 'Manage Plan' }).click();
  await page.getByRole('tab', { name: 'Annual billing (save > 15%)' }).click();
  await expect(page.getByRole('button', { name: 'Upgrade' }).nth(1)).toBeEnabled();
  await page.getByRole('button', { name: 'Upgrade' }).first().click();
  await expect(page.getByText('Collection : 0/24000')).toBeVisible();
  await expect(page.getByText('Letter Outreach : 0/60')).toBeVisible();
  await expect(page.getByText('Email Outreach : 0/6000')).toBeVisible();
  await page.getByRole('button', { name: 'Manage Plan' }).click();
  await page.getByRole('button', { name: 'Cancel Plan' }).click();
  await expect(page.getByText('Are you sure you want to cancel your plan?')).toBeVisible();
  const cancelDate = await getFutureCancelYear();
  await page.getByRole('button', { name: 'Cancel Plan' }).click({force:true});
  await expect(page.getByText(`Cancels on ${cancelDate}`)).toBeVisible();
  await expect(page.getByText('Active')).toBeVisible();
  await page.getByText('TU', { exact: true }).click();
  await page.locator('div').filter({ hasText: /^Log out$/ }).click();
});