import { test, expect } from '@playwright/test';
import { validate<PERSON>mail<PERSON>ield } from '../helpers/command';

test('Register with empty values-mandatory fields check', {
  tag: '@regression',
}, async ({ page }) => {
  await page.goto("/");
  await page.getByRole('button', { name: 'Login' }).click();
  await page.getByRole('button', { name: 'Register' }).click();
  await expect(page.getByText('Please enter your name')).toBeVisible();
  await page.getByRole('textbox', { name: '<PERSON>' }).fill('Test User');
  await page.getByRole('button', { name: 'Register' }).click();
  await expect(page.getByText('Please enter an email')).toBeVisible();
  await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill('<EMAIL>');
  await page.getByRole('button', { name: 'Register' }).click();
  await expect(page.getByText('Please enter a password')).toBeVisible();
});

test('Register with already existing email', {
  tag: '@regression',
}, async ({ page }) => {
  await page.goto("/");
  await page.getByRole('button', { name: 'Login' }).click();
  await page.getByRole('textbox', { name: 'John Doe' }).fill('Test User');
  await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill('Test@123');
  await page.getByRole('button', { name: 'Register' }).click();
  await expect(page.getByText('This email is already being used by an existing user')).toBeVisible();
});

test('Validate email invalid format', {
  tag: '@regression',
}, async ({ page }) => {
    await page.goto("/");
    await page.getByRole('button', { name: 'Login' }).click();
    await page.getByRole('textbox', { name: 'John Doe' }).fill('Test User');
    await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill('Test@123');
    await validateEmailField(page, "testuser", "Invalid email structure");
    await validateEmailField(page, "user@domain", "Invalid email structure");
    await validateEmailField(page, "user@domain,com", "Invalid email structure");
    await validateEmailField(page, "<EMAIL>", "Invalid email structure");
    await validateEmailField(page, "@missingusername.com", "Invalid email structure");
    await validateEmailField(page, ".<EMAIL>", "Invalid email structure");
    await validateEmailField(page, "<EMAIL>.", "Invalid email structure");
    await validateEmailField(page, "<EMAIL>.", "Invalid email structure");
    await validateEmailField(page, "user@<EMAIL>", "Invalid email structure");
});

test('Validate password', {
  tag: '@regression',
}, async ({ page }) => {
    await page.goto("/");
    await page.getByRole('button', { name: 'Login' }).click();
    await page.getByRole('textbox', { name: 'John Doe' }).fill('Test User');
    await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill('<EMAIL>');
    await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill('Test');
    await page.getByRole('button', { name: 'Register' }).click();
    await page.getByText('Password must be at least 6').click();
});