import { test, expect} from '@playwright/test';
import { getCurrentDate, getCurrentDealDate, loginasTestUser } from '../helpers/command';
import path from 'path';

test.describe.configure({ mode: "serial" });
const dealName = 'Deal' + Math.floor(Math.random() * (1000000 - 0)).toString();

test('Navigate to Deal Tracker and validate all deal stages', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.locator('div').filter({ hasText: /^Deal Tracker$/ }).click();
    await expect(page.getByRole('heading', { name: 'Deal Tracker' })).toBeVisible();
    await expect(page.getByText('Manage and track your deals through different stages')).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Interest' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Research' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Offer' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Heads of Terms' })).toBeVisible();
    await (page.getByRole('heading', { name: 'Due Diligence' })).scrollIntoViewIfNeeded();
    await expect(page.getByRole('heading', { name: 'Due Diligence' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Share Purchase Agreement' })).toBeVisible();
    await (page.getByRole('heading', { name: 'Deal Completion' })).scrollIntoViewIfNeeded();
    await expect(page.getByRole('heading', { name: 'Deal Completion' })).toBeVisible();
});

test('Create a New Deal', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.locator('div').filter({ hasText: /^Deal Tracker$/ }).click();
    await page.getByRole('button', { name: 'Add New Deal' }).click();
    await expect(page.getByRole('heading', { name: 'New Deal' })).toBeVisible();
    await page.getByRole('textbox', { name: 'Company Name *' }).fill(dealName);
    await page.getByRole('checkbox', { name: 'Mark As Important' }).click();
    await expect(page.getByRole('textbox', { name: 'Country' })).toHaveValue('United Kingdom');
    await page.getByRole('textbox', { name: 'Company ID Number *' }).fill('Test');
    await page.getByRole('textbox', { name: 'Website' }).fill('test.co.uk');
    await page.getByRole('textbox', { name: 'Contact Name' }).fill('Test');
    await page.getByRole('textbox', { name: 'Email Address' }).fill('<EMAIL>');
    await page.getByRole('textbox', { name: 'Phone Number' }).fill('+44234324232');
    await page.getByRole('combobox').filter({ hasText: 'Select deal stage' }).click();
    await page.getByRole('option', { name: 'Interest' }).click();
    await page.getByRole('combobox', { name: 'Select currency' }).click();
    await page.getByRole('option', { name: 'USD' }).click();
    await page.getByRole('button', { name: 'Deal Documents' }).click();
    await page.locator('input[type="file"]').setInputFiles(path.join(__dirname, 'MyDocument11.pdf'));
    await page.getByRole('button', { name: 'Notes' }).click();
    await page.getByRole('button', { name: 'Add New Note' }).click();
    await page.getByPlaceholder('Enter your note here...').fill('Note1');
    await page.waitForTimeout(5000);
    await page.getByRole('button', { name: 'Research' }).click();
    await page.getByRole('textbox', { name: 'Asking Price' }).fill('121333');
    await page.getByRole('textbox', { name: 'Estimated Valuation' }).fill('1313');
    await page.getByRole('button', { name: 'Offer' }).click();
    await page.getByRole('textbox', { name: 'Your Offer' }).fill('232323');
    await page.getByRole('region', { name: 'Offer' }).getByRole('combobox').click();
    await page.getByText('Pending').click();
    await page.getByRole('button', { name: 'Heads of Terms' }).click();
    await page.getByRole('radio', { name: 'Yes' }).click();
    await page.getByRole('button', { name: 'Due Diligence' }).click();
    const dealDate =  await getCurrentDealDate()
    await page.getByRole('textbox', { name: 'Due Diligence Began On' }).fill(dealDate);
    await page.getByRole('textbox', { name: 'Target Due Diligence' }).fill(dealDate);
    await page.getByRole('textbox', { name: 'Enter link to virtual data' }).fill('www.google.com');
    const dataRoomPromise =  page.waitForEvent('popup');
    await page.getByRole('button', { name: 'Open virtual data room' }).click();
    const dataRoomPage = await dataRoomPromise;
    await dataRoomPage.close();
    await page.getByRole('button', { name: 'Share Purchase Agreement' }).click();
    await page.getByRole('textbox', { name: 'Link to Latest SPA' }).click();
    await page.getByRole('textbox', { name: 'Link to Latest SPA' }).fill('www.google.com');
    await page.getByRole('textbox', { name: 'Date of Draft' }).fill(dealDate);
    await page.locator('#spa-agreed-yes').click();
    await page.locator('#buyer-signed-yes').click();
    await page.locator('#seller-signed-yes').click();
    await page.getByRole('button', { name: 'Deal Completion' }).click();
    await page.getByRole('textbox', { name: 'Final Valuation Agreed', exact: true }).fill('121212');
    await page.getByRole('textbox', { name: 'Final Valuation Agreed On' }).fill(dealDate);
    await page.locator('div').filter({ hasText: /^Account StatusSelect status$/ }).getByRole('combobox').click();
    await page.getByText('Not Started').click();
    await page.locator('div').filter({ hasText: /^Completion StatusSelect status$/ }).getByRole('combobox').click();
    await page.getByText('Pending').click();
    await page.getByRole('textbox', { name: 'IBAN (account reference' }).click();
    await page.getByRole('textbox', { name: 'IBAN (account reference' }).fill('AD123');
    await page.getByRole('checkbox', { name: 'Dual-Authorised?' }).click();
    if(await page.locator('[title="chat widget"]').nth(3).contentFrame().getByRole('button', { name: 'Welcome to our site, if you' }).isVisible()){
        await page.locator('[title="chat widget"]').nth(3).contentFrame().getByRole('button', { name: 'Welcome to our site, if you' }).hover();
        await page.locator('[title="chat widget"]').nth(3).contentFrame().getByRole('button', { name: 'Chat attention grabber' }).click({force:true});
    }
    await page.getByRole('button', { name: 'Create Deal' }).click({force:true});
    await Promise.all([
      page.waitForResponse(response =>
          response.request().method() === 'POST' && response.url().includes('/api/deals') && response.status() === 201),
      page.waitForResponse(response =>
          response.request().method() === 'POST' && response.url().includes('/documents') && response.status() === 201),
      page.waitForResponse(response =>
          response.request().method() === 'POST' && response.url().includes('/notes') && response.status() === 201),
      page.waitForResponse(response =>
          response.request().method() === 'POST' && response.url().includes('/offers') && response.status() === 201),
      ]);
});

test('View, delete uploaded document', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.locator('div').filter({ hasText: /^Deal Tracker$/ }).click();
    await page.getByRole('button', { name: dealName }).first().click();
    await page.getByRole('button', { name: 'Deal Documents' }).click();
    const uploadPromise = page.waitForEvent('popup');
    await page.getByRole('button', { name: 'View document' }).click();
    const uploadPage = await uploadPromise;
    await uploadPage.close();
    await page.getByRole('button', { name: 'Delete document' }).click();
    await expect(page.getByRole('cell', { name: 'No documents uploaded yet' })).toBeVisible();
    await page.locator('input[type="file"]').setInputFiles(path.join(__dirname, 'MyDocument11.pdf'));
    await expect(page.getByText('MyDocument11.pdf')).toBeVisible();
    await page.getByLabel('Deal Documents').getByRole('button').filter({ hasText: /^$/ }).click();
    await expect(page.getByText('MyDocument11.pdf')).not.toBeVisible();
});

test('Create, update and delete offers/notes', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.locator('div').filter({ hasText: /^Deal Tracker$/ }).click();
    await page.getByRole('button', { name: dealName }).first().click();
    await page.getByRole('button', { name: 'Notes' }).click();
    await expect(page.getByText('Previous Notes (1)')).toBeVisible();
    await expect(page.getByText('Note1')).toBeVisible();
    await page.getByRole('button').filter({ hasText: /^$/ }).nth(1).click();
    await page.getByText('Note1').fill('Note1Update');
    await page.getByRole('button', { name: 'Save', exact: true }).click();
    await page.waitForResponse(response =>
          response.request().method() === 'GET' && response.url().includes('/notes') && response.status() === 200);
    await expect(page.getByText('Note1Update')).toBeVisible();
    await page.getByRole('region', { name: 'Notes' }).getByRole('button').nth(2).click();
    await expect(page.getByRole('heading', { name: 'Note deleted successfully!' })).toBeVisible();
    await expect(page.getByText('No notes yet. Click the + button to add your first note.')).toBeVisible();
    await page.getByRole('button', { name: 'Add New Note' }).click();
    await page.getByRole('textbox', { name: 'New Note' }).fill('Note2');
    await page.getByRole('button', { name: 'Save Note' }).click();
    await page.waitForResponse(response =>
          response.request().method() === 'GET' && response.url().includes('/notes') && response.status() === 200);
    await expect(page.getByText('Note2')).toBeVisible();
    await page.getByRole('button', { name: 'Offer' }).click();
    const currentDate = await getCurrentDate();
    await page.getByRole('row', { name: `${currentDate} USD 232,323 Pending` }).getByRole('button').first().click();
    await page.getByRole('cell', { name: 'USD' }).getByRole('textbox').fill('66666');
    await page.getByRole('cell').filter({ hasText: 'Pending' }).click();
    await page.getByRole('option', { name: 'Accepted' }).click();
    await page.getByRole('button', { name: 'Save', exact: true }).click();
    await page.waitForResponse(response =>
          response.request().method() === 'GET' && response.url().includes('/offers') && response.status() === 200);
    await expect(page.getByRole('row', { name: `${currentDate} USD 66,666 Accepted` })).toBeVisible();
    await page.getByRole('row', { name: `${currentDate} USD 66,666 Accepted` }).getByRole('button').nth(1).click();
    await expect(page.getByText('No offers yet')).toBeVisible();
    await page.getByRole('textbox', { name: 'Your Offer' }).fill('1232345');
    await page.getByRole('region', { name: 'Offer' }).getByRole('combobox').click();
    await page.getByText('Pending').click();
    await page.getByRole('button', { name: 'Save Offer' }).click();
    await page.waitForResponse(response =>
          response.request().method() === 'GET' && response.url().includes('/offers') && response.status() === 200);
    await expect(page.getByRole('row', { name: `${currentDate} USD 1,232,340 Pending` }).getByRole('button').first()).toBeVisible();
});

test('Drag and move deal to other stage', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.locator('div').filter({ hasText: /^Deal Tracker$/ }).click();
    await page.getByRole('button', { name: `${dealName} Details`}).first().dragTo(page.locator('div:nth-child(5) > div > .flex-1'));
    await page.getByRole('button', { name: 'Cancel' }).click();
    while(!(page.getByRole('heading', { name: 'Due Diligence' })).isVisible()){
          await page.getByRole('button', { name: 'Scroll Right' }).click();
    }
    await expect(page.getByRole('heading', { name: 'Due Diligence' })).toBeVisible();
    await expect(page.getByRole('button', { name: `${dealName} Details` }).first()).toBeVisible();
});