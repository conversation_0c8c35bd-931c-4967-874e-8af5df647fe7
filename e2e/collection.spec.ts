import { test, expect} from '@playwright/test';
import { getCurrentDate, getUserQuota, loginasTestUser } from '../helpers/command';

test.describe.configure({ mode: "serial" });
const collectionName = 'Collection' + Math.floor(Math.random() * (1000000 - 0)).toString();

test('Save Collection and Visit Collection Detail', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.getByText('Advanced').click();
    await page.getByText('Company', { exact: true }).click();
    await page.getByText('Company name').click();
    await page.getByRole('textbox', { name: 'Enter company name' }).fill('B & B POTATOES LIMITED');
    await page.getByRole('textbox', { name: 'Enter company name' }).press('Enter');
    await page.locator('div').filter({ hasText: /^Company name must contain B & B POTATOES LIMITED$/ }).first().click();
    await page.getByRole('button', { name: 'Done' }).click();
    await expect(page.getByText('1 entries')).toBeVisible();
    await page.getByRole('button', { name: 'Search' }).click();
    const text = await page.locator('div.column.ai-start').filter({hasText: 'companies selected'}).textContent();
    const match = text?.match(/(\d+)\s+companies\s+selected/);
    let obj = await getUserQuota(page);
    if (match) {
        const searchCount = parseInt(match[1], 10);
        await page.getByRole('button', { name: 'Save to Collection' }).click();
        await expect(page.getByText(`Total quota${obj.totalCollectionQuota}`)).toBeVisible();
        await expect(page.getByText(`Available quota${obj.totalCollectionQuota-obj.usedCollectionQuota}`)).toBeVisible();
        await expect(page.getByText(`Remaining quota after collecting${obj.totalCollectionQuota-obj.usedCollectionQuota-searchCount}`)).toBeVisible();
        await page.getByRole('combobox').click();
        await page.getByText('+ Create new one').click();
        await page.getByRole('textbox', { name: 'Enter new collection name' }).fill(collectionName);
        await page.getByRole('button', { name: 'Save' }).click();
        const response = await page.waitForResponse(response =>
          response.request().method() === 'POST' && response.url().includes('/collections') && response.status() === 201
        );
        if(obj.totalCollectionQuota <= 2000){
          await expect(page.getByText(`Collection : ${obj.usedCollectionQuota+searchCount}/${obj.totalCollectionQuota}`)).toBeVisible();
        }else{
          await expect(page.getByText(`Collection : ${obj.usedCollectionQuota}/${obj.totalCollectionQuota-searchCount}`)).toBeVisible();
        }
        await expect(page.getByText(`Letter Outreach : ${obj.usedLetterQuota}/${obj.totalLetterQuota}`)).toBeVisible();
        await expect(page.getByText(`Email Outreach : ${obj.usedEmailQuota}/${obj.totalEmailQuota}`)).toBeVisible();
        await page.getByRole('button', { name: 'View Collection' }).click();
        if(!await page.getByText(collectionName).isVisible()){
          await page.waitForTimeout(10000);
          await page.getByText('Collections').click();
        }
        await page.getByRole('textbox', { name: 'Search collections by name...' }).fill(collectionName);
        await page.getByRole('textbox', { name: 'Search collections by name...' }).press('Enter');
        const todayDate = await getCurrentDate();
        await expect(page.getByRole('row', { name: `${collectionName} ${searchCount} ${searchCount} ${searchCount} ${todayDate}` })).toBeVisible();
        await page.getByRole('button').nth(2).click();
        await expect(page.locator('table.caption-bottom>tbody>tr')).toHaveCount(searchCount);
        const downloadPromise = page.waitForEvent('download');
        await page.getByRole('button', { name: 'Download Collection' }).click();
        await downloadPromise;
        await page.getByText('Back', { exact: true }).click();
        await expect(page.getByRole('textbox', { name: 'Search collections by name...' })).toBeVisible()  
    }
});

test('Delete Company from Collection', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.getByText('Collections').click();
    await page.getByRole('textbox', { name: 'Search collections by name...' }).fill(collectionName);
    await page.getByRole('textbox', { name: 'Search collections by name...' }).press('Enter');
    await page.getByText(collectionName).click();
    await expect(page.getByText('Total companies in the collection: 1')).toBeVisible();
    await expect(page.getByRole('cell', { name: 'B & B POTATOES LIMITED' })).toBeVisible();
    await page.getByRole('button', { name: 'Manage Companies' }).click();
    await page.getByRole('checkbox').click();
    await page.getByRole('button', { name: 'Delete 1 Company' }).click();
    await page.getByRole('button', { name: 'Delete' }).click();
    await expect(page.getByRole('heading', { name: 'Companies deleted successfully!' })).toBeVisible();
    await expect(page.getByRole('cell', { name: 'B & B POTATOES LIMITED' })).not.toBeVisible();
    await expect(page.getByText('Total companies in the collection: 0')).toBeVisible();
});

test('Delete Collection', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.getByText('Collections').click();
    await page.getByRole('textbox', { name: 'Search collections by name...' }).fill(collectionName);
    await page.getByRole('textbox', { name: 'Search collections by name...' }).press('Enter');
    await page.getByRole('button').nth(3).click();
    await page.getByRole('button', { name: 'Delete' }).click();
    await expect(page.getByRole('heading', { name: 'Collection deleted successfully!' })).toBeVisible();
    await page.getByRole('textbox', { name: 'Search collections by name...' }).fill(collectionName);
    await page.getByRole('textbox', { name: 'Search collections by name...' }).press('Enter');
    await expect(page.getByRole('cell', { name: 'No collections found.' })).toBeVisible();
});