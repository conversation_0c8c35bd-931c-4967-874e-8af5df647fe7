import { test, expect } from '@playwright/test';
import { getCompaniesCount, getselectedCompaniesCount, getUserQuota, loginasTestUser } from '../helpers/command';

test('Collect already collected company - quota shouldnt detect', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    let obj = await getUserQuota(page);
    await page.getByText('Enter industry').click();
    await page.getByRole('textbox', { name: 'Enter SIC Code' }).fill('Manufacture of cocoa, and chocolate confectionery');
    await page.locator('label').filter({ hasText: 'Manufacture of cocoa, and chocolate confectionery' }).locator('path').nth(3).click();
    await page.locator('div').filter({ hasText: /^AI Company Search$/ }).getByRole('radio').check();
    await page.getByRole('textbox', { name: 'Search by company keywords' }).fill('chocolate');
    await page.getByRole('textbox', { name: 'Search by company keywords' }).press('Enter');
    await page.locator('div').filter({ hasText: /^SIC & ISIC Code$/ }).getByRole('radio').check();
    await page.getByRole('textbox', { name: 'Enter ISIC Code' }).fill('Manufacture Of Cocoa, Chocolate And Sugar Confectionery');
    await page.locator('label').filter({ hasText: 'Manufacture Of Cocoa, Chocolate And Sugar Confectionery' }).locator('path').nth(3).click();
    await page.waitForLoadState('load');
    await page.getByText('Enter revenue').click();
    if (await page.getByRole('combobox').first().isVisible()) {
        await page.getByRole('combobox').first().selectOption('250000');
    }else{
        await page.getByText('Enter revenue').click();
        await page.getByRole('combobox').first().selectOption('250000');
    }
    await page.getByRole('combobox').nth(1).selectOption('12500000');
    await page.getByText('Select acquire filter').click();
    await page.getByText('Simple Ownership').click();
    await page.getByText('Advanced').click();
    await page.getByText('Contact').click();
    await page.getByText('Companies with direct PSC').click();
    await page.getByText('Net Profit %').click();
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Net Profit', { exact: true }).click();
    await page.getByRole('combobox').first().selectOption('25000');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Debt Ratio').click();
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Fixed Tangible Asset : Net').click();
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Company', { exact: true }).click();
    await page.getByText('Company name').click();
    await page.getByRole('textbox', { name: 'Enter company name' }).fill('FOODS');
    await page.getByRole('textbox', { name: 'Enter company name' }).press('Enter');
    await page.getByRole('textbox', { name: 'Enter company name' }).fill('LTD');
    await page.getByRole('textbox', { name: 'Enter company name' }).press('Enter');
    await page.getByText('Company name must contain FOODS OR LTD').click();
    await page.getByText('Company number').click();
    await page.getByRole('textbox', { name: 'Enter company number' }).fill('********');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Incorporation date').click();
    await page.getByRole('combobox').first().selectOption('2010');
    await page.getByRole('combobox').nth(1).selectOption('2011');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Company status').click();
    await page.getByText('Active').click();
    await page.getByText('Account filing month').click();
    await page.getByRole('combobox').selectOption('3');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Financials').click();
    await page.getByText('Balance Sheet').click();
    await page.locator('div').filter({ hasText: /^Current Assets$/ }).first().click();
    await page.getByRole('combobox').first().selectOption('25000');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.locator('div').filter({ hasText: /^Fixed Assets$/ }).first().click();
    await page.getByRole('combobox').first().selectOption('50000');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Fixed Tangible Assets').click();
    await page.getByRole('combobox').first().selectOption('50000');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Total Assets').click();
    await page.getByRole('combobox').first().selectOption('50000');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Current Liabilities').click();
    await page.getByRole('combobox').first().selectOption('25000');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Long-term Liabilities').click();
    await page.getByRole('combobox').first().selectOption('50000');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Net Assets').click();
    await page.getByRole('combobox').first().selectOption('50000');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByText('Smart Metrics').click();
    await page.getByText('P&L').click();
    await page.getByText('People').click();
    await page.getByText('Ownership', { exact: true }).click();
    await page.locator('div').filter({ hasText: /^No\. of PSCs$/ }).first().click();
    await page.getByRole('combobox').first().selectOption('1');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.locator('div').filter({ hasText: /^Max Parent companiesFilter by the number of 'parent' entities that own a company$/ }).first().click();
    await page.getByRole('radio').first().check();
    await page.getByText('People to contact').click();
    await page.getByText('Majority Shareholders').click();
    await page.getByText('Majority Shareholder Age').click();
    await page.getByRole('combobox').first().selectOption('50');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.locator('span').filter({ hasText: /^Contact$/ }).click();
    await page.getByText('Employees').click();
    await page.locator('div').filter({ hasText: /^No\. of employees$/ }).nth(3).click();
    await page.getByRole('combobox').first().selectOption('1');
    await page.getByRole('button', { name: 'plus Apply filter' }).click();
    await page.getByRole('button', { name: 'Done' }).click();
    await expect(page.getByText('1 entries')).toBeVisible();
    await page.getByRole('button', { name: 'Search' }).click();
    await page.getByRole('button', { name: 'Save to Collection' }).click();
    await expect(page.getByText(`Total quota${obj.totalCollectionQuota}`)).toBeVisible();
    await expect(page.getByText(`Available quota${obj.totalCollectionQuota-obj.usedCollectionQuota}`)).toBeVisible();
    await expect(page.getByText(`Remaining quota after collecting${obj.totalCollectionQuota-obj.usedCollectionQuota}`)).toBeVisible();
});

test('Collect not already collected companies - quotas should detect', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    let obj = await getUserQuota(page);
    await page.getByText('Advanced').click();
    await page.getByText('Company', { exact: true }).click();
    await page.getByText('Company name').click();
    await page.getByRole('textbox', { name: 'Enter company name' }).fill('TEXTILE LTD');
    await page.getByRole('textbox', { name: 'Enter company name' }).press('Enter');
    await page.locator('div').filter({ hasText: /^Company name must contain TEXTILE LTD$/ }).first().click();
    await page.getByRole('button', { name: 'Done' }).click();
    await expect(page.getByText(/\d+ entries/)).toBeVisible();
    let count = await getCompaniesCount(page);
    await page.getByRole('button', { name: 'Search' }).click();
    await expect(page.getByText(`${count.companyCount} companies selected`)).toBeVisible();
    await page.getByRole('button', { name: 'Save to Collection' }).click();
    await expect(page.getByText(`Total quota${obj.totalCollectionQuota}`)).toBeVisible();
    await expect(page.getByText(`Available quota${obj.totalCollectionQuota-obj.usedCollectionQuota}`)).toBeVisible();
    await expect(page.getByText(`Remaining quota after collecting${obj.totalCollectionQuota-obj.usedCollectionQuota-count.companyCount}`)).toBeVisible();
});

test('Try to collect more than quota - topup', {
    tag: '@regression',
  }, async ({page}) =>{
    await page.goto("/");
    await loginasTestUser(page, '<EMAIL>', 'Test@123');
    await page.getByText('Enter revenue').click();
    if (await page.getByRole('combobox').first().isVisible()) {
      await page.getByRole('combobox').first().selectOption('35000000');
    }else{
      await page.getByText('Enter revenue').click();
      await page.getByRole('combobox').first().selectOption('35000000');
    }
    await expect(page.getByText(/^\d{1},\d{3}|\d{3} entries$/)).toBeVisible();
    await page.getByRole('button', { name: 'Search' }).click();
    await expect(page.getByText(/^\d+ companies selected$/)).toBeVisible();
    let count = await getselectedCompaniesCount(page);
    let quota = await getUserQuota(page);
    await page.getByRole('button', { name: 'Save to Collection' }).click();
    await expect(page.getByText(`Companies selected exceeds ${quota.totalCollectionQuota} limit. Change your selection or top-up now.`)).toBeVisible();
    await expect(page.getByText(`To save these companies to your collection, increase your quota limit by ${Math.abs(quota.totalCollectionQuota - quota.usedCollectionQuota - count.companyRetrievedCount)}.`)).toBeVisible();
    await expect(page.getByRole('button', { name: 'Complete' })).toBeVisible();
    await page.getByRole('button', { name: 'Top up' }).click();
    await expect(page.getByText('Increase your collection quota:')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Top up' })).toBeDisabled();
    await expect(page.getByText('$0+0 Collections')).toBeVisible();
    await page.getByRole('button', { name: 'Increase amount' }).click();
    await expect(page.getByRole('button', { name: 'Top up' })).toBeEnabled();
});