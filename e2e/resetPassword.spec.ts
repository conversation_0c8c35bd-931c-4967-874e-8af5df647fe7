import { test, expect } from "@playwright/test";
import { resetPassword } from "../helpers/command";

test(
  "Reset password, Login and logout",
  {
    tag: "@regression",
  },
  async ({ page, context }) => {
    await page.goto("/");
    await page.getByRole("button", { name: "Login" }).click();
    await page.getByText("Sign in").click();
    await page.getByText("Forgot your password?").click();
    await page
      .getByRole("textbox", { name: "e.g. <EMAIL>" })
      .fill("<EMAIL>");
    await page.getByRole("button", { name: "Submit" }).click();
    await expect(
      page.getByText("You have been sent an email to reset your password")
    ).toBeVisible();
    const yopmailPage = await context.newPage();
    await resetPassword(yopmailPage, "<EMAIL>", "Test@123");
    await page.getByRole("button", { name: "Return to Login" }).click();
  }
);
