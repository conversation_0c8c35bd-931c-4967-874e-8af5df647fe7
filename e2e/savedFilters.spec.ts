import { test, expect } from '@playwright/test';
import { loginasTestUser } from '../helpers/command';

test.describe.configure({ mode: "serial" });
const savedFilterName: string = 'savedFilter' + Math.floor(Math.random() * (1000000 - 0)).toString();

test('Search and Save Filters', {
  tag: '@e2e',
}, async ({page}) =>{
  await page.goto("/");
  await loginasTestUser(page, '<EMAIL>', 'Test@123');
  await page.getByText('Advanced').click();
  await page.getByText('Company', { exact: true }).click();
  await page.getByText('Company name').click();
  await page.getByRole('textbox', { name: 'Enter company name' }).fill('CY TRADING LTD');
  await page.getByRole('textbox', { name: 'Enter company name' }).press('Enter');
  await page.locator('div').filter({ hasText: /^Company name must contain CY TRADING LTD$/ }).first().click();
  await page.getByRole('button', { name: 'Done' }).click();
  await expect(page.getByText('1 entries')).toBeVisible();
  await page.getByRole('button', { name: 'Search' }).click();
  await page.getByRole('button', { name: 'Save filter' }).click();
  await page.getByRole('textbox', { name: 'Enter custom search filter' }).fill(savedFilterName);
  await page.getByRole('button', { name: 'Save Filter', exact: true }).click();
  await page.waitForResponse(response =>
    response.url().includes('/addSavedFiltersToAccount') && response.status() === 200
  );
});

test('Load Search from Saved Filters', {
  tag: '@e2e',
}, async ({page}) =>{
  await page.goto("/");
  await loginasTestUser(page, '<EMAIL>', 'Test@123');
  await page.waitForResponse(response =>
    response.request().method() === 'GET' && response.url().includes('/api/collections') && response.status() === 200
  );
  await page.getByText('Saved filters').click();
  if (!await page.getByText(savedFilterName).isVisible()) {
    await page.waitForTimeout(15000);
    await page.getByText('Campaign').click();
    await page.getByText('Saved filters').click();
  }
  const index = await page.locator('div.savedFilterRow').evaluateAll((elements, filterName) => {
    return elements.findIndex(el => el.textContent?.includes(filterName));
  }, savedFilterName);
  await page.locator(`div:nth-child(${index+1}) > .row > .sfrDots`).click();
  await page.getByText('Load search').click();
  await page.getByRole('button', { name: 'Search' }).click();
  await expect(page.getByText('1 companies selected')).toBeVisible();
  await expect(page.getByText('MOO FREE LTD')).toBeVisible();
});

test('Delete the saved filter', {
  tag: '@e2e',
}, async ({page}) => {
  await page.goto("/");
  await loginasTestUser(page, '<EMAIL>', 'Test@123');
  await page.getByText('Saved filters').click();
  if (!await page.getByText(savedFilterName).isVisible()) {
    await page.waitForTimeout(15000);
    await page.getByText('Campaign').click();
    await page.getByText('Saved filters').click();
  }
  const index = await page.locator('div.savedFilterRow').evaluateAll((elements, filterName) => {
    return elements.findIndex(el => el.textContent?.includes(filterName));
  },savedFilterName);
  await page.locator(`div:nth-child(${index+1}) > .row > .sfrDots`).click();
  await page.getByText('Delete').click({force:true});
  await page.waitForResponse(response =>
    response.request().method() === 'POST' && response.url().includes('/deleteSavedFiltersFromAccount') && response.status() === 200
  );
  await expect(page.locator('div.savedFilterRow').filter({hasText: savedFilterName})).not.toBeVisible();
});