import { test, expect } from '@playwright/test';
import { getAvailQuota, loginasAdminUser } from '../helpers/command';

test('Search User, Add and Remove quota', {
  tag: '@regression',
}, async ({page}) =>{
  await page.goto("/");
  await loginasAdminUser(page);
  await page.getByText('Admin').click();
  await page.getByRole('heading', { name: 'Admin Quota Management' }).click();
  await page.getByRole('textbox', { name: 'Search by UID or email address' }).fill('<EMAIL>');
  await page.getByRole('button', { name: 'Search' }).click();
  await expect(page.getByText('Madhumitha Palanisamy')).toBeVisible();
  await expect(page.getByText('<EMAIL>')).toBeVisible();
  await expect(page.getByText('162HLFpBa3c76LEvJMgp47bKBCx2')).toBeVisible();
  await expect(page.getByText('Search - Annually')).toBeVisible();
  await expect(page.getByText('active')).toBeVisible();
  await expect(page.getByText('Collection').nth(2)).toBeVisible();
  await expect(page.getByText('Letter Outreach').nth(2)).toBeVisible();
  await expect(page.getByText('Email Outreach').nth(2)).toBeVisible();
  let obj = await getAvailQuota(page);
  await expect(page.getByText(`Total Quota: ${obj.totalCollectionQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedCollectionQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingCollectionQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Total Quota: ${obj.totalLetterQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedLetterQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingLetterQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Total Quota: ${obj.totalEmailQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedEmailQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingEmailQuota}`).first()).toBeVisible();
  await page.getByRole('button').filter({ hasText: /^$/ }).nth(1).click();
  await page.getByRole('button').filter({ hasText: /^$/ }).nth(3).click();
  await page.getByRole('button').filter({ hasText: /^$/ }).nth(5).click();
  await page.getByRole('button', { name: 'Save Changes' }).click();
  await expect(page.getByText(`Total Quota: ${obj.totalCollectionQuota+100}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedCollectionQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingCollectionQuota+100}`).first()).toBeVisible();
  await expect(page.getByText(`Total Quota: ${obj.totalLetterQuota+100}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedLetterQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingLetterQuota+100}`).first()).toBeVisible();
  await expect(page.getByText(`Total Quota: ${obj.totalEmailQuota+100}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedEmailQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingEmailQuota+100}`).first()).toBeVisible();
  await page.getByRole('button').filter({ hasText: /^$/ }).first().click();
  await page.getByRole('button').filter({ hasText: /^$/ }).nth(2).click();
  await page.getByRole('button').filter({ hasText: /^$/ }).nth(4).click();
  await page.getByRole('button', { name: 'Save Changes' }).click();
  await expect(page.getByText(`Total Quota: ${obj.totalCollectionQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedCollectionQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingCollectionQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Total Quota: ${obj.totalLetterQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedLetterQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingLetterQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Total Quota: ${obj.totalEmailQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Used Quota: ${obj.usedEmailQuota}`).first()).toBeVisible();
  await expect(page.getByText(`Remaining Quota: ${obj.remainingEmailQuota}`).first()).toBeVisible();
});