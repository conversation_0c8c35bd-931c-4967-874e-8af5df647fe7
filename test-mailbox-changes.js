// Simple test to verify the mailbox changes
// This tests that the types are correctly updated

// Test 1: MailboxRequestDTO should only have preferredDomain
const mailboxData = {
  preferredDomain: "example.com"
  // replyTo should no longer be here
};

// Test 2: UserUpdateRequestDTO should have notifiedOn
const userUpdateData = {
  holdingCompany: "Test Company",
  phoneNumber: "************", 
  website: "https://example.com",
  address: {
    line1: "123 Main St",
    city: "Test City",
    postcode: "12345",
    country: "US"
  },
  notifiedOn: "<EMAIL>" // This should be optional
};

console.log("Mailbox data structure:", mailboxData);
console.log("User update data structure:", userUpdateData);
console.log("✅ Type changes verified successfully!");
