import { Page, BrowserContext, expect} from "@playwright/test";
const userName = 'qacypress'+Math.floor(Math.random() * (1000000 - 0)).toString()+'@yopmail.com';

export async function register(page: Page){
    await page.getByRole('button', { name: 'Login' }).click();
    await page.getByRole('textbox', { name: '<PERSON>' }).fill('Test User');
    await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill(userName);
    await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill('qaTestxZ!!234@25');
    await page.getByRole('button', { name: 'Register' }).click({force:true});
    await expect(page.getByText('Please verify your email')).toBeVisible();
}

export async function login(page: Page){
    await page.getByRole('button', { name: 'Login' }).click();
    await page.getByText('Sign in').click();
    await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill(userName);
    await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill('qaTestxZ!!234@25');
    await page.getByRole('button', { name: 'Sign in' }).click({force:true});
}

export async function verifyEmail(page: Page, context:BrowserContext){
    await page.goto('https://yopmail.com/en/');
    if(await page.getByRole('button', { name: 'Consent' }).isVisible()){
        await page.getByRole('button', { name: 'Consent' }).click()
    }
    await expect(page.getByPlaceholder('Enter your inbox here')).toBeVisible();
    await page.getByPlaceholder('Enter your inbox here').fill(userName, {force:true});
    await page.keyboard.press('Enter');
    await expect(page.locator('iframe[name="ifinbox"]').contentFrame().getByText('Verify Your Email').first()).toBeVisible();
    await page.locator('iframe[name="ifinbox"]').contentFrame().getByText('Verify Your Email').first().click({force:true});
    const iframeHandle = await page.locator('iframe[name="ifmail"]').elementHandle();
    if (!iframeHandle) throw new Error('iframe not found');
    const mailFrame = await iframeHandle.contentFrame();
    if (!mailFrame) throw new Error('Failed to resolve iframe content');
    await mailFrame.getByRole('link', { name: 'Verify' }).waitFor({state: "visible"});
    const pagePromise = context.waitForEvent('page');
    await mailFrame.getByRole('link', { name: 'Verify' }).click({ force: true });   
    const newPage = await pagePromise;
    await expect(newPage.getByRole('heading', { name: 'Site Not Found' })).toBeVisible();
}

export async function checkoutPlan(page:Page, planName:string){
    if(planName === 'SearchAnnual')
        await page.getByRole('button', { name: 'Prefer to skip the trial? Subscribe now' }).first().click();
    else if(planName === 'SearchAnnual-Trial')
        await page.getByRole('button', { name: 'Start Your 7 Day Free Trial' }).first().click();
    else if(planName === 'SearchMonthly'){
        await page.getByRole('tab', { name: 'Monthly billing' }).click();
        await page.getByRole('button', { name: 'Prefer to skip the trial? Subscribe now' }).first().click();
    }
    else if(planName === 'SearchMonthly-Trial'){
        await page.getByRole('tab', { name: 'Monthly billing' }).click();
        await page.getByRole('button', { name: 'Start Your 7 Day Free Trial' }).first().click();
    }
    else if(planName === 'SourceAnnual')
        await page.getByRole('button', { name: 'Prefer to skip the trial? Subscribe now' }).nth(1).click();
    else if(planName === 'SourceAnnual-Trial')
        await page.getByRole('button', { name: 'Start Your 7 Day Free Trial' }).nth(1).click();
    else if(planName === 'SourceMonthly'){
        await page.getByRole('tab', { name: 'Monthly billing' }).click();
        await page.getByRole('button', { name: 'Prefer to skip the trial? Subscribe now' }).nth(1).click();
    }
    else if(planName === 'SourceMonthly-Trial'){
        await page.getByRole('tab', { name: 'Monthly billing' }).click();
        await page.getByRole('button', { name: 'Start Your 7 Day Free Trial' }).nth(1).click();
    }
    await page.getByRole('textbox', { name: 'Card number' }).fill('4242 4242 4242 42424');
    await page.getByRole('textbox', { name: 'Expiration' }).fill('04 / 28');
    await page.getByRole('textbox', { name: 'CVC' }).fill('323');
    await page.getByRole('textbox', { name: 'Cardholder name' }).fill('Test User');
    await page.getByLabel('Country or region').selectOption('GB');
    await page.getByRole('textbox', { name: 'Postal code' }).fill('AWZ1AA');
    await page.getByRole('checkbox', { name: 'I’m purchasing as a business' }).check();
    await page.getByRole('textbox', { name: 'Business name' }).fill('Test');
    await page.getByRole('textbox', { name: 'GB123456789' }).fill('GB123456789');
    await page.locator('.CurrencyAmount').nth(3).waitFor({ state: 'visible' });
    await expect(page.locator('.CurrencyAmount').nth(3)).toHaveText('$50.00');
    await expect(page.getByTestId('hosted-payment-submit-button')).toBeEnabled();
    await page.getByTestId('hosted-payment-submit-button').click();
}

export async function initialOnboarding(page:Page){
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByRole('button', { name: 'Finish' }).click();
}

export async function finalOnboarding(page:Page){
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByRole('button', { name: 'Finish' }).click();
}

export async function validateEmailField(page:Page, email:string, error:string){
    await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill(email);
    await page.getByRole('button', { name: 'Register' }).click();
    await expect(page.getByText(error)).toBeVisible();
}

export async function emptyStateMenu(page:Page){
    await page.getByText('Collections').click();
    await expect(page.getByText('No collections found.')).toBeVisible();
    // await expect(page.getByText('Use search to find and collect interesting companies. You can then use your collection to run outreach campaigns.')).toBeVisible();
    // await page.getByRole('button', { name: 'Go to Search' }).click();
    // await expect(page.getByText('Discover Your Next Deal')).toBeVisible();
    await page.getByText('Campaigns', { exact: true }).click();
    await expect(page.getByText('No Collections yet')).toBeVisible();
    await expect(page.getByText('To run a campaign you need to save companies to a collection.')).toBeVisible();
    await page.getByRole('button', { name: 'Go to Search' }).click();
    await expect(page.getByText('Discover Your Next Deal')).toBeVisible();
    await page.getByText('Saved filters').click();
    await expect(page.getByText('No saved filters yet')).toBeVisible();
    await expect(page.getByText('Save filters first')).toBeVisible();
    await page.getByRole('button', { name: 'Go to Search' }).click();
    await expect(page.getByText('Discover Your Next Deal')).toBeVisible();
}

export async function collectionExistsEmptyCampaign(page:Page, planName:string){
    await page.getByText('Campaigns', { exact: true }).first().click();
    if(planName === 'Search'){
        await expect(page.getByText('Need to send a high volume of personalised, professional letters each month? '+
            'Boost your outreach with 500 letters per month on the Source plan, or buy a 100-letter Bolt-On to increase your quota.')).toBeVisible();
        await expect(page.getByRole('button', { name: 'Upgrade' })).toBeVisible();
    }else{
        await expect(page.getByText('Need to send a high volume of personalised, professional letters each month? '+
            'Boost your outreach with 500 letters per month on the Source plan, or buy a 100-letter Bolt-On to increase your quota.')).not.toBeVisible();
            await expect(page.getByRole('button', { name: 'Upgrade' })).not.toBeVisible();
    }
    await expect(page.getByText('No campaigns created yet')).toBeVisible();
    await expect(page.getByText('Start your campaign by choosing campaign type:')).toBeVisible();
    await expect(page.getByText('E-mail campaign')).toBeVisible();
    await expect(page.getByText('Letter campaign').first()).toBeVisible();
    await expect(page.getByText('Email & Letter campaign')).toBeVisible();
    await expect(page.getByRole('button', { name: 'New Campaign' })).toBeVisible();
}

export async function getFutureCancelYear(){
    const today = new Date();
    today.setFullYear(today.getFullYear() + 1); 
    const dd = String(today.getDate()).padStart(2, '0');
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const yyyy = today.getFullYear();
    return `${dd}/${mm}/${yyyy}`;
}

export async function getCurrentDate(){
    const today = new Date();
    const dd = String(today.getDate()).padStart(2, '0');
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const yyyy = today.getFullYear();
    return `${dd}/${mm}/${yyyy}`;
}

export async function getCurrentDealDate(){
    const today = new Date();
    const dd = String(today.getDate()).padStart(2, '0');
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const yyyy = today.getFullYear();
    return `${yyyy}-${mm}-${dd}`;
}

export async function loginasAdminUser(page:Page){
    await page.getByRole('button', { name: 'Login' }).click();
    await page.getByText('Sign in').click();
    await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill('<EMAIL>');
    await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill('p@ssw0rd!!');
    await page.getByRole('button', { name: 'Sign in' }).click({force:true});
}

export async function loginasTestUser(page:Page, email:string, password: string){
    await page.getByRole('button', { name: 'Login' }).click();
    await page.getByText('Sign in').click();
    await page.getByRole('textbox', { name: 'e.g. <EMAIL>' }).fill(email);
    await page.getByRole('textbox', { name: 'xxxxxxxx' }).fill(password);
    await page.getByRole('button', { name: 'Sign in' }).click({force:true});
}

export async function resetPassword(page:Page, email: string, password: string){
    await page.goto('https://yopmail.com/en/');
    if(await page.getByRole('button', { name: 'Consent' }).isVisible()){
        await page.getByRole('button', { name: 'Consent' }).click()
    }
    await expect(page.getByPlaceholder('Enter your inbox here')).toBeVisible();
    await page.getByPlaceholder('Enter your inbox here').fill(email, {force:true});
    await page.keyboard.press('Enter');
    await page.waitForTimeout(40000);
    await page.locator('button[id="refresh"]').click();
    await expect(page.locator('iframe[name="ifinbox"]').contentFrame().getByText('Reset your password').first()).toBeVisible();
    await page.locator('iframe[name="ifinbox"]').contentFrame().getByText('Reset your password').first().click({force:true});
    const pagePromise = page.waitForEvent('popup');
    await page.locator('iframe[name="ifmail"]').contentFrame().getByRole('link', { name: 'https://bizcrunch-test.' }).click();
    const newpage = await pagePromise;
    await newpage.getByRole('textbox').fill(password);
    await newpage.getByRole('button', { name: 'Save' }).click();
    await expect(newpage.getByText('Password changed')).toBeVisible();
    await newpage.close();
}

export async function getAvailQuota(page:Page){
    const totalQuotaFirst = await page.locator('span').filter({ hasText: /^Total Quota: \d+$/ }).first().textContent();
    const usedQuotaFirst = await page.locator('span').filter({ hasText: /^Used Quota: \d+$/ }).first().textContent();
    const remainingQuotaFirst = await page.locator('span').filter({ hasText: /^Remaining Quota: \d+$/ }).first().textContent();
    const totalQuotaSecond = await page.locator('span').filter({ hasText: /^Total Quota: \d+$/ }).nth(1).textContent();
    const usedQuotaSecond = await page.locator('span').filter({ hasText: /^Used Quota: \d+$/ }).nth(1).textContent();
    const remainingQuotaSecond = await page.locator('span').filter({ hasText: /^Remaining Quota: \d+$/ }).nth(1).textContent();
    const totalQuotaThird = await page.locator('span').filter({ hasText: /^Total Quota: \d+$/ }).nth(2).textContent();
    const usedQuotaThird = await page.locator('span').filter({ hasText: /^Used Quota: \d+$/ }).nth(2).textContent();
    const remainingQuotaThird = await page.locator('span').filter({ hasText: /^Remaining Quota: \d+$/ }).nth(2).textContent();
    const totalQuotaFirstMatch = totalQuotaFirst?.match(/Total Quota: (\d+)/);
    const usedQuotaFirstMatch = usedQuotaFirst?.match(/Used Quota: (\d+)/);
    const remainingQuotaFirstMatch = remainingQuotaFirst?.match(/Remaining Quota: (\d+)/);
    const totalQuotaSecondMatch = totalQuotaSecond?.match(/Total Quota: (\d+)/);
    const usedQuotaSecondMatch = usedQuotaSecond?.match(/Used Quota: (\d+)/);
    const remainingQuotaSecondMatch = remainingQuotaSecond?.match(/Remaining Quota: (\d+)/);
    const totalQuotaThirdMatch = totalQuotaThird?.match(/Total Quota: (\d+)/);
    const usedQuotaThirdMatch = usedQuotaThird?.match(/Used Quota: (\d+)/);
    const remainingQuotaThirdMatch = remainingQuotaThird?.match(/Remaining Quota: (\d+)/);
    let [totalCollectionQuota, totalEmailQuota, totalLetterQuota, usedCollectionQuota, usedLetterQuota,
        usedEmailQuota, remainingCollectionQuota, remainingLetterQuota, remainingEmailQuota]: number[] = [0, 0, 0, 0, 0, 0, 0];
    if(totalQuotaFirstMatch && usedQuotaFirstMatch && remainingQuotaFirstMatch && totalQuotaSecondMatch && usedQuotaSecondMatch
        && remainingQuotaSecondMatch && totalQuotaThirdMatch && usedQuotaThirdMatch && remainingQuotaThirdMatch){
        totalCollectionQuota = parseInt(totalQuotaFirstMatch[1], 10);
        totalLetterQuota = parseInt(totalQuotaSecondMatch[1], 10);
        totalEmailQuota = parseInt(totalQuotaThirdMatch[1], 10);
        usedCollectionQuota = parseInt(usedQuotaFirstMatch[1], 10);
        usedLetterQuota = parseInt(usedQuotaSecondMatch[1], 10);
        usedEmailQuota = parseInt(usedQuotaThirdMatch[1], 10);
        remainingCollectionQuota = parseInt(remainingQuotaFirstMatch[1], 10);
        remainingLetterQuota = parseInt(remainingQuotaSecondMatch[1], 10);
        remainingEmailQuota = parseInt(remainingQuotaThirdMatch[1], 10);
    } 
    return {totalCollectionQuota, totalLetterQuota, totalEmailQuota, usedCollectionQuota, usedLetterQuota,
        usedEmailQuota, remainingCollectionQuota, remainingLetterQuota, remainingEmailQuota} 
}

export async function getUserQuota(page:Page){
    const collectionQuota = await page.locator('span').filter({ hasText: /^Collection : (\d+)\/(\d+)$/ }).first().textContent();
    const letterQuota = await page.locator('span').filter({ hasText: /^Letter Outreach : (\d+)\/(\d+)$/ }).first().textContent();
    const emailQuota = await page.locator('span').filter({ hasText: /^Email Outreach : (\d+)\/(\d+)$/ }).first().textContent();
    const collectionQuotaMatch = collectionQuota?.match(/Collection : (\d+)\/(\d+)/);
    const letterQuotaMatch = letterQuota?.match(/Letter Outreach : (\d+)\/(\d+)/);
    const emailQuotaMatch = emailQuota?.match(/Email Outreach : (\d+)\/(\d+)/);
    let [usedCollectionQuota, totalCollectionQuota, usedLetterQuota, 
        totalEmailQuota, totalLetterQuota, usedEmailQuota]: number[] = [0, 0, 0, 0, 0, 0];
    if(collectionQuotaMatch && letterQuotaMatch && emailQuotaMatch){
        usedCollectionQuota = parseInt(collectionQuotaMatch[1], 10);
        totalCollectionQuota = parseInt(collectionQuotaMatch[2], 10);
        usedLetterQuota = parseInt(letterQuotaMatch[1], 10);
        totalLetterQuota = parseInt(letterQuotaMatch[2], 10);
        usedEmailQuota = parseInt(emailQuotaMatch[1], 10);
        totalEmailQuota = parseInt(emailQuotaMatch[2], 10);
    }
    return {usedCollectionQuota, totalCollectionQuota, usedLetterQuota, totalLetterQuota, totalEmailQuota, usedEmailQuota}
}

export async function getCompaniesCount(page:Page){
    const company = await page.locator('span').filter({ hasText: /^(\d{1},\d{3}|\d{1,3}) entries$/ }).first().textContent();
    const companyMatch = company?.match(/(\d{1},\d{3}|\d{1,3}) entries/);
    let companyCount = 0;
    if(companyMatch){
        const companyCorrectedMatch = companyMatch.map(num => num.replace(/,/g, ''));
        companyCount = parseInt(companyCorrectedMatch[1], 10);
    }
    return {companyCount}
}

export async function getselectedCompaniesCount(page:Page){
    const company = await page.locator('span').filter({ hasText: /^(\d+) companies selected$/ }).first().textContent();
    const companyMatch = company?.match(/(\d+) companies selected/);
    let companyRetrievedCount = 0;
    if(companyMatch){
        const companyCorrectedMatch = companyMatch.map(num => num.replace(/,/g, ''));
        companyRetrievedCount = parseInt(companyCorrectedMatch[1], 10);
    }
    return {companyRetrievedCount}
}

module.exports = {
    register,
    login,
    verifyEmail,
    checkoutPlan,
    initialOnboarding,
    finalOnboarding,
    validateEmailField,
    emptyStateMenu,
    collectionExistsEmptyCampaign,
    getFutureCancelYear,
    loginasAdminUser,
    loginasTestUser,
    resetPassword,
    getAvailQuota,
    getCurrentDate,
    getCurrentDealDate,
    getUserQuota,
    getCompaniesCount,
    getselectedCompaniesCount
}