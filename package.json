{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@dnd-kit/accessibility": "^3.1.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.1.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.5.0", "@shadcn/ui": "^0.0.4", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.53.0", "@tanstack/react-table": "^8.20.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@untitled-ui/icons-react": "^0.1.1", "axios": "^1.4.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.5", "firebase": "^11.0.2", "firebase-tools": "^13.34.0", "fs": "0.0.1-security", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "lucide-react": "^0.469.0", "mixpanel-browser": "^2.59.0", "moment": "^2.29.4", "nth-check": "^2.1.1", "quill": "^2.0.3", "react": "^18.2.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-loading-skeleton": "^3.3.1", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-router-dom": "^6.11.1", "react-scripts": "^5.0.1", "react-spinners": "^0.13.8", "react-toastify": "^11.0.5", "react-webview": "^0.1.0", "redux-persist": "^6.0.0", "sanitize-html": "^2.14.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "untitledui-js": "^2.0.0", "web-vitals": "^2.1.4", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "yup": "^1.6.1"}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.38"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@playwright/test": "^1.52.0", "@types/canvas-confetti": "^1.9.0", "@types/mixpanel-browser": "^2.50.2", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-csv": "^1.1.10", "@types/sanitize-html": "^2.13.0", "tailwindcss": "^3.3.3", "typescript": "^4.5.4"}}