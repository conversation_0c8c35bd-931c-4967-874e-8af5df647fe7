import "./app.css";
import {
  BrowserRouter as Router,
  Route,
  Routes,
  Navigate,
} from "react-router-dom";
import { useEffect, useState, useContext } from "react";
import { getIdToken, onAuthStateChanged } from "firebase/auth";
import { auth, db } from ".";
import { Mixpanel } from "./components/mixpanel/mixpanel";
import Header from "./components/header";
import Payments from "./screens/Payments";
import Login from "./screens/Login";
import Search from "./screens/Search";
import axios from "axios";
import SavedFilters from "./screens/SavedFilters";
import Billing from "./components/billing/billing";
import SharedExport from "./screens/SharedExport";
import SuccessToast from "./components/popUps/SuccessToast";
import ForgotPassword from "./screens/ForgotPassword";
import VerifyEmail from "./screens/VerifyEmail";
import { themeSetter } from "./localFunctions/themeSetter";
import { ParentContext } from "./components/constants/ParentContext";
import VerifyEmailSuccess from "./screens/VerifyEmailSuccess";
import PoweredByBC from "./components/poweredByBC";
import AIDataScreen from "./screens/AIData";
import ProtectedRoute from "protectedRoute";
import axiosWithToken from "axiosWithToken";
import CollectionDetails from "components/collection/collectionDetails";
import Loader from "components/common/loader";
import { useDispatch } from "react-redux";
import { setCollections } from "components/collection/collectionSlice";
import CampaignLayout from "./components/campaign/campaign-layout/campaignLayout";
import CampaignDetails from "components/campaign/campaign-details/campaignDetails";
import Admin from "components/admin/admin";
import { calendalyInvite, showCampaignFeature, showNewPaymentFlow } from "components/utils/network/endpoints";
import { createCheckoutSession, getCurrentSubscription, getNewPlans } from "components/subscription/services";
import { setProductPlan, updateCurrentSubscription } from "components/subscription/subscriptionSlice";
import { setDomainSettings } from "components/campaign/campaignSlice";
import { collection, doc, onSnapshot } from "firebase/firestore";
import { FIRESTORE_USER_SUB_COLLECTIONS, FirestoreCollections } from "types";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import ShowQuotaAdditionInProgress from "components/popUps/showQuotaAdditionInProgress";
import CreateSubscription from "components/subscription/create-subscription/createSubscription";
import CampaignLanding from "components/campaign/campaign-landing/campaignLanding";
import CampaignList from "components/campaign/campaign-list/campaignList";
import DealTracker from "components/deal/dealTracker";
import CollectionList from "components/collection/collection-list/collectionList";

function App() {
  const baseURL2 = process.env.REACT_APP_BASEURL2;
  const [loggedIn, setLoggedIn] = useState(false);
  const [userData, setUserData] = useState<any>(null);
  const [credits, setCredits] = useState<any>(null);
  const [showSuccessToast, setShowSuccessToast] = useState<boolean>();
  const [succesText, setSuccessText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const context = useContext<any>(ParentContext);

  const [newPlans, setNewPlans] = useState<any>(null);
  const [createNewSubscription, setCreateNewSubscription] = useState(false);

  const [isBiz, setIsBiz] = useState(
    context.isBiz4Biz || context.isHarbour ? false : true
  );

  const [isHarbourClub, setIsHarbourClub] = useState(
    context.isHarbour ? true : false
  );
  const [tutorialStep, setTutorialStep] = useState("");

  const [isBiz4Biz, setIsBiz4Biz] = useState(context.isBiz4Biz ? true : false);

  const dispatch = useDispatch();

  let params = new URL(document.location.href).searchParams;
  let utm_source = params.get("utm_source");
  if (utm_source !== null) {
    Mixpanel.track("RepeatLogin", {
      "User ID": auth.currentUser ? auth.currentUser.uid : null,
      Date: new Date().toISOString(),
      $email: auth?.currentUser?.email,
      $name: auth?.currentUser?.displayName,
      //"Subscription": userData?.plan,
      "User Type": "Buyer",
    });
  }
  let lastUpdatedQuota = localStorage.getItem("lastUpdatedQuota") || null;

  themeSetter(isBiz); // run themeSetter function to set primary colors

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        const idToken = await getIdToken(user!);
        localStorage.setItem("idToken", idToken);

        setLoggedIn(true);
        let cr = localStorage.getItem("currentlyRegistering");
        if (cr !== null) {
          localStorage.removeItem("currentlyRegistering");
          //setUserData(null);
        } else {
          loadReducedUserFromDB();
        }

        checkSession(user.uid);

        let currentTime = new Date().getTime();
        if (auth.currentUser && auth.currentUser.metadata.creationTime) {
          let creationTime = new Date(
            auth.currentUser.metadata.creationTime
          ).getTime();
          if (currentTime - creationTime > 300000) {
            //It's been over 5 minutes since account was created
            let userData = JSON.parse(localStorage.getItem("user")!);

            Mixpanel.track("RepeatLogin", {
              $name: userData?.name,
              $email: userData?.email,
              "User Type": "Buyer",
              "User ID": userData?.uid,
              Date: new Date().toISOString(),
            });
          }
        }
      } else {
        setLoggedIn(false);
        setUserData(null);
        if (!isBiz && window.location.pathname !== "/login") {
          window.location.pathname = "/login";
        }
      }
    });
    return () => {
      Mixpanel.reset();
      unsubscribe();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const getTokenOnAppRefresh = async () => {
      const user = auth.currentUser;
      if (user) {
        try {
          const idToken = await getIdToken(user, true);

          localStorage.setItem("idToken", idToken);
        } catch (error) {
          console.error("Error getting token:", error);
        }
      } else {
        console.log("User is not authenticated");
      }
    };
    getTokenOnAppRefresh();
  }, []);

  useEffect(() => {
    const refreshToken = async () => {
      const user = auth.currentUser;

      if (user) {
        try {
          const idToken = await getIdToken(user, true);

          localStorage.setItem("idToken", idToken);
          console.log("Token refreshed");
        } catch (error) {
          console.error("Error getting token:", error);
        }
      } else {
        console.log("User is not authenticated");
      }
    };

    const tokenRefreshTimeout = setInterval(() => {
      refreshToken();
    }, 55 * 60 * 1000);

    return () => {
      clearInterval(tokenRefreshTimeout);
    };
  }, []);


  const fetchPlans = async () => {
    if (showNewPaymentFlow) {
      try {
        setIsLoading(true);
        const newPlans = await getNewPlans();
        setNewPlans(newPlans);
        dispatch(setProductPlan(newPlans));
        setIsLoading(false);
      } catch (error) {
        console.error("Error Fetching plans:", error);
        setIsLoading(false);
      }
    }
  };

  const fetchCurrentSubscription = async (showloader?: boolean) => {
    if (showNewPaymentFlow) {
      try {
        if (showloader) setIsLoading(true);
        const currentSubscription = await getCurrentSubscription();
        dispatch(updateCurrentSubscription(currentSubscription));
        setCreateNewSubscription(false);
        setIsLoading(false);
      } catch (error: any) {
        console.error("Error Fetching Subscriptions:", error);
        setIsLoading(false);

        const urlParams = new URLSearchParams(window.location.search);
        const redirectFromCheckout = urlParams.get("redirectFromCheckout") === "true";
        if (error.status === 404 && !redirectFromCheckout) {
          setCreateNewSubscription(true);
        }
      }
    }
  };

  const checkSession = (uid: any) => {
    axios
      .get(baseURL2 + "getUserSession", {
        params: {
          uid: uid,
        },
      })
      .then((response) => {
        let lastSession = response.data.lastSession;
        if (lastSession) {
          let time = new Date().getTime();

          if (time - lastSession > 30 * 86400000) {
            setSessionCIO(uid);
          }
        }
        setSession(uid);
      });
  };

  const setSession = (uid: any) => {
    axios
      .get(baseURL2 + "setUserSession", {
        params: {
          uid: uid,
        },
      })
      .then((response) => { });
  };

  const setSessionCIO = (uid: any) => {
    axios
      .get(baseURL2 + "userSessionCIO", {
        params: {
          uid: uid,
        },
      })
      .then((response) => { });
  };

  const loadReducedUserFromDB = async () => {
    const idToken = localStorage.getItem("idToken");
    if (idToken) {
      try {
        setIsLoading(true);
        const response = await axiosWithToken.get(`${baseURL2}api/auth/user`);
        const data = response.data;

        if (!data.error) {
          Mixpanel.identify(data.uid);
          Mixpanel.people.set({
            $name: data.name,
            $email: data.email,
            plan: data.plan,
          });

          setUserData(data);
          localStorage.setItem("user", JSON.stringify(data));

          // let stripeCustomerID = data.stripeCustomerID;
          // if (stripeCustomerID) {
          //   getCustomerData();
          // }
          await loadUserFromDB();
        }
      } catch (error) {
        console.error("Error loading reduced user:", error);
        setIsLoading(false);
      }
    }
  };

  const loadUserFromDB = async () => {
    const idToken = localStorage.getItem("idToken");
    if (idToken) {
      try {
        setIsLoading(true);
        const response = await axiosWithToken.get(
          `${baseURL2}api/auth/account-dashboard-details`
        );
        const data = response.data;

        if (!data.error) {
          setUserData(data);
          setIsBiz(!data.isHarbourClub && !data.isBiz4Biz);
          if (!isHarbourClub) {
            setIsHarbourClub(data.isHarbourClub);
          }
          if (!isBiz4Biz) {
            setIsBiz4Biz(data.isBiz4Biz);
          }

          localStorage.setItem("user", JSON.stringify(data));
          fetchPlans();

          if (showCampaignFeature) {
            getDomainSettings();
          }
          if (auth.currentUser?.emailVerified) {
            fetchCurrentSubscription(true);
          }
          //getCustomerData();
          const collectionsResponse = await axiosWithToken.get(
            baseURL2 + "api/collections"
          );
          const collectionsData = collectionsResponse.data;

          // if (showNewPaymentFlow && !currentSubscription && auth.currentUser?.emailVerified) {
          //   setCreateNewSubscription(true);
          // }

          dispatch(setCollections(collectionsData));
        } else {
          console.error("Error loading account dashboard details:", data.error);
        }
      } catch (error) {
        console.error("Error loading account dashboard details:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getDomainSettings = async () => {
    if (auth.currentUser?.emailVerified) {
      setIsLoading(true);
      try {
        const response = await axiosWithToken.get(
          `${baseURL2}api/user/domain-settings`
        );
        dispatch(setDomainSettings(response.data));
        localStorage.setItem("domainSettings", JSON.stringify(response.data));
        setIsLoading(false);

      } catch (error) {
        setIsLoading(false);
        console.error("Error fetching customer data:", error);
      }
    }
  };

  const successClicked = async (text: string) => {
    setSuccessText(text);
    setShowSuccessToast(true);
    await delay(5000);
    setShowSuccessToast(false);
  };

  const delay = (ms: any) => new Promise((res) => setTimeout(res, ms));


  const handleSubscriptionChoosen = async (plan: any, isFreeTrial: boolean) => {
    setIsLoading(false);
    const mixpanelProps = {
      $name: `${userData.name}`,
      //$distinct_id: userData.uid,
      $email: userData.email,
    }
    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        ...(!isFreeTrial && { GetStarted: `${plan.name} - ${plan.pricingInterval}` }),
        ...(isFreeTrial && { Start7DayFreeTrial: `${plan.name} - ${plan.pricingInterval}` }),
      },
      id: userData?.uid.toString(),
      eventName: isFreeTrial
        ? MixpanelEventName.userSelects7DayFreeTrial
        : MixpanelEventName.preferToSkipTrial,
    });
    const checkout = await createCheckoutSession(plan.productPricingId, isFreeTrial);
    if (checkout) {
      window.location.href = checkout.url;
      setIsLoading(false);
    }
  }

  const handleCalendlyInvite = () => {
    const mixpanelProps = {
      $name: `${userData.name}`,
      $email: userData.email,
    }
    mixpanelCustomEvent({
      mixpanelProps: mixpanelProps,
      id: userData?.uid.toString(),
      eventName: MixpanelEventName.userClickedOnGetEarlyAccess
    })
    window.location.href = calendalyInvite;
  }
  useEffect(() => {
    const domainSettingsRef = auth?.currentUser?.uid
      ? collection(
        db,
        FirestoreCollections.users,
        auth.currentUser.uid,
        FIRESTORE_USER_SUB_COLLECTIONS.domainSettings
      )
      : null;

    const unsubscribe = domainSettingsRef
      ? onSnapshot(domainSettingsRef, () => {
        getDomainSettings();
      })
      : () => { };

    return () => unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [auth?.currentUser?.uid]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const redirectFromCheckout =
      urlParams.get("redirectFromCheckout") === "true";

    if (redirectFromCheckout) {
      setShowProcessingModal(true);
    }
    const userRef = auth?.currentUser?.uid
      ? doc(db, FirestoreCollections.users, auth.currentUser.uid)
      : null;

    const unsubscribe = userRef
      ? onSnapshot(userRef, (docSnapshot: any) => {
        if (docSnapshot.exists()) {
          const userData = docSnapshot.data();

          localStorage.setItem(
            "lastUpdatedQuota",
            userData.quotaUpdatedAt?.seconds
          );

          if (!lastUpdatedQuota || lastUpdatedQuota === undefined || lastUpdatedQuota === "undefined") {
            // eslint-disable-next-line react-hooks/exhaustive-deps
            lastUpdatedQuota = userData.quotaUpdatedAt?.seconds;
          }

          if (
            auth?.currentUser?.emailVerified &&
            lastUpdatedQuota &&
            Number(lastUpdatedQuota) <= userData.quotaUpdatedAt.seconds
          ) {
            fetchCurrentSubscription(false);

            mixpanelCustomEvent({
              mixpanelProps: {},
              id: "",
              eventName: MixpanelEventName.snapshotUpdated,
            });

            if (redirectFromCheckout) {
              setShowProcessingModal(false);
              const newUrl = window.location.pathname;
              window.location.href = newUrl;
            }
          }
        }
      })
      : () => { };

    return () => unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [auth?.currentUser?.uid]);

  return (
    <div className="App">
      {isLoading && !showProcessingModal && <Loader />}
      {
        showProcessingModal && (
          <ShowQuotaAdditionInProgress isOpen={showProcessingModal} />
        )
      }
      <ParentContext.Provider value={{ isHarbour: isHarbourClub, isBiz4Biz, tutorialStep, setTutorialStep }}>
        <Router>
          {
            createNewSubscription &&
            <CreateSubscription
              plansData={newPlans}
              open={createNewSubscription}
              purchasePlan={(plan: any, isFreeTrial) => handleSubscriptionChoosen(plan, isFreeTrial)}
              handleCalendlyInvite={handleCalendlyInvite}
              close={() => setCreateNewSubscription(false)} />
          }
          {window.location.pathname !== "/iframe" && (
            <Header
              loggedIn={loggedIn}
              user={userData}
            />
          )}

          <Routes>
            <Route
              path="/"
              element={
                <Search
                  user={userData}
                  creditsP={credits}
                  updateCredits={(x: any) => setCredits(x)}
                  saveToast={successClicked}
                />
              }
            />

            <Route
              path="/login"
              element={
                loggedIn ? (
                  <Navigate to="/search" />
                ) : (
                  <Login
                    setUser={(x: any) => setUserData(x)}
                  />
                )
              }
            />
            <Route
              path="/billing"
              element={
                <ProtectedRoute
                  loggedIn={loggedIn}
                  element={
                    <Billing
                      userP={userData}
                    />
                  }
                />
              }
            />

            <Route
              path="/collectionDetail"
              element={
                <ProtectedRoute
                  loggedIn={loggedIn}
                  element={<CollectionDetails />}
                />
              }
            />

            <Route path="/payments" element={<Payments />} />

            {/* <Route
              path="/account"
              element={
                <ProtectedRoute loggedIn={loggedIn} element={<Account />} />
              }
            /> */}
            <Route
              path="/search"
              element={
                <Search
                  user={userData}
                  creditsP={credits}
                  updateCredits={(x: any) => setCredits(x)}
                  saveToast={successClicked}
                />
              }
            />
            <Route
              path="/search/newUser"
              element={
                <Search
                  user={userData}
                  creditsP={credits}
                  updateCredits={(x: any) => setCredits(x)}
                  saveToast={successClicked}
                />
              }
            />
            <Route path="/aidata" element={<AIDataScreen />} />
            <Route
              path="/savedFilters"
              element={
                <ProtectedRoute
                  loggedIn={loggedIn}
                  element={
                    <SavedFilters
                      isFilters={true}
                      isCollection={false}
                      hasData={userData && userData.savedFilters}
                    />
                  }
                />
              }
            />
            <Route
              path="/collections"
              element={
                <ProtectedRoute
                  loggedIn={loggedIn}
                  element={
                    <CollectionList />
                  }
                />
              }
            />
            <Route
              path="/campaignLanding"
              element={
                <ProtectedRoute
                  loggedIn={loggedIn}
                  element={<CampaignLanding />}
                />
              }
            />
            <Route
              path="/campaignDetails"
              element={
                <ProtectedRoute
                  loggedIn={loggedIn}
                  element={<CampaignDetails />}
                />
              }
            />
            <Route
              path="/campaign"
              element={
                <ProtectedRoute loggedIn={loggedIn} element={<CampaignList />} />
              }
            />
            <Route
              path="/campaignFlow"
              element={
                <ProtectedRoute
                  loggedIn={loggedIn}
                  element={<CampaignLayout />}
                />
              }
            />
            <Route
              path="/deals"
              element={
                <ProtectedRoute loggedIn={loggedIn} element={<DealTracker />} />
              }
            />
            <Route
              path="/sharedExport/:user/:exportID"
              element={<SharedExport />}
            />
            <Route
              path="/admin"
              element={
                <ProtectedRoute loggedIn={loggedIn} element={<Admin />} />
              }
            />
            <Route path="/verify-email" element={<VerifyEmail />} />
            <Route
              path="/verify-email-success"
              element={<VerifyEmailSuccess />}
            />
            <Route path="/passwordReset" element={<ForgotPassword />} />
            {/* <Route
              path="/iframe"
              element={<PricingIFrame userP={userData} />}
            /> */}
          </Routes>
        </Router>

        {showSuccessToast && <SuccessToast text={succesText} />}

        {!isBiz && <PoweredByBC />}

        {window.location.pathname !== "/iframe" && (
          <img
            className="bottomGraphic"
            src={
              isBiz
                ? "/assets/bottomGraphic.png"
                : "/assets/bottomGraphicHC.png" // TODO: different graphic for biz4biz?
            }
            alt="bottomGraphic"
          />
        )}
      </ParentContext.Provider>
    </div>
  );
}

export default App;
