const campaign = {
  campaigns: "Campaigns",
  noCampaignsHeading: "No campaigns created yet",
  campaignOptionText: "Start your campaign by choosing campaign type:",
  campaignOptionType: [
    {
      id: 1,
      label: "E-mail campaign ",
      disabled: false,
      subText: "Most Likely to Receive a Reply",
    },
    {
      id: 2,
      label: "Letter campaign",
      disabled: false,
      subText: "Moderate Chance of a Reply",
    },
    {
      id: 3,
      label: "Email & Letter campaign",
      disabled: false,
      subText: "Least Likely to Receive a Reply",
    },
  ],
  newCampaign: "New Campaign",
  noCollectionsYet: "No Collections yet",
  noCollectionsSubText:
    "To run a campaign you need to save companies to a collection. ",
  goToSearch: "Go to Search",
  startCampaign: "Start Campaign",
  next: "Next",
  saveAndProceed: "Save & Proceed",
  saveAndExit: "Save & Exit",
  exitWithoutSaving: "Exit without saving",
  back: "Back",
  createCampaignSubtext: "Create new campaign template",
  edit: "Edit",
  reviewAndRefine: "Review & Refine",
  selectCollection: "Select Collections for Campaign",
  labelForCompanyNAme:
    "What's the name of your Hold Co, Fund or Acquisition Vehicle?",
  phoneNumber: "Phone Number",
  website: "Your Current Main Website",
  headsUp: "Heads up!",
  mailboxAlertDescription:
    "To prevent your mailbox from being flagged as spam, we limit sending to 30 emails per day per email address.",
  mailboxes: "Mailboxes",
  labelPreferredDomain: "What's your preferred domain identity to send emails?",
  websiteDomain: "Enter your preferred domain identity e.g. bizcrunch.co",
  allowedTopLevelDomains: " Allowed top-level domains (TLD):",
  allowedTlds: " .co.uk, .com, .net, .co, .uk",
  registerNewDomainHelperText:
    "We will register a new domain for your campaign, from available similar options",
  burnerDomain:
    "We have detected the following burner domains for your outreach:",
  outreachMaiboxesHeading:
    "We have detected the following mailboxes for your outreach:",
  mailboxBurnerDomainHeading:
    "New mailboxes with burner domains help isolate outreach campaigns and manage spam risks effectively",
  repliesToEmail: "Forward email",
  repliesToEmailSubtextTooltip:
    "Replies from your campaigns will be forwarded to email address in your BizCrunch profile.",
  campaignSummary: "Campaign summary",
  campaignSummarySubtext: "Flexible pricing that grows with you.",
  aboutCredits: "About credits",
  warmingUp: "Warming Up",
  warmingUpSubtext:
    "It will take few days to warmup your campaign inbox. Campaign will be sent automatically out as soon as it's ready.",
  emailsToSend: "Emails to send",
  creditsBalance: "Credits balance",
  editNameHeader: "Name your campaign",
  cancel: "Cancel",
  save: "Save",
  doYouOwnTheDomain: "Do you own this domain?",
  domainError: "Please enter a valid domain (e.g. bizcrunch.co)",
  discardModalHeader: "Are you sure you want to leave?",
  discradModalSubtext: "Campaign progress will be saved as draft",
  reviewEmail: "Review Email",
  copy: "Copy",
  close: "Close",
  parameters: "Parameters",
  variables: [
    "{{firstName}} ",
    "{{lastName}} ",
    "{{companyName}} ",
    "{{sender.firstName}} ",
    "{{sender.lastName}} ",
    "{{sender.company}} ",
    "{{sender.email}} ",
    "{{sender.website}} ",
    "{{sender.phoneNumber}} ",
  ],
  errorMsgCampaignNameExists:
    "Campaign with this name already exists. Please try another name.",
  trialUserBannerText:
    "You’re in your 7-day free trial, with limited quotas for Collecting companies and Emailing recipients.   Unlock your full quota by activating your subscription now.",
  unlockFullQouta: "Unlock Full Quota",
  upgrade: "Upgrade",
  upgradeAccount: "Upgrade Account",
  campaignSuccessBannerHeader: "We have successfully created campaign",
  campaignSuccessBannerSubtext:
    "You can view your newly launched campaign in the campaign list.",
  campaignErrorBannerHeader:
    "We have encountered an error while starting your campaign",
  campaignErrorBannerSubtext:
    "but don't worry, your campaign have been saved as draft.",
  notifyViaEmail: "We will notify via email on the status change",
  toPreventSpam: "To prevent your mailbox from being flagged as spam",
  companiesSelected: "Companies selected",
  totalNoOfRecipents: "Total no. recipients",
  totalNoOfLetterRecipients: "Total no. letter recipients",
  totalNoOfEmailRecipients: "Total no. email recipients",
  quotaUsage: "Quota usage",
  emailQoutaUsage: "Email quota usage",
  letterQoutaUsage: "Letter quota usage",
  emailsPerWorkflow: "Emails per workflow",
  recipients: "Recipients",
  totalEmailSent: "Total E-mails Sent",
  warmingUpCampaign: "Warming Up!",
  warmingUpSubText:
    "It will take few days to warmup your campaign inbox. We will notify you via email of progress",
  endTrial: "End Trial",
  noOfEmailRecipients: "No. of email recipients",
  noOfLetterRecipients: "No. of letter recipients",
  noOfRecipients: "No. of recipients",
  recipientQuota: "Recipient quota",
  emailRecipientQuota: "Email recipient quota",
  letterRecipientQuota: "Letter recipient quota",
  email: "Email",
  letter: "Letter",
  collectionsSelected: "Collections selected",
  subtextForTrialUserEmailSelection: `Your trial mode currently allows you to send emails to only the first 25 recipients. To activate your subscription now, click on "Unlock Full Quota"`,
  subtextForTrialUserLetterSelection: `Your trial mode currently allows you to send letters to only the first 5 recipients. To activate your subscription now, click on "Unlock Full Quota"`,
  subtextForTrialUserCombinedSelection: `Your trial mode currently allows you to send emails to only the first 25 recipients and letters to only the first 5 recipients. To activate your subscription now, click on "Unlock Full Quota"`,
  domainRegistrationInProgress:
    "The domain and mailbox setup are in progress. You can start other campaigns once the setup is ready. ",
  domainRegistrationInProgressForEmailCampaign:
    "We're currently setting up your domain and mailbox. You'll be able to launch additional email campaigns once that's done. Meanwhile, you can launch letter campaigns right away!",
  letterOutreachSubtextSummary:
    "Your letters will be prepared and sent within approximately 5 days. We'll update your campaign status as soon as the letters are on their way.",
  endTrialSucessBannerHeader: "Subscription activated, it’s time to scale!	",
  endTrialErrorBannerSubtext:
    "More collections, more email sends, more insights - let’s grow.",
  promoteSourcePlan: `Need to send a high volume of personalised, professional letters each month? Boost your outreach with 500 letters per month on the Source plan, or buy a 100-letter Bolt-On to increase your quota.`,
  letterSubText: [
    "You can write your letter body here, comprised of 4 paragraphs to fit a single-side A4 template.",
    "Each paragraph can contain 300 typed characters, plus Parameters.",
    "Each Parameter can only be used once in the letter body.",
    `Allowed special characters: .,!?'"-(){}#@$%&*+=/_:;`,
  ],
  preview: "Preview",
  paragraph: "Paragraph",
  maxCharacterLimit: "/300 characters",
  letterPreview: "Letter Preview",
  lockedCollectionError:
    "Some of the selected collections are locked and cannot be added to the campaign. Please update your selection.",
  totalLetterSent: "Total Letters Sent",
  quotaIncreasedSucesfully: "Quota increased successfully",
  subscriptionActivated: "Your Subscription has been activated",
  subscriptionActivatedSubtext:
    "Your campaign has been saved as a draft. To finish setting it up, click the draft campaign below.",
  activateYourSubscription: "- activate your subscription",
  noRecipientSelected: "No recipient selected",
  zeroRecipientWarning:
    "Your selection has zero recipient count. Please select collections that have at least one recipient.",
};

const admin = {
  tabOptions: [
    // { value: "profile", label: "User Profile" },
    // { value: "domains", label: "Burner Domains" },
    { value: "userQuota", label: "User Quota Management" },
    { value: "aiData", label: "AI Data" },
    { value: "apiKeys", label: "API Keys" },
  ],
  userQuota: "userQuota",
  burnerDomains: "Burner Domains",
  profile: "profile",
  dkim: "DKIM",
  cancel: "Cancel",
  save: "Save",
  enterKey: "Enter Key :",
  enterValue: "Enter value :",
  gotoWorkSpaceText:
    "Go to your google worskpace and copy the key and  the value",
  gotoWorkspace: "Go to Google Workspace",
  userDetails: "User Details",
  name: "Name",
  email: "Email",
  currentSubscription: "Current Subscription",
  userId: "User ID",
  subscriptionStatus: "Subscription Status",
  currentQuotas: "Current Quota status for",
  quotaDetails: "Quota Details",
  additionalQuota: "Additional Quota",
  updateRemainingQuota: "Update Remaining Quota",
  adminQuotaManagement: "Admin Quota Management",
  search: "Search",
  searchSubText: "Search to retrieve a particular record",
  successBannerText: "Quota updated successfully",
  errorBannerText:
    " An error occurred while updating quota. Please try again later.",
  saveChanges: "Save Changes",
  usedQuota: "Used Quota:",
  remainingQuota: "Remaining Quota:",
  totalQuota: "Total Quota:",
  generatedApiKeyName: "Generated API Key Name -",
  apiKeyName: "API Key Name -",
  createNewApiKey: "Create New API Key",
  tokenCreatedSuccessfully: "Token created successfully!",
  failedToCreateToken: "Failed to create token. Please try again later.",
  scopes: "Scopes",
  creating: "Creating ...",
  createApiKey: "Create API Key",
  generatedApiKeyTOken: "Generated API Key Token",
  keepThisTokenSecure: "Keep this token secure.",
  apiKeyToken: "API Key Token",
  loadingTOken: "Loading token...",
  failedToLoadToekn: "Failed to load token. Try again later",
  close: "Close",
  apiKeys: "API Keys",
  manageApiKeys:
    "Manage your API keys for accessing different services programmatically.",
};

const collection = {
  collectionName: "Collection Name",
  enterNameofCollection: "Enter new collection name",
  save: "Save",
  cancel: "Cancel",
  downloadCollection: "Download Collection",
  newCampaign: "New Campaign",
  guidanceBannerSubtext: [
    "Collections can only be deleted if they are selected in a Draft Campaign.  But first, you must de-select them in the Campaign Setup and Save that change.",
    "Collections attached to Campaigns that are Warming, Running, Queued, Paused, or Completed cannot be deleted.",
  ],
};

const dealTracker = {
  dealTracker: "Deal Tracker",
  dealTrackerSubtext: "Track your deals here",
  currencies: ["GBP", "USD", "EUR"],
};

const payment = {
  subscribeNow: "Subscribe Now",
  pricingPlans: "Pricing plans",
  pricingPlanSubtext1: "Before you continue please select your plan.",
  pricingPlansSubtext2:
    "All plans are annual, with the option to pay monthly or upfront for 12 months.",
  startTrial: "Start Your 7 Day Free Trial",
  trialHelpIconTooltip:
    "During your trial period you will have a reduced quota allowance",
  tabOptions: [
    { value: "monthly", label: "Monthly billing" },
    { value: "yearly", label: "Annual billing" },
  ],
  monthly: "monthly",
  annualy: "annualy",
  subscribe: "Subscribe",
  getStarted: "Get Started",
  services: "SERVICES",
  yourCurrentPlan: "Your current plan",
  upgrade: "Upgrade",
  trialEndsIn: "Trial ends in ",
  days: " days",
  description: "Our most popular plan for small teams.",
  managePlan: "Manage Plan",
  increaseQuotaHeading: "Increase your quota with top-up:",
  monthlyBilling: "Monthly billing",
  annualBilling: "Annual billing (save > 15%)",
  currentPlan: "Current Plan",
  endTrialNow: "End Trial Now",
  cancelPlan: "Cancel Plan",
  singleTopUp: "Single Top up",
  topUp: "Top up",
  summary: "Summary",
  errorForUpgrading:
    " We have encountered an error while upgrading your plan. Please try again later.",
  errorForEndingTrial:
    "We have encountered an error while ending your trial. Please try again later.",
  errorForCancellingPlan:
    "We have encountered an error while cancelling your plan. Please try again later.",
  errorTryAgainLater: "Please try again later.",
  exceedMonthlyQuota: "Your selection exceeds your monthly quota limit",
  increaseEmailQuota: "Increase Email Quota",
  increaseLetterQuota: "Increase Letter Quota",
  moveRecipients: "Move Recipients",
  moveRecipientSubtext:
    "You can move remaining recipients to a new collection, ready for your next campaign.",
  welcome: "Welcome",
  unlockFullQuotaNow: "Unlock full quota now",
  areYouSureYouWantToUnlockFullQuota:
    "Are you sure you want to cancel your free trial early?",
  unlockFullQuotaNowSubtext:
    "Your free trial will end instantly, and your subscription will start right away.",
  readyToUnlockFullQuota: "Ready to unlock your full quota?",
  activateNow: "Activate Now",
  noKeepTrial: "No, keep trial",
  stayInTrial: "Stay In Trial",
  cancelledOn: "Cancels on",
  planCancelled: "PLAN CANCELLED",
  active: "Active",
  unavalible: "Unavailable",
  includes: "Includes ",
  rolloverSubtext: " rolled over from previous month",
  comingSoon: "Coming Soon",
  expiredSubText: "Your subscription ended on",
  areyouSureYouWantToCancel: "Are you sure you want to cancel your plan?",
  cancelSubscriptionModalSubtext:
    "Your subscription will remain active until the end of your billing cycle. Your quotas will be available until that date.",
  cancelSubscription: "Cancel Plan",
  close: "Close",
  getEarlyAccess: "Get Early Access",
  skiptheTrial: "Prefer to skip the trial? Subscribe now",
  watchTheTourHelperText: "Want to see BizCrunch in action before your trial?",
  watchTheTour: "Watch the tour",
  bizcrunchTour: "BizCrunch Tour",
  upgradeHelperText:
    "Please upgrade or use a one-time top-up to continue sending.",
  emailQuota: "Email Quota",
  letterQuota: "Letter Quota",
  total: "Total:",
  available: "Available:",
  required: "Required:",
  createCollectionHelperText:
    "Or you can create a new collection with the available quota and try again",
  exceddedBothQuota: "You’ve exceeded both your email and letter quotas.",
  exceededEmailQuota: "You’ve exceeded your email quota.",
  exceededLetterQuota: "You’ve exceeded your letter quota.",
  logoutTextModal: "Not ready yet? Logout and come back later.",
};

const account = {
  billing: "Billing",
  resetPassword: "Reset Password",
  logout: "Log out",
};

export const lang = {
  campaign,
  admin,
  collection,
  dealTracker,
  payment,
  account,
};

export default lang;
