import { useState } from "react";
import { useDispatch } from "react-redux";
import { endTrialSubscription, getCurrentSubscription } from "./services";
import { updateCurrentSubscription } from "./subscriptionSlice";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import { Button } from "components/shadcn/ui/button";
import { CurrentSubscriptionDetails } from "./types";
import UnlockFullQuotaNowModal from "./confirm-unlock-full-quota/confirmUnlockFullQuota";
import lang from "lang";
import Loader from "components/common/loader";

interface UnlockQuotaBannerProps {
    userData: any;
    currentSubscriptionDetails: CurrentSubscriptionDetails;
    source: string;
    onUnlockSuccess?: (success: boolean) => void;
}

const UnlockFullQuotaBanner: React.FC<UnlockQuotaBannerProps> = ({
    userData,
    currentSubscriptionDetails,
    source,
    onUnlockSuccess,
}) => {
    const { campaign: campaignCopy } = lang;
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [loading, setLoading] = useState(false);
    const dispatch = useDispatch();

    const handleUnlockQuota = async () => {
        setLoading(true);
        try {
            setShowConfirmModal(false);
            const endTrialResponse = await endTrialSubscription(
                currentSubscriptionDetails?.id
            );
            if (endTrialResponse) {
                const currentSubscription = await getCurrentSubscription();
                if (currentSubscription) {
                    dispatch(updateCurrentSubscription(currentSubscription));
                    onUnlockSuccess?.(true);
                }
                mixpanelCustomEvent({
                    mixpanelProps: {
                        End_Trial: source,
                    },
                    id: userData?.uid.toString(),
                    eventName: MixpanelEventName.clicksEndTrialNow,
                });
            }
        } catch (error) {
            console.error("Error ending trial:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            {loading && <Loader />}
            <div
                className={`w-full flex justify-center ${source === "searchPage"
                    ? " mt-32 mb-[-70px]"
                    : source === "campaign"
                        ? "w-full flex items-center justify-between bg-[--primary-100] text-pink-900 rounded-t-[24px]"
                        : ""
                    }`}
            >
                <div className={`flex justify-between p-4 bg-[--primary-100] text-pink-900 rounded-[24px] ${source === "campaign" ? "w-full" : "w-[75%]"}`}>
                    <p className="text-sm font-regular text-gray-950 text-left">
                        {campaignCopy.trialUserBannerText}
                    </p>
                    <Button
                        variant="primary"
                        className="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 text-sm font-medium rounded-[32px]"
                        onClick={() => setShowConfirmModal(true)}
                        disabled={loading}
                    >
                        <span className="text-white font-inter text-sm font-semibold leading-5 text-left underline-from-font">
                            {campaignCopy.unlockFullQouta}
                        </span>
                    </Button>
                </div>

                {
                    showConfirmModal && (
                        <UnlockFullQuotaNowModal
                            onClose={() => setShowConfirmModal(false)}
                            handleUnlockQuota={handleUnlockQuota}
                            isOpen={showConfirmModal}
                        />
                    )
                }
            </div >
        </>
    );
};

export default UnlockFullQuotaBanner;
