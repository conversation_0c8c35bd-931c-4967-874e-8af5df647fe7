import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from "components/shadcn/ui/dialog"
import { Button } from "components/shadcn/ui/button"
import lang from "lang"
import SvgCloseIcon from "components/common/iconComponents/closeIcon"

interface ConfirmCancelSubscriptionProps {
    isOpen: boolean,
    onClose: () => void,
    handleCancelSubscription?: () => void;
}

export function ConfirmCancelSubscription({ isOpen, onClose, handleCancelSubscription }: ConfirmCancelSubscriptionProps) {
    const { payment: paymentCopy } = lang;

    return (
        <div className="flex z-999 rounded-[32px]">
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="sm:max-w-[60%]">
                    <div className="flex flex-row justify-end">
                        <button onClick={onClose}><SvgCloseIcon /></button>
                    </div>
                    <DialogHeader className="flex flex-row items-center justify-between">
                        <DialogTitle className="text-lg font-semibold text-gray-900">{paymentCopy.areyouSureYouWantToCancel}</DialogTitle>
                    </DialogHeader>
                    <div className="text-gray-900 text-sm font-normal">{paymentCopy.cancelSubscriptionModalSubtext}</div>
                    <div className="flex justify-end gap-4">
                        <Button variant="outline" onClick={onClose}>
                            {paymentCopy.close}
                        </Button>
                        <Button onClick={handleCancelSubscription} variant="primary" className="bg-[#E6007A] hover:bg-[#E6007A]/90 text-white">
                            {paymentCopy.cancelSubscription}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    )
}

export default ConfirmCancelSubscription;