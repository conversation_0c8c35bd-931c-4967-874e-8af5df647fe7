import { useContext, useState } from "react";
import { useSelector } from "react-redux";
import {
  selectCurrentSubscription,
  selectProductPlans,
} from "../subscriptionSlice";
import {
  PRICING_INTERVAL,
  PRICING_INTERVAL_KEY,
  SUBSCRIPTION_DISPLAY_NAME,
} from "../types";
import { ParentContext } from "components/constants/ParentContext";
import lang from "lang";
import { paymentStyles } from "localFunctions/themeSetter";

export const useUpgradeSubscription = () => {
  const context = useContext(ParentContext);
  const { payment: paymentCopy } = lang;
  const subscriptionDetails = useSelector(selectCurrentSubscription);
  const productPlans = useSelector(selectProductPlans);

  const [activeTab, setActiveTab] = useState<"monthly" | "yearly">(
    subscriptionDetails.productPricing.interval === PRICING_INTERVAL.MONTH
      ? PRICING_INTERVAL_KEY.MONTHLY
      : PRICING_INTERVAL_KEY.YEARLY
  );

  const plans = Array.isArray(productPlans)
    ? productPlans.filter(
        (plan) =>
          plan.pricingInterval ===
          (activeTab === "monthly"
            ? PRICING_INTERVAL.MONTH
            : PRICING_INTERVAL.YEAR)
      )
    : [];

  const extendedPlans = plans.map((plan) => ({
    ...plan,
    isCurrentPlan:
      plan.productPricingId === subscriptionDetails.productPricing.id,
  }));

  const isDowngrade = (plan: any) => {
    const currentInterval = subscriptionDetails.productPricing.interval;
    const currentType = subscriptionDetails.productDisplayName;
    const newInterval = plan.pricingInterval;
    const newType = plan.displayName;

    if (
      currentInterval === PRICING_INTERVAL.YEAR &&
      currentType === SUBSCRIPTION_DISPLAY_NAME.SOURCE &&
      (newInterval === PRICING_INTERVAL.MONTH ||
        (newInterval === PRICING_INTERVAL.YEAR &&
          newType === SUBSCRIPTION_DISPLAY_NAME.SEARCH))
    ) {
      return true;
    }

    if (
      currentInterval === PRICING_INTERVAL.MONTH &&
      currentType === SUBSCRIPTION_DISPLAY_NAME.SOURCE &&
      newInterval === PRICING_INTERVAL.YEAR &&
      newType === SUBSCRIPTION_DISPLAY_NAME.SEARCH
    ) {
      return true;
    }

    if (
      currentInterval === PRICING_INTERVAL.MONTH &&
      currentType === SUBSCRIPTION_DISPLAY_NAME.SEARCH
    ) {
      return false;
    }

    if (
      currentInterval === PRICING_INTERVAL.MONTH &&
      currentType === SUBSCRIPTION_DISPLAY_NAME.SOURCE &&
      newInterval === PRICING_INTERVAL.MONTH &&
      newType === SUBSCRIPTION_DISPLAY_NAME.SEARCH
    ) {
      return true;
    }

    if (
      currentInterval === PRICING_INTERVAL.YEAR &&
      currentType === SUBSCRIPTION_DISPLAY_NAME.SEARCH &&
      newInterval === PRICING_INTERVAL.MONTH
    ) {
      return true;
    }

    return false;
  };

  return {
    context,
    paymentCopy,
    subscriptionDetails,
    activeTab,
    setActiveTab,
    plans,
    extendedPlans,
    isDowngrade,
    paymentStyles,
  };
};
