import React from "react";
import { Card, CardHeader, CardFooter } from "components/shadcn/ui/card";
import { Badge } from "components/shadcn/ui/badge";
import { Button } from "components/shadcn/ui/button";
import clsx from "clsx";
import { getCardWidthClass, formatService, calculateDaysRemaining } from "helpers";
import { SUBSCRIPTION_STATUS } from "../types";
import { renderFeatureIcon, renderAdditionalFeatureIcon, getPlanIcon } from "../featureIcons";
import { calendalyInvite, enableSourcePlan } from "components/utils/network/endpoints";

interface PlanCardProps {
  plan: any;
  plans: any[];
  isCurrentPlan: boolean;
  isHarbour: boolean;
  subscriptionDetails: any;
  isDowngrade: (plan: any) => boolean;
  paymentCopy: any;
  paymentStyles: any;
  handleUpgradeSubscription?: (currentSubscription: any, productPricingId: string) => void;
  handleUnlockQuota?: (subscriptionId: string) => void;
  handleCancelSubscription?: () => void;
}

const PlanCard: React.FC<PlanCardProps> = ({
  plan,
  plans,
  isCurrentPlan,
  isHarbour,
  subscriptionDetails,
  isDowngrade,
  paymentCopy,
  paymentStyles,
  handleUpgradeSubscription,
  handleUnlockQuota,
  handleCancelSubscription
}) => {
  return (
    <Card
      key={plan.id}
      style={{
        background: isCurrentPlan
          ? isHarbour
            ? paymentStyles.cardBackgroundHarbourRecommended
            : paymentStyles.cardBackgroundDefaultRecommended
          : "linear-gradient(0deg, #F9FAFB, #F9FAFB)",
      }}
      className={clsx(
        "rounded-[32px] border border-gray-300 bg-gray-50 overflow-hidden p-3 sm:p-4 flex flex-col",
        getCardWidthClass(plans.length),
        plans.length === 1 ? "mx-auto" : ""
      )}
    >
      <CardHeader className="flex flex-col gap-2 flex-grow p-0 sm:p-2">
        <div className="flex flex-row justify-between">
          <div className="flex flex-col">
            <div className="text-base sm:text-lg font-semibold text-gray-600 flex flex-row gap-2">
              <span className={
                isHarbour
                  ? paymentStyles.gradientHarbour
                  : paymentStyles.gradientDefault
              }>
                {plan.displayName}
              </span>

              {isCurrentPlan && (
                <Badge
                  className={clsx(
                    "rounded-full px-3 py-1 text-sm h-[fit-content] font-medium text-[--primary-700]",
                    isHarbour
                      ? "bg-[#B9E6FE] " // Harbour Club styles
                      : "bg-pink-50 " // Default styles
                  )}
                >
                  {paymentCopy.currentPlan}
                </Badge>
              )}
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl sm:text-3xl font-semibold text-gray-900">
                ${plan.price.toLocaleString()}
              </span>
              <span
                className={`text-sm ml-1 ${isHarbour ? "text-blue-500" : "text-gray-500"}`}
              >
                {plan.pricingInterval === "month" ? "/month" : "/year"}
              </span>
            </div>
          </div>
          {getPlanIcon({ planName: plan.name, isHarbour })}
        </div>
        <div className="flex text-left flex-col gap-2 content-start">
          {plan.features
            .filter((feature: any) => feature.canHighlight)
            .map((feature: any, idx: number) => (
              <div
                key={idx}
                className="flex text-sm sm:text-base mb-2 flex-row gap-4"
              >
                <div>{renderFeatureIcon({ lookupKey: feature.lookupKey, isHarbour })}</div>
                <div className="flex flex-row gap-1 text-left text-gray-600">
                  <span className="font-semibold text-gray-600">
                    {feature?.capacity?.toLocaleString()}
                  </span>
                  <span className="font-semibold text-gray-600">
                    {feature.highlightName}
                  </span>
                  {plan.pricingInterval === "month" ? "/month" : "/year"}
                </div>
              </div>
            ))}
        </div>
      </CardHeader>

      <div className="flex flex-col items-center justify-center mt-3 sm:mt-4 gap-4">
        {isCurrentPlan &&
          subscriptionDetails.status === SUBSCRIPTION_STATUS.TRAILING ? (
          <Button
            variant="outline"
            className="flex flex-col h-[fit-content] w-[60%]"
          >
            <span
              style={{ color: "var(--primary-700)" }}
              onClick={() =>
                handleUnlockQuota &&
                handleUnlockQuota(subscriptionDetails.id)
              }
            >
              {paymentCopy.unlockFullQuotaNow}
            </span>
            <span className="text-sm font-semibold hover:cursor-default">
              ({paymentCopy.trialEndsIn}{" "}
              {calculateDaysRemaining(
                subscriptionDetails?.trialEnd
              )}{" "}
              {paymentCopy.days})
            </span>{" "}
          </Button>
        ) : isCurrentPlan ? (
          <Button variant="outline">
            <span className="text-sm font-semibold">
              {paymentCopy.yourCurrentPlan}
            </span>
          </Button>
        ) : (
          <Button
            variant="payment"
            className="w-full sm:w-[80%] text-white"
            onClick={
              !isCurrentPlan && handleUpgradeSubscription
                ? () =>
                  handleUpgradeSubscription(
                    subscriptionDetails,
                    plan.productPricingId
                  )
                : plan.displayName === "Source" && !enableSourcePlan ? () => { window.location.href = calendalyInvite; } : undefined
            }
            disabled={isDowngrade(plan)}
          >
            <span className="text-white font-semibold text-sm">
              {plan.displayName === "Source" && !enableSourcePlan
                ? paymentCopy.getEarlyAccess
                : paymentCopy.upgrade}
            </span>
          </Button>
        )}
        {isCurrentPlan && !subscriptionDetails.cancelAt && (
          <button
            onClick={handleCancelSubscription}
          >
            <span className="text-md font-semibold"
              style={{ color: "var(--primary-700)" }}>
              {paymentCopy.cancelPlan}
            </span>
          </button>
        )}
      </div>

      <CardFooter className="p-0 sm:p-2 pt-4 sm:pt-6 flex-grow">
        <div className="space-y-2 sm:space-y-3 w-full">
          <h4 className="text-xs sm:text-sm font-semibold uppercase text-gray-900">
            {paymentCopy.services}
          </h4>
          <ul className="space-y-1 sm:space-y-2">
            {plan.features
              .map((feature: any, idx: number) => (
                <li key={idx} className="flex items-center gap-3">
                  {renderAdditionalFeatureIcon({ featureName: feature.lookupKey, isHarbour })}
                  <span className="text-sm sm:text-base font-normal ">
                    {feature.canHighlight ? feature.name : formatService(feature.name, feature.capacity)}
                  </span>
                </li>
              ))}
          </ul>
        </div>
      </CardFooter>
    </Card>
  );
};

export default PlanCard;