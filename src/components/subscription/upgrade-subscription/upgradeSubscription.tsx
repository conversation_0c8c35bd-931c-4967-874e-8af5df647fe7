import type React from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON>Title,
} from "components/shadcn/ui/dialog";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";
import { Ta<PERSON>, TabsList, TabsTrigger } from "components/shadcn/ui/tabs";
import clsx from "clsx";
import { CurrentSubscriptionDetails, SUBSCRIPTION_STATUS } from "../types";
import { useUpgradeSubscription } from "./useUpgradeSubscription";
import PlanCard from "./planCard";
import IndividualQuotaTopUp from "../individual-quota-topup/individualQuotaTopUp";

interface UpgradeSubscriptionModalProps {
    open: boolean;
    close: () => void;
    handleUnlockQuota?: (subscriptionId: string) => void;
    handleCancelSubscription?: () => void;
    handleUpgradeSubscription?: (
        currentSubscription: CurrentSubscriptionDetails,
        productPricingId: string
    ) => void;
    handleTopUp?: (topupDetails: any) => void;
}

const UpgradeSubscriptionModal: React.FC<UpgradeSubscriptionModalProps> = ({
    open,
    close,
    handleUnlockQuota,
    handleUpgradeSubscription,
    handleCancelSubscription,
    handleTopUp,
}) => {
    const {
        context,
        paymentCopy,
        subscriptionDetails,
        activeTab,
        setActiveTab,
        plans,
        extendedPlans,
        isDowngrade,
        paymentStyles
    } = useUpgradeSubscription();

    return (
        <Dialog open={open} onOpenChange={close}>
            <DialogContent className="w-[95vw] max-w-[75%] h-auto max-h-[90vh] p-4 sm:p-6 overflow-auto">
                <DialogHeader>
                    <button className="flex flex-row justify-end" onClick={close}>
                        <SvgCloseIcon />
                    </button>
                </DialogHeader>

                <div className="flex flex-col lg:flex-row w-full gap-6">
                    {subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING && (
                        <div className="w-full lg:w-[35%] flex flex-col gap-4 sm:gap-6">
                            <div className="text-center text-lg sm:text-xl font-semibold leading-none tracking-tight flex flex-col gap-4 sm:gap-6 items-center justify-center">
                                {paymentCopy.increaseQuotaHeading}
                            </div>
                            <div className="mx-auto w-full max-w-sm">
                                <IndividualQuotaTopUp
                                    handleTopUp={(topupDetails: any) =>
                                        handleTopUp && handleTopUp(topupDetails)
                                    }
                                />
                            </div>
                        </div>
                    )}

                    {subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING && (
                        <div className="h-[1px] lg:h-full w-full lg:w-[1px] bg-[#D7D7D7] my-4 lg:my-0" />
                    )}
                    <div
                        className={`w-full ${subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING
                            ? "lg:w-[60%]"
                            : "lg:w-full"
                            } flex flex-col gap-4 sm:gap-5`}
                    >
                        <DialogTitle className="text-center text-lg sm:text-xl flex flex-col gap-4 sm:gap-6 items-center justify-center">
                            {subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING
                                ? "or change your plan:"
                                : "change your plan:"}
                        </DialogTitle>
                        <Tabs
                            value={activeTab}
                            onValueChange={(value) =>
                                setActiveTab(value as "monthly" | "yearly")
                            }
                            className="w-full flex flex-row justify-center"
                        >
                            <TabsList
                                className="flex flex-col sm:flex-row justify-center w-full sm:w-[fit-content] space-y-2 sm:space-y-0 sm:space-x-4 p-2 bg-[#F9FAFB] rounded-lg"
                                isHarbour={context.isHarbour}
                            >
                                <TabsTrigger
                                    value="monthly"
                                    isHarbour={context.isHarbour}
                                    className={clsx(
                                        "text-sm sm:text-base px-2 py-1.5 sm:py-2",
                                        activeTab === "monthly"
                                            ? context.isHarbour
                                                ? "bg-[#3DAEDF] " // Harbour Club colors
                                                : "bg-[#FFF1F3] " // Default colors
                                            : context.isHarbour
                                                ? "text-[#182059]" // Harbour Club text color
                                                : "text-gray-500" // Default text color
                                    )}
                                    style={{
                                        color:
                                            activeTab === "monthly"
                                                ? "var(--primary-700)"
                                                : "var(--gray-500)",
                                    }}
                                >
                                    {paymentCopy.monthlyBilling}
                                </TabsTrigger>
                                <TabsTrigger
                                    value="yearly"
                                    isHarbour={context.isHarbour}
                                    className={clsx(
                                        "text-sm sm:text-base px-2 py-1.5 sm:py-2",
                                        activeTab === "yearly"
                                            ? context.isHarbour
                                                ? "bg-[#3DAEDF] " // Harbour Club colors
                                                : "bg-[#FFF1F3] " // Default colors
                                            : context.isHarbour
                                                ? "text-[#182059]" // Harbour Club text color
                                                : "text-gray-500" // Default text color
                                    )}
                                    style={{
                                        color:
                                            activeTab === "yearly"
                                                ? "var(--primary-700)"
                                                : "var(--gray-500)",
                                    }}
                                >
                                    {paymentCopy.annualBilling}
                                </TabsTrigger>
                            </TabsList>
                        </Tabs>
                        <div className="flex flex-wrap justify-center gap-3 sm:gap-4 mt-3 sm:mt-4 overflow-y-auto max-h-[60vh] sm:max-h-[70vh] md:max-h-none pb-1 sm:pb-2 -mx-2 px-2">
                            {extendedPlans.map((plan) => (
                                <PlanCard
                                    key={plan.id}
                                    plan={plan}
                                    plans={plans}
                                    isCurrentPlan={plan.isCurrentPlan}
                                    isHarbour={context.isHarbour}
                                    subscriptionDetails={subscriptionDetails}
                                    isDowngrade={isDowngrade}
                                    paymentCopy={paymentCopy}
                                    paymentStyles={paymentStyles}
                                    handleUpgradeSubscription={handleUpgradeSubscription}
                                    handleUnlockQuota={handleUnlockQuota}
                                    handleCancelSubscription={handleCancelSubscription}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default UpgradeSubscriptionModal;