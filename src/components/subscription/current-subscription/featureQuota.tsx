import React from "react";
import { SUBSCRIPTION_STATUS } from "../types";
import lang from "lang";

interface FeatureQuotaProps {
  id: string;
  featureName: string;
  usedQuota: number;
  totalQuota: number;
  rolloverQuota?: number;
  subscriptionStatus: string;
}

const FeatureQuota: React.FC<FeatureQuotaProps> = ({
  id,
  featureName,
  usedQuota,
  totalQuota,
  rolloverQuota = 0,
  subscriptionStatus
}) => {
  const { payment: paymentCopy } = lang;
  const isCancelled = subscriptionStatus === SUBSCRIPTION_STATUS.CANCELLED;

  return (
    <div key={id} className="pb-4 border-b border-[#EAECF0] last:border-none">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between w-full gap-2 sm:gap-0">
        <div className="flex flex-col text-left gap-2 sm:gap-3 w-full">
          <h3 className="font-semibold mt-0 text-gray-600">{featureName} quota</h3>
          {rolloverQuota > 0 && (
            <span className="text-xs font-medium text-gray-500">
              {paymentCopy.includes}
              {rolloverQuota}
              {paymentCopy.rolloverSubtext}
            </span>
          )}
        </div>
        {isCancelled ? (
          <span className="text-xl font-semibold mt-1 sm:mt-0" style={{ color: "var(--primary-600)" }}>
            {paymentCopy.unavalible}
          </span>
        ) : (
          <span className="text-xl font-semibold text-gray-950 mt-1 sm:mt-0">
            {usedQuota}/{totalQuota}
          </span>
        )}
      </div>
    </div>
  );
};

export default FeatureQuota;