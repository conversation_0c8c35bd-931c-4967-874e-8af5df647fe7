import { useMemo } from "react";
import { calculateDaysRemaining, formatDate } from "helpers";
import { SUBSCRIPTION_STATUS, type CurrentSubscriptionDetails } from "../types";
import lang from "lang";

export const useCurrentSubscription = (
  currentSubscription: CurrentSubscriptionDetails
) => {
  const { payment: paymentCopy } = lang;
  const daysRemaining = useMemo(
    () => calculateDaysRemaining(currentSubscription?.trialEnd),
    [currentSubscription?.trialEnd]
  );

  const isTrialActive =
    currentSubscription.status === SUBSCRIPTION_STATUS.TRAILING;
  const isSubscriptionActive =
    currentSubscription.status === SUBSCRIPTION_STATUS.ACTIVE;
  const isSubscriptionCancelled =
    currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED;

  const showManagePlanButton = [
    SUBSCRIPTION_STATUS.ACTIVE,
    SUBSCRIPTION_STATUS.TRAILING,
  ].includes(currentSubscription.status as SUBSCRIPTION_STATUS);

  const showSubscribeButton =
    currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED;

  return {
    paymentCopy,
    daysRemaining,
    isTrialActive,
    isSubscriptionActive,
    isSubscriptionCancelled,
    showManagePlanButton,
    showSubscribeButton,
    formatDate,
  };
};
