import React from "react";
import { type CurrentSubscriptionDetails } from "../types";
import { useCurrentSubscription } from "./useCurrentSubscription";
import SubscriptionStatus from "./subscriptionStatus";
import FeatureQuota from "./featureQuota";
import CancelledSubscriptionMessage from "./cancelledSubscriptionMessage";

interface CurrentSubscriptionProps {
  editPlan: () => void;
  handleEndTrial?: (id: string) => void;
  currentSubscription: CurrentSubscriptionDetails;
  user: any;
  handleCreateSubscription?: () => void;
}

const CurrentSubscriptionTile: React.FC<CurrentSubscriptionProps> = ({
  editPlan,
  currentSubscription,
  handleCreateSubscription
}) => {
  const {
    paymentCopy,
    daysRemaining,
    isSubscriptionCancelled,
    showManagePlanButton,
    formatDate
  } = useCurrentSubscription(currentSubscription);

  return (
    <div className="w-full rounded-[32px] bg-[#F9FAFB] p-4 sm:p-6 shadow-sm">
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
        <div className="flex flex-col gap-1 text-left">
          <div className="flex flex-wrap items-center gap-2 sm:gap-3">
            <h2 className="text-lg font-semibold text-gray-900">
              {currentSubscription.productDisplayName}
            </h2>
            <SubscriptionStatus
              status={currentSubscription.status}
              cancelAt={currentSubscription.cancelAt}
              daysRemaining={daysRemaining}
              paymentCopy={paymentCopy}
              formatDate={formatDate}
            />
          </div>
        </div>
        <div className="text-left sm:text-right">
          <span className="text-3xl sm:text-5xl font-semibold leading-tight sm:leading-[60px]">
            ${currentSubscription.productPricing.price}
          </span>
          <span className="text-base sm:text-lg text-gray-600">
            /per {currentSubscription.productPricing.interval}
          </span>
        </div>
      </div>

      {!isSubscriptionCancelled ? (
        <div className="bg-white border border-[#F2F4F7] shadow-[0px_0px_0px_4px_#F4EBFF,0px_1px_2px_0px_#1018280F] rounded-3xl p-3 sm:p-4">
          <div className="flex flex-col gap-4">
            {currentSubscription.userFeatureQuotas.map((quota) => (
              <FeatureQuota
                key={quota.id}
                id={quota.id || ""}
                featureName={quota.featureName || ""}
                usedQuota={quota.usedQuota || 0}
                totalQuota={quota.totalQuota || 0}
                rolloverQuota={quota.rolloverQuota || undefined}
                subscriptionStatus={currentSubscription.status}
              />
            ))}
          </div>
        </div>
      ) : (
        <CancelledSubscriptionMessage
          cancelledAt={currentSubscription.cancelledAt}
          paymentCopy={paymentCopy}
          formatDate={formatDate}
          handleCreateSubscription={handleCreateSubscription}
        />
      )}

      {showManagePlanButton && (
        <button
          className="mt-4 sm:mt-6 text-sm font-medium"
          style={{ color: "var(--primary-700)" }}
          onClick={editPlan}
        >
          {paymentCopy.managePlan}
        </button>
      )}
    </div>
  );
};

export default CurrentSubscriptionTile;
