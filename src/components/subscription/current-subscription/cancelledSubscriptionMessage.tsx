import React from "react";
import { Button } from "components/shadcn/ui/button";

interface CancelledSubscriptionMessageProps {
  cancelledAt?: string | null;
  paymentCopy: any;
  formatDate: (date: string) => string;
  handleCreateSubscription?: () => void;
}

const CancelledSubscriptionMessage: React.FC<CancelledSubscriptionMessageProps> = ({
  cancelledAt,
  paymentCopy,
  formatDate,
  handleCreateSubscription
}) => {
  return (
    <div>
      <div className="flex items-center justify-between p-4 bg-[--primary-100] text-pink-900 rounded-[12px] w-full">
        <div className="flex items-center gap-3">
          {cancelledAt && (
            <p className="text-md font-medium text-gray-950 text-left">
              {paymentCopy.expiredSubText} {formatDate(cancelledAt)}.
              <br /><br />To continue enjoying uninterrupted access to BizCrunch's powerful features, please subscribe to a new plan today.
            </p>
          )}
        </div>
      </div>
      <Button
        variant="payment"
        className="w-full sm:w-[80%] text-white mt-4 sm:mt-6"
        onClick={handleCreateSubscription}
      >
        <span className="text-white font-semibold text-sm">Subscribe</span>
      </Button>
    </div>
  );
};

export default CancelledSubscriptionMessage;