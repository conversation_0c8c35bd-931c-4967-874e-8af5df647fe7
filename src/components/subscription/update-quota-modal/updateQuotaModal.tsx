import React from "react";
import { DialogTitle } from "@radix-ui/react-dialog";
import SvgMinusCircularIcon from "components/common/iconComponents/minusCircularIcon";
import SvgPlusCircularIcon from "components/common/iconComponents/plusCircularIcon";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";
import { Button } from "components/shadcn/ui/button";
import { Dialog, DialogContent } from "components/shadcn/ui/dialog";
import { useEmailQuotaModal } from "./useUpdateQuotaModal";

interface UpdateQuotaModalProps {
    open: boolean;
    showEmailQuotaBlock: boolean;
    showLetterQuotaBlock: boolean;
    close: () => void;
    changePlan: () => void;
    handleTopUpClick: (topUpPayload: any) => void;
}

const UpdateQuotaModal: React.FC<UpdateQuotaModalProps> = ({
    open,
    showEmailQuotaBlock,
    showLetterQuotaBlock,
    close,
    changePlan,
    handleTopUpClick
}) => {
    const {
        productTopUp,
        topUpState,
        totalPrice,
        handleIncrement,
        handleDecrement,
        createTopUpPayload
    } = useEmailQuotaModal(showEmailQuotaBlock, showLetterQuotaBlock);

    const handleTopUp = () => {
        const topUpPayload = createTopUpPayload();
        handleTopUpClick(topUpPayload);
    };

    return (
        <Dialog open={open} onOpenChange={close}>
            <DialogContent className="max-w-lg w-full p-6 rounded-[32px] bg-white shadow-lg">
                <button className="w-full flex justify-end" onClick={close}>
                    <SvgCloseIcon />
                </button>
                <DialogTitle className="text-base sm:text-lg font-semibold text-gray-600">
                    Increase your quota:
                </DialogTitle>

                {productTopUp.map((product) => (
                    <div key={product.id} className="flex mt-3 mb-3 items-center justify-between border rounded-lg h-[70px] sm:h-[90px]">
                        <button
                            onClick={() => handleDecrement(product.id)}
                            className="text-pink-500 text-xl font-bold px-2 sm:px-3"
                            aria-label="Decrease amount"
                        >
                            <SvgMinusCircularIcon />
                        </button>
                        <div className="flex flex-col items-center gap-1">
                            <div className="text-2xl sm:text-[36px] font-semibold leading-tight sm:leading-[44px] tracking-[-0.01em] text-gray-900">
                                ${topUpState[product.id]?.amount || 0}
                            </div>
                            <p className="text-center text-sm sm:text-lg text-gray-600">
                                +{topUpState[product.id]?.quota || 0} {product.feature}
                            </p>
                        </div>
                        <button
                            onClick={() => handleIncrement(product.id)}
                            className="text-pink-500 text-xl font-bold px-2 sm:px-3"
                            aria-label="Increase amount"
                        >
                            <SvgPlusCircularIcon />
                        </button>
                    </div>
                ))}

                <Button
                    variant="payment"
                    className="w-full text-white py-1.5 sm:py-2 rounded-[120px]"
                    onClick={handleTopUp}
                    disabled={totalPrice === 0}
                >
                    Top up (${totalPrice})
                </Button>

                <button className="w-full mt-2 text-lg font-semibold" style={{ color: "var(--primary-900)" }} onClick={changePlan}>
                    or Change your plan
                </button>
            </DialogContent>
        </Dialog>
    );
};

export default UpdateQuotaModal;