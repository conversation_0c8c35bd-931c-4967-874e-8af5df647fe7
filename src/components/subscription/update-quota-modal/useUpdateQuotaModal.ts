import { useState } from "react";
import { useSelector } from "react-redux";
import { selectProductTopup } from "../subscriptionSlice";

interface ProductTopUp {
  id: string;
  price: number;
  quota: number;
  productPricingId: string;
  feature: string;
}

export interface TopUpState {
  [key: string]: {
    amount: number;
    quota: number;
    count: number;
  };
}

export const useEmailQuotaModal = (
  showEmailQuotaBlock: boolean,
  showLetterQuotaBlock: boolean
) => {
  const selectedFeatures: string[] = [];
  if (showEmailQuotaBlock) selectedFeatures.push("Email Outreach");
  if (showLetterQuotaBlock) selectedFeatures.push("Letter Outreach");

  const productTopUp: ProductTopUp[] = useSelector(selectProductTopup).filter(
    (product) => selectedFeatures.includes(product.feature)
  );

  const [topUpState, setTopUpState] = useState<TopUpState>(
    productTopUp.reduce((acc: TopUpState, item: ProductTopUp) => {
      acc[item.id] = { amount: 0, quota: 0, count: 0 };
      return acc;
    }, {})
  );

  const handleIncrement = (id: string) => {
    setTopUpState((prev: TopUpState) => {
      const product = productTopUp.find((p) => p.id === id);
      if (!product) return prev;

      return {
        ...prev,
        [id]: {
          amount: prev[id].amount + product.price,
          quota: prev[id].quota + product.quota,
          count: prev[id].count + 1,
        },
      };
    });
  };

  const handleDecrement = (id: string) => {
    setTopUpState((prev: TopUpState) => {
      if (prev[id].count > 0) {
        const product = productTopUp.find((p) => p.id === id);
        if (!product) return prev;

        return {
          ...prev,
          [id]: {
            amount: prev[id].amount - product.price,
            quota: prev[id].quota - product.quota,
            count: prev[id].count - 1,
          },
        };
      }
      return prev;
    });
  };

  const totalPrice = Object.values(topUpState).reduce(
    (sum, item) => sum + item.amount,
    0
  );

  const createTopUpPayload = () => {
    return {
      products: Object.entries(topUpState)
        .filter(([_, item]) => item.amount > 0)
        .map(([id, item]) => ({
          productPricingId:
            productTopUp.find((p) => p.id === id)?.productPricingId ?? id,
          quantity: item.count,
        })),
    };
  };

  return {
    productTopUp,
    topUpState,
    totalPrice,
    handleIncrement,
    handleDecrement,
    createTopUpPayload,
  };
};
