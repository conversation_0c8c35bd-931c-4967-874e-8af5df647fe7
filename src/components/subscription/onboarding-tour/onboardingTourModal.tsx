import { onboardingTourUrl } from 'components/utils/network/endpoints';
import React from 'react';
import ReactPlayer from 'react-player';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "components/shadcn/ui/dialog";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";
import lang from 'lang';

interface OnboardingTourModalProps {
    isOpen: boolean;
    onClose: () => void;
}

const OnboardingTourModal: React.FC<OnboardingTourModalProps> = ({ isOpen, onClose }) => {
    const { payment: paymentCopy } = lang;
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[85%]  p-6">
                <div className="flex flex-row justify-end">
                    <button onClick={onClose}>
                        <SvgCloseIcon />
                    </button>
                </div>
                <DialogHeader>
                    <DialogTitle className="text-lg font-semibold text-gray-900">
                        {paymentCopy.bizcrunchTour}                    </DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                    <ReactPlayer
                        url={onboardingTourUrl}
                        playing={true}
                        controls={true}
                        width="100%"
                        height="70vh"
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default OnboardingTourModal;
