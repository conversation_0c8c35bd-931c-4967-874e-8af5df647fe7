import {
    Dialog,
    Di<PERSON><PERSON>ontent,
    Di<PERSON>Header,
    DialogTitle,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";
import { useSelector } from "react-redux";
import { selectCurrentSubscription } from "./subscriptionSlice";

interface UnlockFullQuotaSuccessModalProps {
    isOpen: boolean;
    onClose: () => void;
}

export function UnlockFullQuotaSuccessModal({
    isOpen,
    onClose,
}: UnlockFullQuotaSuccessModalProps) {

    const currentSubscriptionDetails = useSelector(selectCurrentSubscription);

    return (
        <div className="flex z-999 rounded-[32px]">
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="sm:max-w-[40%]">
                    <div className="flex flex-row justify-end">
                        <button onClick={onClose}>
                            <SvgCloseIcon />
                        </button>
                    </div>
                    <div className="flex flex-col items-center gap-9">
                        <DialogHeader className="flex flex-row items-center justify-between">
                            <DialogTitle className="text-lg font-semibold text-gray-900">
                                Confirmation Successful! 🎉
                            </DialogTitle>
                        </DialogHeader>
                        <div className="text-gray-900 text-sm font-normal">
                            Your {currentSubscriptionDetails.productPricing.interval}ly{" "}
                            {currentSubscriptionDetails.productName} subscription is now live.
                        </div>
                        <div className="flex justify-end gap-4">
                            <Button variant="outline" onClick={onClose}>
                                Manage Subscription
                            </Button>
                            <Button
                                onClick={onClose}
                                variant="primary"
                                className="bg-[#E6007A] hover:bg-[#E6007A]/90 text-white"
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}

export default UnlockFullQuotaSuccessModal;
