import { useState } from "react";
import { useSelector } from "react-redux";
import { selectProductTopup } from "../subscriptionSlice";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import lang from "lang";

export interface TopUpState {
  [key: string]: {
    amount: number;
    quota: number;
    count: number;
  };
}

export const useIndividualQuotaTopUp = (
  handleTopUp: (topUpPayload: any) => void
) => {
  const { payment: paymentCopy } = lang;
  const productTopUp = useSelector(selectProductTopup);
  const user = JSON.parse(localStorage.getItem("user") || "{}");

  const [topUpState, setTopUpState] = useState<TopUpState>(
    productTopUp.reduce((acc, item) => {
      acc[item.id] = { amount: 0, quota: 0, count: 0 };
      return acc;
    }, {} as TopUpState)
  );

  const handleIncrement = (id: string) => {
    setTopUpState((prev) => {
      const product = productTopUp.find((p) => p.id === id);
      if (!product) return prev;

      return {
        ...prev,
        [id]: {
          amount: prev[id].amount + product.price,
          quota: prev[id].quota + product.quota,
          count: prev[id].count + 1,
        },
      };
    });
  };

  const handleDecrement = (id: string) => {
    setTopUpState((prev) => {
      if (prev[id].amount > 0) {
        const product = productTopUp.find((p) => p.id === id);
        if (!product) return prev;

        return {
          ...prev,
          [id]: {
            amount: prev[id].amount - product.price,
            quota: prev[id].quota - product.quota,
            count: prev[id].count - 1,
          },
        };
      }
      return prev;
    });
  };

  const totalPrice = Object.values(topUpState).reduce(
    (sum, item) => sum + item.amount,
    0
  );

  const handleTopUpClick = () => {
    const topUpPayload = {
      products: Object.entries(topUpState)
        .filter(([_, item]) => item.amount > 0)
        .map(([id, item]) => ({
          productPricingId:
            productTopUp.find((p) => p.id === id)?.productPricingId ?? id,
          quantity: item.count,
        })),
    };

    const quotaType = topUpState?.productName ?? "";
    const newQuotaValue = Object.values(topUpState).reduce(
      (total, item) => total + item.count,
      0
    );
    const amount = Object.values(topUpState).reduce(
      (total, item) => total + item.amount,
      0
    );

    const mixpanelProps = {
      $name: user?.name ?? "",
      $email: user?.email ?? "",
      quota_type: quotaType,
      new_quota_value: newQuotaValue,
      amount: amount,
    };

    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
      },
      id: user?.uid?.toString(),
      eventName: MixpanelEventName.clicksOnTopUpQouta,
    });

    handleTopUp(topUpPayload);
  };

  return {
    paymentCopy,
    productTopUp,
    topUpState,
    totalPrice,
    handleIncrement,
    handleDecrement,
    handleTopUpClick,
  };
};
