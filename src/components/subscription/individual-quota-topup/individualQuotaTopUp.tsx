import React from "react";
import SvgMinusCircularIcon from "components/common/iconComponents/minusCircularIcon";
import SvgPlusCircularIcon from "components/common/iconComponents/plusCircularIcon";
import { Button } from "components/shadcn/ui/button";
import { Card } from "components/shadcn/ui/card";
import { useIndividualQuotaTopUp } from "./useIndividualQuotaTopUp";

interface IndividualQuotaTopUpProps {
    handleTopUp: (topUpPayload: any) => void;
}

const IndividualQuotaTopUp: React.FC<IndividualQuotaTopUpProps> = ({ handleTopUp }) => {
    const {
        paymentCopy,
        productTopUp,
        topUpState,
        totalPrice,
        handleIncrement,
        handleDecrement,
        handleTopUpClick
    } = useIndividualQuotaTopUp(handleTopUp);

    return (
        <Card className="w-full max-w-md py-6 px-3 rounded-[24px] shadow-lg bg-white flex flex-col gap-4">
            <div className="text-lg font-semibold text-gray-600">{paymentCopy.singleTopUp}</div>

            {productTopUp.map((item) => (
                <div
                    key={item.id}
                    className="flex flex-col gap-2 border rounded-lg px-2 py-4 bg-gray-50"
                >
                    <div className="text-sm font-medium text-gray-700">{item.feature}</div>

                    <div className="flex items-center justify-between border rounded-lg py-3 px-1 bg-white">
                        <button
                            onClick={() => handleDecrement(item.id)}
                            className="text-pink-500 text-xl font-bold"
                            aria-label="Decrease amount"
                        >
                            <SvgMinusCircularIcon />
                        </button>

                        <div className="flex flex-col items-center">
                            <div className="text-2xl font-semibold text-gray-900">
                                ${topUpState[item.id].amount}
                            </div>
                            <p className="text-sm text-gray-600">
                                {topUpState[item.id].quota} {item.feature}
                            </p>
                        </div>

                        <button
                            onClick={() => handleIncrement(item.id)}
                            className="text-pink-500 text-xl font-bold"
                            aria-label="Increase amount"
                        >
                            <SvgPlusCircularIcon />
                        </button>
                    </div>
                </div>
            ))}

            <div className="flex flex-col gap-2 border-t pt-3 text-sm text-gray-700">
                {productTopUp.map((item) => (
                    <div key={item.id} className="flex justify-between">
                        <span>{item.feature}:</span>
                        <span>${topUpState[item.id].amount}</span>
                    </div>
                ))}
                <div className="flex justify-between font-semibold text-[--primary-600]">
                    <span>{paymentCopy.summary}:</span>
                    <span>${totalPrice}</span>
                </div>
            </div>

            <Button
                className="w-full flex items-center text-white py-2 rounded-[120px]"
                variant="primary"
                disabled={totalPrice === 0}
                onClick={handleTopUpClick}
            >
                {paymentCopy.topUp}
            </Button>
        </Card>
    );
};

export default IndividualQuotaTopUp;