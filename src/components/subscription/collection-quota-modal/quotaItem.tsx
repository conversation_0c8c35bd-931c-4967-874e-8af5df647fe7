import React from "react";
import SvgMinusCircularIcon from "components/common/iconComponents/minusCircularIcon";
import SvgPlusCircularIcon from "components/common/iconComponents/plusCircularIcon";
import { TopUpState } from "./useCollectionQuotaModal";

interface QuotaItemProps {
    productId: string;
    topUpState: TopUpState;
    onDecrement: (id: string) => void;
    onIncrement: (id: string) => void;
}

const QuotaItem: React.FC<QuotaItemProps> = ({
    productId,
    topUpState,
    onDecrement,
    onIncrement
}) => {
    return (
        <div className="flex mt-3 mb-3 items-center justify-between border rounded-lg h-[70px] sm:h-[90px]">
            <button
                onClick={() => onDecrement(productId)}
                className="text-pink-500 text-xl font-bold px-2 sm:px-3"
                aria-label="Decrease amount"
            >
                <SvgMinusCircularIcon />
            </button>
            <div className="flex flex-col items-center gap-1">
                <div className="text-2xl sm:text-[36px] font-semibold leading-tight sm:leading-[44px] tracking-[-0.01em] text-gray-900">
                    ${topUpState[productId]?.amount || 0}
                </div>
                <p className="text-center text-sm sm:text-lg text-gray-600">
                    +{topUpState[productId]?.quota || 0} Collections
                </p>
            </div>
            <button
                onClick={() => onIncrement(productId)}
                className="text-pink-500 text-xl font-bold px-2 sm:px-3"
                aria-label="Increase amount"
            >
                <SvgPlusCircularIcon />
            </button>
        </div>
    );
};

export default QuotaItem;