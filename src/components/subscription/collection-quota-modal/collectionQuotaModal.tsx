import React from "react";
import { DialogTitle } from "@radix-ui/react-dialog";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";
import { Button } from "components/shadcn/ui/button";
import { Dialog, DialogContent } from "components/shadcn/ui/dialog";
import { useCollectionQuotaModal } from "./useCollectionQuotaModal";
import QuotaItem from "./quotaItem";

interface CollectionQuotaModalProps {
    open: boolean;
    close: () => void;
    changePlan: () => void;
    handleTopUpClick?: (topUpPayload: any) => void;
}

const CollectionQuotaModal: React.FC<CollectionQuotaModalProps> = ({
    open,
    close,
    changePlan,
    handleTopUpClick
}) => {
    const {
        productTopUp,
        topUpState,
        totalPrice,
        handleIncrement,
        handleDecrement,
        createTopUpPayload
    } = useCollectionQuotaModal();

    const handleTopUp = () => {
        const topUpPayload = createTopUpPayload();
        handleTopUpClick?.(topUpPayload);
    };

    return (
        <Dialog open={open} onOpenChange={close}>
            <DialogContent className="max-w-[30%] w-full p-6 rounded-[32px] bg-white shadow-lg">
                <button className="w-full flex justify-end" onClick={close}>
                    <SvgCloseIcon />
                </button>
                <DialogTitle className="text-base sm:text-lg font-semibold text-gray-600">
                    Increase your collection quota:
                </DialogTitle>

                {productTopUp.map((product) => (
                    <QuotaItem
                        key={product.id}
                        productId={product.id}
                        topUpState={topUpState}
                        onDecrement={handleDecrement}
                        onIncrement={handleIncrement}
                    />
                ))}

                <Button
                    variant="payment"
                    className="w-full text-white py-1.5 sm:py-2 rounded-[120px]"
                    onClick={handleTopUp}
                    disabled={totalPrice === 0}
                >
                    Top up (${totalPrice})
                </Button>

                <button
                    className="w-full mt-2 text-lg font-semibold"
                    style={{ color: "var(--primary-900)" }}
                    onClick={changePlan}
                >
                    or Change your plan
                </button>
            </DialogContent>
        </Dialog>
    );
};

export default CollectionQuotaModal;