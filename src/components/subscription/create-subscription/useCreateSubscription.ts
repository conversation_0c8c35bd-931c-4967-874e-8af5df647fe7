import { useState, useEffect } from "react";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import {
  PRICING_INTERVAL,
  PRICING_INTERVAL_KEY,
  SubscriptionPlan,
} from "../types";
import { paymentStyles } from "localFunctions/themeSetter";
import { signOut } from "firebase/auth";
import { auth } from "index";
import { persistor } from "components/common/store/store";
import { useNavigate } from "react-router-dom";

export const useCreateSubscription = (props: {
  open: boolean;
  purchasePlan: (plan: SubscriptionPlan, isFreeTrial: boolean) => void;
  close: () => void;
  handleCalendlyInvite: () => void;
  plansData: SubscriptionPlan[];
  planCancelled?: boolean;
}) => {
  const navigate = useNavigate();
  const { plansData, planCancelled } = props;
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const [showOnboardingTourModal, setShowOnboardingTourModal] = useState(false);
  const [activeTab, setActiveTab] = useState<
    PRICING_INTERVAL_KEY.MONTHLY | PRICING_INTERVAL_KEY.YEARLY
  >(PRICING_INTERVAL_KEY.YEARLY);

  const plans = Array.isArray(plansData)
    ? plansData.filter(
        (plan) =>
          plan.pricingInterval ===
          (activeTab === PRICING_INTERVAL_KEY.MONTHLY
            ? PRICING_INTERVAL.MONTH
            : PRICING_INTERVAL.YEAR)
      )
    : [];

  const mixpanelProps = {
    $name: `${user?.name}`,
    $email: user?.email,
  };

  useEffect(() => {
    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        Billing:
          activeTab === PRICING_INTERVAL_KEY.MONTHLY
            ? PRICING_INTERVAL_KEY.MONTHLY
            : PRICING_INTERVAL_KEY.YEARLY,
      },
      id: user?.uid?.toString(),
      eventName:
        activeTab === PRICING_INTERVAL_KEY.MONTHLY
          ? MixpanelEventName.clicksMonthlyBilling
          : MixpanelEventName.clicksAnnualBilling,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  const handleWatchTourModal = () => {
    setShowOnboardingTourModal(true);
    mixpanelCustomEvent({
      mixpanelProps,
      id: user?.uid?.toString(),
      eventName: MixpanelEventName.userClicksWatchTour,
    });
  };

  const handleLogout = () => {
    mixpanelCustomEvent({
      mixpanelProps,
      id: user?.uid?.toString() || "",
      eventName: `User logged out from subscription modal` as MixpanelEventName,
    });

    signOut(auth)
      .then(() => {
        localStorage.clear();
        persistor.purge();
        navigate("../login", { replace: true });
      })
      .catch((error) => console.error("ERROR", error.message));
  };

  return {
    activeTab,
    user,
    plans,
    paymentStyles,
    planCancelled,
    showOnboardingTourModal,
    handleWatchTourModal,
    setShowOnboardingTourModal,
    setActiveTab,
    handleLogout,
  };
};
