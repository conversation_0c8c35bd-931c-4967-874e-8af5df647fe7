import type React from "react";
import { <PERSON><PERSON> } from "components/shadcn/ui/button";
import { Card, CardFooter, CardContent } from "components/shadcn/ui/card";
import { HelpCircle } from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "components/shadcn/ui/dialog";
import { Ta<PERSON>, TabsList, TabsTrigger } from "components/shadcn/ui/tabs";
import clsx from "clsx";
import lang from "lang";
import { PRICING_INTERVAL, PRICING_INTERVAL_KEY, SUBSCRIPTION_PLAN_NAME, SubscriptionPlan } from "../types";
import { formatService, getCardWidthClass } from "helpers";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "components/shadcn/ui/tooltip";
import { Badge } from "components/shadcn/ui/badge";
import SvgSearchIconSubscription from "components/common/iconComponents/searchIconSubscription";
import SvgSourceIconSubscription from "components/common/iconComponents/sourceIconSubscription";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";
import { enableSourcePlan } from "components/utils/network/endpoints";
import { useCreateSubscription } from "./useCreateSubscription";
import OnboardingTourModal from "../onboarding-tour/onboardingTourModal";
import { renderAdditionalFeatureIcon, renderFeatureIcon } from "../featureIcons";

interface CreateSubscriptionProps {
    open: boolean;
    purchasePlan: (plan: SubscriptionPlan, isFreeTrial: boolean) => void;
    close: () => void;
    handleCalendlyInvite: () => void;
    plansData: SubscriptionPlan[];
    planCancelled?: boolean;
}

const CreateSubscription: React.FC<CreateSubscriptionProps> = (props) => {
    const { payment: paymentCopy } = lang;
    const {
        open,
        planCancelled,
        purchasePlan,
        handleCalendlyInvite,
        close,
    } = props;

    const {
        activeTab,
        user,
        plans,
        paymentStyles,
        showOnboardingTourModal,
        handleWatchTourModal,
        setActiveTab,
        setShowOnboardingTourModal,
        handleLogout,
    } = useCreateSubscription(props);

    return (
        <Dialog open={open}>
            {showOnboardingTourModal && (
                <div className="w-full h-[80vh]">
                    <OnboardingTourModal
                        isOpen={showOnboardingTourModal}
                        onClose={() => setShowOnboardingTourModal(false)}
                    />
                </div>
            )}
            <DialogContent className="w-full max-w-[70vw] h-auto max-h-[90vh] flex flex-col px-2 sm:px-4 md:px-6 lg:px-8">
                <DialogHeader className="mb-2 sm:mb-2">
                    {planCancelled && (
                        <button className="flex justify-end outline-none" onClick={close}>
                            <SvgCloseIcon />
                        </button>
                    )}

                    <DialogTitle className="text-center text-2xl font-semibold sm:text-2xl flex flex-col gap-2 sm:gap-4 items-center justify-center">
                        {paymentCopy.welcome} {""} {user?.name}
                    </DialogTitle>
                    <div className="w-full flex justify-center items-center mb-2 mt-2">
                        <div className="bg-green-100 p-3 rounded-xl flex items-center justify-between gap-3 w-[fit-content] h-[fit-content]">
                            <span className="font-semibold leading-none tracking-tight text-green-800">
                                {paymentCopy.watchTheTourHelperText}
                            </span>
                            <Button onClick={handleWatchTourModal} variant="primary">
                                {paymentCopy.watchTheTour}
                            </Button>
                        </div>
                    </div>
                </DialogHeader>

                <Tabs
                    value={activeTab}
                    onValueChange={(value) =>
                        setActiveTab(
                            value as
                            | PRICING_INTERVAL_KEY.MONTHLY
                            | PRICING_INTERVAL_KEY.YEARLY
                        )
                    }
                    className="w-full max-w-[90%] min-w-[200px] flex justify-center mx-auto"
                >
                    <TabsList
                        className="flex flex-col sm:flex-row content-center space-x-4 p-2 bg-[#F9FAFB] rounded-lg"
                        isHarbour={user.isHarbourClub}
                    >
                        {paymentCopy.tabOptions.map(({ value, label }) => (
                            <TabsTrigger
                                key={value}
                                value={value}
                                isHarbour={user.isHarbourClub}
                                className={clsx(
                                    "h-[40px] px-4",
                                    activeTab === value
                                        ? user.isHarbourClub
                                            ? "bg-[#3DAEDF] " // Harbour Club colors
                                            : "bg-[#FFF1F3] " // Default colors
                                        : user.isHarbourClub
                                            ? "text-[#182059]" // Harbour Club text color
                                            : "text-gray-500" // Default text color
                                )}
                                style={{
                                    color:
                                        activeTab === value
                                            ? "var(--primary-700)"
                                            : "var(--gray-500)",
                                }}
                            >
                                <div className="flex items-center gap-2">
                                    {label}
                                    {value === "yearly" && (
                                        <Badge
                                            className={`rounded-full ${activeTab === "yearly"
                                                ? user.isHarbourClub
                                                    ? "bg-[#3DAEDF] text-[#182059]" // Harbour Club badge colors
                                                    : "bg-[#FFF1F3] text-primary-700" // Default badge colors
                                                : user.isHarbourClub
                                                    ? "bg-[#3DAEDF] text-[#182059]" // Harbour Club non-active badge colors
                                                    : "bg-[#F9FAFB] text-gray-700" // Default non-active badge colors
                                                } border border-[--primary-200] px-3 py-1 text-sm font-medium`}
                                        >
                                            {"Save > 15%"}
                                        </Badge>
                                    )}
                                </div>
                            </TabsTrigger>
                        ))}
                    </TabsList>
                </Tabs>
                <div className="text-sm font-semibold text-gray-600 flex flex-col items-center justify-center">
                    <div>{paymentCopy.pricingPlanSubtext1}</div>
                    <div>{paymentCopy.pricingPlansSubtext2}</div>
                </div>

                <div className="flex flex-wrap justify-center gap-3 sm:gap-4 mt-3 sm:mt-4 overflow-y-auto max-h-[70vh] sm:max-h-[75vh] md:max-h-none pb-1 sm:pb-2 -mx-2 px-2">
                    {plans.map((plan) => (
                        <Card
                            key={plan.id}
                            style={{
                                background: plan.isRecommended
                                    ? user.isHarbourClub
                                        ? paymentStyles.cardBackgroundHarbourRecommended
                                        : paymentStyles.cardBackgroundDefaultRecommended
                                    : "linear-gradient(0deg, #F9FAFB, #F9FAFB)",
                            }}
                            className={clsx(
                                "rounded-xl border border-gray-300 bg-gray-50 overflow-hidden p-3 sm:p-4 flex flex-col flex-grow",
                                getCardWidthClass(plans.length),
                                plans.length === 1 ? "mx-auto" : ""
                            )}
                        >
                            <CardContent className="flex flex-col gap-6 flex-grow p-0 sm:p-2">
                                <div className="flex flex-row justify-between">
                                    <div className="flex flex-col ">
                                        <div className="text-base sm:text-lg font-semibold text-gray-600 flex flex-row gap-4">
                                            <span
                                                className={
                                                    user.isHarbourClub
                                                        ? paymentStyles.gradientHarbour
                                                        : paymentStyles.gradientDefault
                                                }
                                            >
                                                {plan.displayName}
                                            </span>
                                            {plan.isRecommended && (
                                                <Badge
                                                    className={clsx(
                                                        "rounded-full px-3 py-1 text-sm h-[fit-content] font-medium",
                                                        user.isHarbourClub
                                                            ? "bg-[#B9E6FE] text-[#182059]" // Harbour Club styles
                                                            : "bg-pink-50 text-primary-700" // Default styles
                                                    )}
                                                >
                                                    Recommended
                                                </Badge>
                                            )}
                                        </div>

                                        <div className="flex items-baseline">
                                            <span className="text-2xl sm:text-3xl font-semibold text-gray-900">
                                                ${plan.price.toLocaleString()}
                                            </span>
                                            <span
                                                className={`text-sm ml-1 ${user.isHarbourClub
                                                    ? "text-blue-500"
                                                    : "text-gray-500"
                                                    }`}
                                            >
                                                {plan.pricingInterval === PRICING_INTERVAL.MONTH
                                                    ? "/month"
                                                    : "/year"}
                                            </span>
                                        </div>
                                    </div>
                                    {plan.name === SUBSCRIPTION_PLAN_NAME.BUY_SIDE_SEARCH ? (
                                        <SvgSearchIconSubscription
                                            isHarbour={user.isHarbourClub}
                                        />
                                    ) : (
                                        <SvgSourceIconSubscription
                                            isHarbour={user.isHarbourClub}
                                        />
                                    )}
                                </div>
                                <div className="flex text-left flex-col gap-2 content-start">
                                    {plan.features
                                        .filter((feature) => feature.canHighlight)
                                        .map((feature, idx) => (
                                            <div
                                                key={idx}
                                                className="flex text-sm sm:text-base mb-2 flex-row gap-4"
                                            >
                                                <div>
                                                    {renderFeatureIcon({
                                                        lookupKey: feature.lookupKey,
                                                        isHarbour: user?.isHarbourClub,
                                                    })}
                                                </div>
                                                <div className="flex flex-row gap-1 text-left">
                                                    <span className="font-semibold text-gray-600">
                                                        {feature?.capacity?.toLocaleString()}
                                                    </span>
                                                    <span className="text-gray-600">
                                                        {feature.highlightName}
                                                    </span>
                                                    {plan.pricingInterval === PRICING_INTERVAL.MONTH
                                                        ? "/month"
                                                        : "/year"}
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </CardContent>

                            <div className="flex flex-col items-center justify-center mt-3 sm:mt-4 gap-4">
                                <TooltipProvider>
                                    <Button
                                        variant="payment"
                                        className="w-full sm:w-[80%] text-white"
                                        onClick={
                                            plan.displayName === "Source" && !enableSourcePlan
                                                ? () => handleCalendlyInvite()
                                                : planCancelled
                                                    ? () => purchasePlan(plan, false)
                                                    : () => purchasePlan(plan, true)
                                        }
                                    >
                                        <span className="text-white font-semibold text-sm">
                                            {plan.displayName === "Source" && !enableSourcePlan
                                                ? paymentCopy.getEarlyAccess
                                                : planCancelled
                                                    ? paymentCopy.subscribeNow
                                                    : paymentCopy.startTrial}
                                        </span>
                                        {!planCancelled && enableSourcePlan && (
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <span>
                                                            <HelpCircle
                                                                size={16}
                                                                className="text-white cursor-pointer"
                                                            />
                                                        </span>
                                                    </TooltipTrigger>
                                                    <TooltipContent side="top">
                                                        {paymentCopy.trialHelpIconTooltip}
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                        )}
                                    </Button>
                                </TooltipProvider>
                                {!planCancelled &&
                                    (enableSourcePlan || plan.displayName !== "Source") && (
                                        <div className="flex items-left gap-2 flex-col">
                                            <div className="flex flex-row gap-2 items-center">
                                                <button onClick={() => purchasePlan(plan, false)}>
                                                    <div
                                                        className={
                                                            user.isHarbourClub
                                                                ? paymentStyles.textHarbour
                                                                : paymentStyles.textDefault
                                                        }
                                                    >
                                                        {paymentCopy.skiptheTrial}
                                                    </div>
                                                </button>
                                            </div>
                                        </div>
                                    )}
                            </div>

                            <CardFooter className="p-0 sm:p-2 pt-4 sm:pt-6 flex-grow">
                                <div className="space-y-2 sm:space-y-3 w-full">
                                    <h4
                                        className={`text-xs sm:text-sm font-semibold uppercase ${user.isHarbourClub ? "text-blue-500" : "text-gray-500"
                                            }`}
                                    >
                                        {paymentCopy.services}
                                    </h4>
                                    <ul className="flex flex-col gap-4">
                                        {plan.features.map((feature, idx) => (
                                            <li key={idx} className="flex items-center gap-3">
                                                {renderAdditionalFeatureIcon({
                                                    featureName: feature.lookupKey,
                                                    isHarbour: user.isHarbourClub,
                                                })}
                                                <span className="text-sm sm:text-base font-normal ">
                                                    {feature.canHighlight
                                                        ? feature.name
                                                        : formatService(feature.name, feature.capacity)}
                                                </span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </CardFooter>
                        </Card>
                    ))}
                </div>
                <div
                    className="flex justify-end cursor-default"
                >
                    <button
                        className="content-end text-md sm:text-end sm:text-base font-semibold text-blue-600 hover:text-blue-800 underline cursor-pointer"
                        onClick={() => {
                            close();
                            handleLogout();
                        }}>
                        {paymentCopy.logoutTextModal}
                    </button>
                </div>
            </DialogContent>
        </Dialog >
    );
};

export default CreateSubscription;
