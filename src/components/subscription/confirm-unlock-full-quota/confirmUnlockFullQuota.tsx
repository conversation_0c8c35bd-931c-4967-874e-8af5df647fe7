import { Dialog, DialogContent, DialogHeader, DialogTitle } from "components/shadcn/ui/dialog"
import { Button } from "components/shadcn/ui/button"
import lang from "lang"
import SvgCloseIcon from "components/common/iconComponents/closeIcon"

interface UnlockFullQuotaNowModalProps {
    isOpen: boolean,
    onClose: () => void,
    handleUnlockQuota?: () => void;
}

export function UnlockFullQuotaNowModal({ isOpen, onClose, handleUnlockQuota }: UnlockFullQuotaNowModalProps) {
    const { payment: paymentCopy } = lang;

    return (
        <div className="flex z-999 rounded-[32px]">
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="sm:max-w-[40%]">
                    <div className="flex flex-row justify-end">
                        <button onClick={onClose}><SvgCloseIcon /></button>
                    </div>
                    <DialogHeader className="flex flex-row items-center justify-between">
                        <DialogTitle className="text-lg font-semibold text-gray-900">{paymentCopy.readyToUnlockFullQuota}</DialogTitle>
                    </DialogHeader>
                    <div className="text-gray-900 text-sm font-normal">{paymentCopy.unlockFullQuotaNowSubtext}</div>
                    <div className="flex justify-end gap-4">
                        <Button variant="outline" onClick={onClose}>
                            {paymentCopy.stayInTrial}
                        </Button>
                        <Button onClick={handleUnlockQuota} variant="primary" className="bg-[#E6007A] hover:bg-[#E6007A]/90 text-white">
                            {paymentCopy.activateNow}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    )
}

export default UnlockFullQuotaNowModal;
