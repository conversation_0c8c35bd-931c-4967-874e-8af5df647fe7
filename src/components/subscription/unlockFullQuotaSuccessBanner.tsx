import { Alert } from "components/shadcn/ui/alert";
import lang from "lang";

interface UnlockFullQuotaSuccessBannerProps {
    source: string;
}

const UnlockFullQuotaSuccessBanner: React.FC<
    UnlockFullQuotaSuccessBannerProps
> = ({ source }) => {
    const { campaign: campaignCopy } = lang;

    return (
        <div
            className={`w-full flex justify-center items-center ${source === "searchPage"
                    ? "mt-32 mb-[-70px]"
                    : source === "campaign"
                        ? "mb-2"
                        : ""
                }`}
        >
            <Alert
                variant={"success"}
                title={`🎉 ${campaignCopy.endTrialSucessBannerHeader}`}
                description={campaignCopy.endTrialErrorBannerSubtext}
            />
        </div>
    );
};

export default UnlockFullQuotaSuccessBanner;
