import { useState, useRef, useEffect, useMemo, useContext } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  selectCampaignList,
  setCampaignListWithDetails,
  setCampaignType,
} from "../campaignSlice";
import {
  selectCurrentSubscription,
  updateCurrentSubscription,
} from "components/subscription/subscriptionSlice";
import { getCampaignList } from "../services";
import { CAMPAIGN_TYPE, FEATURE_HIGHLIGHT_NAME } from "../types";
import {
  SUBSCRIPTION_DISPLAY_NAME,
  SUBSCRIPTION_STATUS,
} from "components/subscription/types";
import { ParentContext } from "components/constants/ParentContext";
import lang from "lang";
import {
  upgradeTopUp,
  getCurrentSubscription,
} from "components/subscription/services";

export const useCampaignLanding = () => {
  const { campaign: campaignCopy } = lang;
  const context = useContext<any>(ParentContext);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const campaignList = useSelector(selectCampaignList);
  const subscription = useSelector(selectCurrentSubscription);
  const user = JSON.parse(localStorage.getItem("user") || "{}");

  const [selectedOption, setSelectedOption] = useState("option-1");
  const [loading, setLoading] = useState(false);
  const isFetched = useRef(false);
  const [
    showSuccessEndTrialSuccessBanner,
    setShowSuccessEndTrialSuccessBanner,
  ] = useState(false);

  const [showQuotaModal, setShowQuotaModal] = useState(false);
  const [showTopUpSuccessAlert, setShowTopUpSuccessAlert] = useState(false);

  const showSearchPlanBanner =
    subscription.productDisplayName === SUBSCRIPTION_DISPLAY_NAME.SEARCH;

  const showTrialBanner =
    subscription.status === SUBSCRIPTION_STATUS.TRAILING &&
    subscription.productDisplayName === SUBSCRIPTION_DISPLAY_NAME.SOURCE;

  const emailOutreachQuota = useMemo(() => {
    return (
      subscription.userFeatureQuotas?.find(
        (quota) => quota.featureName === FEATURE_HIGHLIGHT_NAME.EMAIL_OUTREACH
      )?.remainingQuota || 0
    );
  }, [subscription]);

  const letterOutreachQuota = useMemo(() => {
    return (
      subscription.userFeatureQuotas?.find(
        (quota) => quota.featureName === FEATURE_HIGHLIGHT_NAME.LETTER_OUTREACH
      )?.remainingQuota || 0
    );
  }, [subscription]);

  const isTrailUserExceddedQouta = useMemo(() => {
    if (!subscription) return false;

    const { status, userFeatureQuotas } = subscription;
    if (status !== SUBSCRIPTION_STATUS.TRAILING) return false;

    const emailOutreachQuota = userFeatureQuotas?.find(
      (quota) => quota.featureName === FEATURE_HIGHLIGHT_NAME.EMAIL_OUTREACH
    );

    const letterOutreachQuota = userFeatureQuotas?.find(
      (quota) => quota.featureName === FEATURE_HIGHLIGHT_NAME.LETTER_OUTREACH
    );

    if (selectedOption === "option-1") {
      return emailOutreachQuota?.remainingQuota === 0;
    } else if (selectedOption === "option-2") {
      return letterOutreachQuota?.remainingQuota === 0;
    } else if (selectedOption === "option-3") {
      return (
        emailOutreachQuota?.remainingQuota === 0 ||
        letterOutreachQuota?.remainingQuota === 0
      );
    }

    return false;
  }, [subscription, selectedOption]);

  useEffect(() => {
    const fetchCampaignList = async () => {
      if (isFetched.current) return;

      try {
        setLoading(true);
        const fetchedCampaignList = await getCampaignList();
        dispatch(setCampaignListWithDetails(fetchedCampaignList));
      } catch (error) {
        console.error("Failed to fetch campaign list:", error);
      } finally {
        setLoading(false);
        isFetched.current = true;
      }
    };

    fetchCampaignList();
  }, [dispatch]);

  const campaignTypeMap: Record<string, CAMPAIGN_TYPE> = {
    "option-1": CAMPAIGN_TYPE.EMAIL,
    "option-2": CAMPAIGN_TYPE.LETTER,
    "option-3": CAMPAIGN_TYPE.BOTH,
  };

  const handleChange = (value: string) => {
    setSelectedOption(value);
  };

  const handleNewCampaign = () => {
    const selectedCampaignType = campaignTypeMap[selectedOption];
    dispatch(setCampaignType(selectedCampaignType));
    navigate("../campaignFlow", {
      state: { campaignType: selectedCampaignType },
    });
  };

  const handleUnlockSuccess = () => {
    setShowSuccessEndTrialSuccessBanner(true);
    setTimeout(() => setShowSuccessEndTrialSuccessBanner(false), 7000);
  };

  const handleTopUpClick = async (topUpDetails: any) => {
    try {
      setLoading(true);
      await upgradeTopUp(topUpDetails);
      const updatedSubscription = await getCurrentSubscription();
      dispatch(updateCurrentSubscription(updatedSubscription));
      return true;
    } catch (error) {
      console.error("Failed to top up:", error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleRadioChange = (value: string) => {
    handleChange(value);
  };

  const handleShowQuotaModal = () => {
    setShowQuotaModal(false);
    setSelectedOption("option-1");
  };

  const handleTopUpSuccess = (topUpDetails: any) => {
    handleTopUpClick(topUpDetails).then(() => {
      setShowQuotaModal(false);
      setShowTopUpSuccessAlert(true);
      setTimeout(() => setShowTopUpSuccessAlert(false), 5000);
    });
  };

  return {
    campaignCopy,
    context,
    campaignList,
    user,
    showTopUpSuccessAlert,
    showQuotaModal,
    showSearchPlanBanner,
    showTrialBanner,
    selectedOption,
    loading,
    showSuccessEndTrialSuccessBanner,
    subscription,
    isTrailUserExceddedQouta,
    emailOutreachQuota,
    letterOutreachQuota,
    navigate,
    handleRadioChange,
    handleTopUpSuccess,
    handleChange,
    handleNewCampaign,
    handleUnlockSuccess,
    handleTopUpClick,
    setShowQuotaModal,
    handleShowQuotaModal,
  };
};
