import React from "react";
import SvgSearchIcon from "components/common/iconComponents/SearchIcon";
import "../../../styling/saved.css";
import { RadioGroup, RadioGroupItem } from "components/shadcn/ui/radio-group";
import { Label } from "components/shadcn/ui/label";
import { Button } from "components/shadcn/ui/button";
import Loader from "components/common/loader";
import UnlockFullQuotaBanner from "components/subscription/unlockFullQoutaBanner";
import UnlockFullQuotaSuccessBanner from "components/subscription/unlockFullQuotaSuccessBanner";
import { useCampaignLanding } from "./useCampaignLanding";
import { Alert } from "components/shadcn/ui/alert";
import UpdateQuotaModal from "components/subscription/update-quota-modal/updateQuotaModal";

interface CampaignLandingProps { }

const CampaignLanding: React.FC<CampaignLandingProps> = () => {
    const {
        campaignCopy,
        context,
        campaignList,
        user,
        showTopUpSuccessAlert,
        showQuotaModal,
        showSearchPlanBanner,
        showTrialBanner,
        selectedOption,
        loading,
        showSuccessEndTrialSuccessBanner,
        subscription,
        isTrailUserExceddedQouta,
        navigate,
        handleRadioChange,
        handleTopUpSuccess,
        handleNewCampaign,
        handleUnlockSuccess,
        handleShowQuotaModal,
    } = useCampaignLanding();



    return (
        <div className="fullScreen">
            <div className="container max-w-[80%]">
                {loading && <Loader />}
                <div className="savedFiltersScreen">
                    <div className="sfTitles flex flex-col gap-5">
                        <div className="display-sm semibold text-gray-900">
                            {campaignCopy.campaigns}
                        </div>
                    </div>

                    {showSearchPlanBanner && (
                        <div className="w-full flex justify-center">
                            <div className="flex items-center justify-between content-center p-4 bg-[--primary-100] text-pink-900 rounded-[24px] w-[75%]">
                                <p className="text-sm font-regular text-gray-950 text-left">
                                    {campaignCopy.promoteSourcePlan}
                                </p>
                                <Button
                                    variant="primary"
                                    className="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 text-sm font-medium rounded-[32px]"
                                    onClick={() => navigate("../billing", { replace: true })}
                                    disabled={loading}
                                >
                                    <span className="text-white font-inter text-sm font-semibold leading-5 text-left underline-from-font">
                                        {campaignCopy.upgrade}
                                    </span>
                                </Button>
                            </div>
                        </div>
                    )}

                    {showTrialBanner && (
                        <UnlockFullQuotaBanner
                            userData={user}
                            currentSubscriptionDetails={subscription}
                            source="CampaignLanding"
                            onUnlockSuccess={handleUnlockSuccess}
                        />
                    )}

                    {showSuccessEndTrialSuccessBanner && (
                        <UnlockFullQuotaSuccessBanner source="CampaignLanding" />
                    )}

                    {showTopUpSuccessAlert && (
                        <Alert
                            variant={"success"}
                            title={campaignCopy.quotaIncreasedSucesfully}
                        />
                    )}

                    {showQuotaModal && (
                        <UpdateQuotaModal
                            open={showQuotaModal}
                            close={handleShowQuotaModal}
                            handleTopUpClick={handleTopUpSuccess}
                            changePlan={() => navigate("../billing", { replace: true })}
                            showEmailQuotaBlock={false}
                            showLetterQuotaBlock={true}
                        />
                    )}

                    {!loading && (
                        <div className="w-full flex flex-col gap-8 items-center">
                            <div className="flex align-center justify-center w-12 h-12 p-3 pt-3 gap-0 rounded-[10px] border border-gray-200 bg-white shadow-sm">
                                <SvgSearchIcon />
                            </div>

                            <div className="flex flex-col gap-2">
                                {campaignList.length === 0 && (
                                    <div className="font-[InstrumentSans] text-lg font-semibold leading-7 text-center text-gray-900">
                                        {campaignCopy.noCampaignsHeading}
                                    </div>
                                )}
                                <div className="font-inter text-sm font-normal leading-5 text-center text-gray-600">
                                    {campaignCopy.campaignOptionText}
                                </div>
                            </div>

                            <RadioGroup
                                value={selectedOption}
                                onValueChange={handleRadioChange}
                                defaultValue="option-one"
                            >
                                <div className="flex gap-2 flex-col">
                                    {campaignCopy.campaignOptionType.map((option) => {
                                        //const isDisabled =
                                        // subscription?.status === SUBSCRIPTION_STATUS.TRAILING;
                                        const optionValue = `option-${option.id}`;
                                        //const isOptionDisabled = isDisabled && optionValue !== "option-1";
                                        const isSelected = selectedOption === optionValue;

                                        const borderColor = isSelected
                                            ? context.isHarbour
                                                ? "border-[#3DAEDF]"
                                                : "border-[#C01048]"
                                            : "border-[#F2F4F7]";

                                        return (
                                            <div
                                                key={option.id}
                                                className={`w-[352px] h-[74px] p-[16px] gap-1 bg-white rounded-[12px] flex items-center justify-between border-2 
                                                    ${borderColor}`}
                                                onClick={() => { handleRadioChange(optionValue) }}
                                            >
                                                <div className="flex flex-col gap-1 items-start">
                                                    <Label
                                                        htmlFor={optionValue}
                                                        className="text-sm font-bold text-[#344054] text-left"
                                                    >
                                                        {option.label}
                                                        {/* {isOptionDisabled && campaignCopy.activateYourSubscription} */}
                                                    </Label>
                                                </div>
                                                <RadioGroupItem
                                                    value={optionValue}
                                                    id={optionValue}
                                                    className="mr-4 flex items-center justify-center"
                                                    //disabled={isOptionDisabled}
                                                    isHarbour={context.isHarbour}
                                                />
                                            </div>
                                        );
                                    })}
                                </div>
                            </RadioGroup>

                            <Button
                                variant="primary"
                                onClick={handleNewCampaign}
                                disabled={isTrailUserExceddedQouta}
                            >
                                <div className="text-white font-inter text-sm font-semibold leading-5 text-left underline-from-font">
                                    {campaignCopy.newCampaign}
                                </div>
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default CampaignLanding;
