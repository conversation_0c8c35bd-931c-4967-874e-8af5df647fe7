import { CurrentSubscriptionDetails } from "components/subscription/types";

type Address = {
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  postcode: string;
  country: string;
};

export type CreateCampaignDto = {
  name: string;
  type: string;
};

export type CreateCampaignResponseDto = {
  createdAt: string;
  currentStep: number;
  id: string;
  name: string;
  status: string;
  type: string;
  updatedAt: string;
};

export type SaveCollectionIdsDTO = {
  collectionIds: string[];
};

export type UserUpdateRequestDTO = {
  holdingCompany: string;
  phoneNumber: string;
  website: string;
  address: Address;
  notifiedOn?: string;
};

export type EmailTemplateRequestDTO = {
  body: string;
  sendAfterDays: number;
  subject: string;
};

export type SaveEmailTemplatesPayload = {
  emails: EmailTemplateRequestDTO[];
};

export type SaveLetterTemplatePayload = {
  deafult: string;
};

export type MailboxRequestDTO = {
  preferredDomain: string;
};

export interface CampaignSummaryDto {
  emailsToSend: number;
  creditsBalance: number;
  creditsToUse: number;
  creditsLeft: number;
  companiesWithoutContacts: string[];
  domainStatus: string;
  totalCollections: number;
  totalRecipients: number;
  emailsPerWorkflow: number;
  letterRecipients: number;
  emailRecipients: number;
}

export type CampaignList = {
  id: string;
  name: string;
  company: string;
  status: string;
  recipients: number;
  type: string;
  replies: string;
  date: string;
  actions: string;
  emailStatus?: EMAIL_STATUS;
  letterStatus?: LETTER_STATUS;
};

export type updateCollectionDetailsRequest = {
  name: string;
  description: string;
  exportedOn: string;
};

type Collection = {
  id: string;
  name: string;
  description: string;
};

type Message = {
  subject: string;
  body: string;
  sendAfterDays: number;
};

type DomainSettings = {
  id: string;
  preferredDomain: string;
  domain: string;
  mailboxes: string[];
  createdAt: string;
  expireAt: string;
  updatedAt: string;
  status: string;
};

export type GetStepsParams = {
  campaignType: string | null | undefined;
  currentSubscription: CurrentSubscriptionDetails;
  existingDomainDetails: any;
};

export type CampaignDetails = {
  id: string;
  name: string;
  type: string;
  currentStep: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  collections: Collection[];
  messages: Message[];
  domainSettings: DomainSettings;
};

export enum STATUS {
  SENT = "sent",
  DRAFT = "draft",
  WARMING_UP = "warming-up",
  WARMING = "warming",
  ERROR = "error",
  PAUSED = "paused",
  COMPLETED = "completed",
  RUNNING = "running",
  QUEUED = "queued",
}

export enum DOMAIN_STATUS {
  DOMAIN_READY = "domain_ready",
  SET_PREFERRED = "set_preferred",
  DOMAIN_PURCHASE_FAILED = "domain_purchase_failed",
  DOMAIN_PURCHASED_QUEUED = "domain_purchase_queued",
  DOMAIN_ADDITION_FAILED = "domain_addition_failed",
  DOMAIN_VERIFICATION_FAILED = "domain_verification_failed",
  DOMAIN_MX_DKIM_ADDITION_FAILED = "domain_mx_dkim_addition_failed",
  DOMAIN_MX_DKIM_VERIFICATION_FAILED = "domain_mx_dkim_verification_failed",
  MAIL_HOSTING_USER_CREATION_FAILED = "mail_hosting_user_creation_failed",
  INSTANTLY_USER_CREATION_FAILED = "instantly_user_creation_failed",
}

export enum CAMPAIGN_TYPE {
  EMAIL = "email",
  LETTER = "letter",
  BOTH = "both",
}

export enum CAMPAIGN_STEP {
  SELECT_COLLECTION = "Select Collection",
  CUSTOMISE = "Customise",
  EDIT_EMAIL = "Edit Email",
  EDIT_LETTER = "Edit Letter",
  MAILBOXES = "Mailboxes",
  SUMMARY = "Summary",
}

type Stat = {
  label: string;
  value: string;
};

type CombinedStat = {
  combined: true;
  lines: {
    label: string;
    value: string;
  }[];
};

export type FinalStat = Stat | CombinedStat;

export enum EMAIL_STATUS {
  DRAFT = "Draft",
  ACTIVE = "Active",
  PAUSED = "Paused",
  COMPLETED = "Completed",
  RUNNING_SUBSEQUENCES = "Running Subsequences",
  ACCOUNT_SUSPENDED = "Account Suspended",
  ACCOUNTS_UNHEALTHY = "Accounts Unhealthy",
  BOUNCE_PROTECT = "Bounce Protect",
}

export enum LETTER_STATUS {
  DRAFT = "draft",
  APPROVED = "approved",
  PROVISIONED = "provisioned",
  SCHEDULED = "scheduled",
  RUNNING = "running",
  PAUSED = "paused",
  COMPLETE = "complete",
}

export enum FEATURE_HIGHLIGHT_NAME {
  EMAIL_OUTREACH = "Email Outreach",
  LETTER_OUTREACH = "Letter Outreach",
  CollectionS = "Collections",
}

export interface Step {
  id: number;
  name: string;
}

export interface CampaignFormState {
  isFormValid: boolean;
  customisationsData: any;
  isAddingCollectionError: boolean;
  letterTemplateError: boolean;
  letterTemplateErrorMessage: string;
  showStepError: boolean;
  showApiError: string;
}

export interface CampaignUIState {
  isEditing: boolean;
  isLoading: boolean;
  showCampaignNameModal: boolean;
  discardModal: boolean;
  generatedCampaignName: string;
  errorMessage: boolean;
  showConfirmUnlockFullQuotaModal: boolean;
  showSuccessEndTrialSuccessBanner: boolean;
}

export interface EmailTemplate {
  subject: string;
  body: string;
  sendAfterDays: number;
}
