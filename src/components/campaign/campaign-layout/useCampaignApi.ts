import { useState } from "react";
import {
  createNewCampaign,
  updateExistingCampaign,
  saveCollectionId,
  saveCustomisations,
  saveEmailTemplates,
  saveLetterTemplate,
  saveMailBoxDataToApi,
  saveCampaign,
  getAccountDetails,
  getEmailTemplates,
  getLetterTemplate,
} from "../services";
import { useDispatch } from "react-redux";
import {
  setCreateCampaignResponse,
  setEmailTemplates,
  setLetterTemplate,
} from "../campaignSlice";
import { EmailTemplate, CAMPAIGN_STEP } from "../types";
import { useCampaignAnalytics } from "./useCampaignAnalytics";

export const useCampaignAPI = () => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    apiError: "",
    collectionError: false,
    letterTemplateError: false,
    letterTemplateErrorMessage: "",
    showStepError: false,
  });

  const userData = JSON.parse(localStorage.getItem("user") || "{}");
  const { trackStepError } = useCampaignAnalytics(userData);

  const createOrUpdateCampaign = async (
    campaignId: string | undefined,
    name: string,
    campaignType = ""
  ) => {
    try {
      setIsLoading(true);
      let data;

      if (campaignId) {
        data = await updateExistingCampaign(campaignId, name);
      } else {
        const payload = { name, type: campaignType };
        data = await createNewCampaign(payload);
      }

      if (data) {
        dispatch(setCreateCampaignResponse(data));
        setErrors({ ...errors, apiError: "" });
        return { success: true, data };
      }
      return { success: false };
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || "An error occurred";
      setErrors({ ...errors, apiError: errorMessage });
      trackStepError(errorMessage, "Campaign Creation");
      return {
        success: false,
        error: errorMessage,
        isNameError: errorMessage === "Campaign with this name already exists",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const saveCollectionIdsToAPI = async (
    collectionIds: string[],
    campaignId: string
  ) => {
    try {
      setIsLoading(true);
      const data = await saveCollectionId({ collectionIds }, campaignId);
      if (data) {
        setErrors({
          ...errors,
          collectionError: false,
          showStepError: false,
          apiError: "",
        });
        return { success: true };
      }
      return { success: false };
    } catch (error: any) {
      setIsLoading(false);
      if (error?.response?.data?.statusCode === 400) {
        setErrors({
          ...errors,
          collectionError: true,
          showStepError: false,
          apiError: "",
        });
        trackStepError(
          error?.response?.data?.message,
          CAMPAIGN_STEP.SELECT_COLLECTION
        );
      } else {
        const errorMessage =
          error?.response?.data?.message || "An error occurred";
        setErrors({
          ...errors,
          showStepError: true,
          apiError: errorMessage,
        });
        trackStepError(errorMessage, CAMPAIGN_STEP.SELECT_COLLECTION);
      }
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const saveCustomisationsToAPI = async (customisationsData: any) => {
    try {
      setIsLoading(true);
      const data = await saveCustomisations(customisationsData);
      if (data) {
        const userData = await getAccountDetails();
        if (userData) {
          localStorage.setItem("user", JSON.stringify(userData));
        }
        setErrors({ ...errors, showStepError: false, apiError: "" });
        return { success: true };
      }
      return { success: false };
    } catch (error: any) {
      setErrors({
        ...errors,
        showStepError: true,
        apiError: error?.response?.data?.message || "An error occurred",
      });
      trackStepError(
        error?.response?.data?.message,
        CAMPAIGN_STEP.SELECT_COLLECTION
      );
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const saveEmailTemplatesToAPI = async (
    emailTemplates: EmailTemplate[],
    campaignId: string
  ) => {
    try {
      setIsLoading(true);
      const payload = {
        emails: emailTemplates.map((template) => ({
          subject: template.subject,
          body: template.body,
          sendAfterDays: template.sendAfterDays,
        })),
      };

      const data = await saveEmailTemplates(payload, campaignId);
      if (data) {
        setErrors({ ...errors, showStepError: false, apiError: "" });
        return { success: true };
      }
      return { success: false };
    } catch (error: any) {
      setErrors({
        ...errors,
        showStepError: true,
        apiError: error?.response?.data?.message || "An error occurred",
      });
      trackStepError(error?.response?.data?.message, CAMPAIGN_STEP.EDIT_EMAIL);
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const saveLetterTemplateToAPI = async (
    letterTemplate: any,
    campaignId: string
  ) => {
    try {
      setIsLoading(true);
      const data = await saveLetterTemplate(letterTemplate, campaignId);
      if (data) {
        setErrors({
          ...errors,
          letterTemplateError: false,
          letterTemplateErrorMessage: "",
        });
        return { success: true };
      }
      return { success: false };
    } catch (error: any) {
      setErrors({
        ...errors,
        letterTemplateError: true,
        letterTemplateErrorMessage:
          error?.response?.data?.message || "An error occurred",
      });
      trackStepError(error?.response?.data?.message, CAMPAIGN_STEP.EDIT_LETTER);
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const saveMailboxDataToAPI = async (mailboxData: any, campaignId: string) => {
    try {
      setIsLoading(true);
      const data = await saveMailBoxDataToApi(mailboxData, campaignId);
      if (data) {
        setErrors({ ...errors, showStepError: false, apiError: "" });
        return { success: true };
      }
      return { success: false };
    } catch (error: any) {
      setErrors({
        ...errors,
        showStepError: true,
        apiError: error?.response?.data?.message || "An error occurred",
      });
      trackStepError(error?.response?.data?.message, CAMPAIGN_STEP.MAILBOXES);
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const finalizeCampaign = async (campaignId: string) => {
    try {
      setIsLoading(true);
      const data = await saveCampaign(campaignId);
      if (data) {
        return { success: true };
      }
      return { success: false };
    } catch (error: any) {
      return {
        success: false,
        error: error?.response?.data?.message || "An error occurred",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const fetchEmailTemplatesFromAPI = async () => {
    try {
      setIsLoading(true);
      const emailTemplateData = await getEmailTemplates();
      if (emailTemplateData) {
        dispatch(setEmailTemplates(emailTemplateData));
        return { success: true, data: emailTemplateData };
      }
      return { success: false };
    } catch (error) {
      console.error("Error fetching email templates:", error);
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const fetchLetterTemplateFromAPI = async () => {
    try {
      setIsLoading(true);
      const letterTemplateResponse = await getLetterTemplate();
      if (letterTemplateResponse) {
        dispatch(setLetterTemplate(letterTemplateResponse));
        return { success: true, data: letterTemplateResponse };
      }
      return { success: false };
    } catch (error) {
      console.error("Error fetching letter template:", error);
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    errors,
    createOrUpdateCampaign,
    saveCollectionIdsToAPI,
    saveCustomisationsToAPI,
    saveEmailTemplatesToAPI,
    saveLetterTemplateToAPI,
    saveMailboxDataToAPI,
    finalizeCampaign,
    fetchEmailTemplatesFromAPI,
    fetchLetterTemplateFromAPI,
  };
};
