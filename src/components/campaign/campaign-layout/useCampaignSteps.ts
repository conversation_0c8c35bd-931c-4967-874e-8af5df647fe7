import { SUBSCRIPTION_STATUS } from "components/subscription/types";
import { CAMPAIGN_STEP, CAMPAIGN_TYPE, GetStepsParams, Step } from "../types";

export const useCampaignSteps = () => {
  const getSteps = ({
    campaignType,
    currentSubscription,
    existingDomainDetails,
  }: GetStepsParams): Step[] => {
    const isEmail = campaignType === CAMPAIGN_TYPE.EMAIL;
    const isLetter = campaignType === CAMPAIGN_TYPE.LETTER;
    const isBoth = campaignType === CAMPAIGN_TYPE.BOTH;

    if (isBoth) {
      const steps = [
        { id: 1, name: CAMPAIGN_STEP.SELECT_COLLECTION },
        { id: 2, name: CAMPAIGN_STEP.CUSTOMISE },
        { id: 3, name: CAMPAIGN_STEP.EDIT_EMAIL },
        { id: 4, name: CAMPAIGN_STEP.EDIT_LETTER },
      ];

      if (
        currentSubscription.status === SUBSCRIPTION_STATUS.TRAILING ||
        !existingDomainDetails?.length
      ) {
        steps.push({ id: 5, name: CAMPAIGN_STEP.MAILBOXES });
      }

      steps.push({ id: 6, name: CAMPAIGN_STEP.SUMMARY });

      return steps;
    }

    const stepsList = [
      { id: 1, name: CAMPAIGN_STEP.SELECT_COLLECTION },
      { id: 2, name: CAMPAIGN_STEP.CUSTOMISE },
    ];

    if (isEmail) {
      stepsList.push({ id: 3, name: CAMPAIGN_STEP.EDIT_EMAIL });

      if (
        currentSubscription.status === SUBSCRIPTION_STATUS.TRAILING ||
        !existingDomainDetails?.length
      ) {
        stepsList.push({ id: 4, name: CAMPAIGN_STEP.MAILBOXES });
      }

      stepsList.push({ id: 5, name: CAMPAIGN_STEP.SUMMARY });
    }

    if (isLetter) {
      stepsList.push({ id: 3, name: CAMPAIGN_STEP.EDIT_LETTER });
      stepsList.push({ id: 4, name: CAMPAIGN_STEP.SUMMARY });
    }

    return stepsList;
  };

  const getNameOfStep = (currentStep: number): string => {
    switch (currentStep) {
      case 1:
        return "Select Collection";
      case 2:
        return "Customise";
      case 3:
        return "Edit Email";
      case 4:
        return "Edit Letter";
      case 5:
        return "Mailboxes";
      case 6:
        return "Summary";
      default:
        return "Unknown Step";
    }
  };

  return {
    getSteps,
    getNameOfStep,
  };
};
