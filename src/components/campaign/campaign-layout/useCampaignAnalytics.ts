import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import type { Dict } from "mixpanel-browser";
import { useCallback } from "react";

export const useCampaignAnalytics = (userData: any) => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const mixpanelProps: Dict = {
    $name: `${userData?.name || ""}`,
    $email: userData?.email || "",
  };

  const trackStepNavigation = (
    direction: "Save and Proceed" | "Back" | "Stepper Jump",
    stepName: string,
    stepData?: any
  ) => {
    if (!userData?.uid) return;

    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        ...(stepName === "Select Collection" && {
          NoOfCollectionAttchedToCampaign: stepData?.length || 0,
        }),
        ...(stepName === "Mailboxes" && {
          prefereedDomainByUser: stepData?.preferredDomain,
          replyToEmail: stepData?.replyTo,
        }),
        [direction]: stepName,
      },
      id: userData.uid.toString(),
      eventName:
        direction === "Save and Proceed"
          ? MixpanelEventName.userClicksSaveAndProceedOnCampaign
          : MixpanelEventName.userClicksBack,
    });
  };

  const trackCampaignNameSave = () => {
    if (!userData?.uid) return;

    mixpanelCustomEvent({
      mixpanelProps,
      id: userData.uid.toString(),
      eventName: MixpanelEventName.saveCampaignName,
    });
  };

  const trackCampaignStart = () => {
    if (!userData?.uid) return;

    mixpanelCustomEvent({
      mixpanelProps,
      id: userData.uid.toString(),
      eventName: MixpanelEventName.userClicksStartCampaign,
    });
  };

  const trackCampaignSuccess = () => {
    if (!userData?.uid) return;

    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        Success:
          "Campaign Created Successfully and user navigated to campaign list",
      },
      id: userData.uid.toString(),
      eventName: MixpanelEventName.campaignCreationSuccess,
    });
  };

  const trackCampaignError = useCallback(
    (errorMessage: string, stepName?: string) => {
      if (!userData?.uid) return;

      mixpanelCustomEvent({
        mixpanelProps: {
          ...mixpanelProps,
          Error: errorMessage,
          Timestamp: new Date().toISOString(),
        },
        id: userData.uid.toString(),
        eventName: MixpanelEventName.errorOccuredWhileCreatingCampaign,
      });
    },
    [userData, mixpanelProps]
  );

  const trackStepError = (errorMessage: string, stepName: string) => {
    if (!userData?.uid) return;

    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        Step: stepName,
        ErrorMessage: errorMessage,
        Timestamp: new Date().toISOString(),
      },
      id: userData.uid.toString(),
      eventName: MixpanelEventName.userGetsStepError,
    });
  };

  const trackUserLeavesCampaign = (stepName: string) => {
    if (!userData?.uid) return;

    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        SaveAsDraft: stepName,
      },
      id: userData.uid.toString(),
      eventName: MixpanelEventName.userLeavesCampaign,
    });
  };

  return {
    trackStepNavigation,
    trackCampaignNameSave,
    trackCampaignStart,
    trackCampaignSuccess,
    trackCampaignError,
    trackStepError,
    trackUserLeavesCampaign,
  };
};
