import type React from "react";

import { useState, useRef, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import {
  selectCampaignName,
  selectedCollection,
  selectCurrentStep,
  selectCreateCampaignResponse,
  selectEmailTemplate,
  selectMailboxData,
  updateMailboxData,
  resetCampaignState,
  selectCampaignList,
  setCampaignId,
  selectCampaignType,
  selectLetterTemplate,
} from "../campaignSlice";
import {
  CAMPAIGN_TYPE,
  DOMAIN_STATUS,
  type CampaignFormState,
  type CampaignUIState,
} from "../types";
import { selectCurrentSubscription } from "components/subscription/subscriptionSlice";
import { generateCampaignName } from "helpers";

export const useCampaignState = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const fromDraftState = location.state?.fromDraftState || false;
  const draftCampaignId = location.state?.campaignId;
  const campaignType = useSelector(selectCampaignType);
  const currentSubscription = useSelector(selectCurrentSubscription);
  const currentStep = useSelector(selectCurrentStep);
  const selectedCollectionIds = useSelector(selectedCollection);
  const alreadyCampaignName = useSelector(selectCampaignName);
  const campaignCreated = useSelector(selectCreateCampaignResponse);
  const emailMessage = useSelector(selectEmailTemplate);
  const mailboxData = useSelector(selectMailboxData);
  const existingCampaignNames = useSelector(selectCampaignList);
  const letterTemplate = useSelector(selectLetterTemplate);

  const existingDomainDetails = JSON.parse(
    localStorage.getItem("domainSettings") || "[]"
  );

  const userData = JSON.parse(localStorage.getItem("user") || "{}");

  const [uiState, setUIState] = useState<CampaignUIState>({
    isEditing: false,
    isLoading: false,
    showCampaignNameModal: false,
    discardModal: false,
    generatedCampaignName: "",
    errorMessage: false,
    showConfirmUnlockFullQuotaModal: false,
    showSuccessEndTrialSuccessBanner: false,
  });

  const [formState, setFormState] = useState<CampaignFormState>({
    isFormValid: false,
    customisationsData: {},
    isAddingCollectionError: false,
    letterTemplateError: false,
    letterTemplateErrorMessage: "",
    showStepError: false,
    showApiError: "",
  });

  const [name, setName] = useState(alreadyCampaignName);
  const inputRef = useRef<HTMLInputElement>(null);

  const campaignIdForCampaign = fromDraftState
    ? draftCampaignId
    : campaignCreated?.id;

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  useEffect(() => {
    if (campaignIdForCampaign) {
      dispatch(setCampaignId(campaignIdForCampaign as string));
    }
  }, [dispatch, campaignIdForCampaign]);

  const disableStartCampaign =
    !existingDomainDetails.every((domain: any) =>
      [DOMAIN_STATUS.DOMAIN_READY, DOMAIN_STATUS.SET_PREFERRED].includes(
        domain.status
      )
    ) && campaignType !== CAMPAIGN_TYPE.LETTER;

  useEffect(() => {
    if (alreadyCampaignName === "New Campaign") {
      const campaignNames = existingCampaignNames.map(
        (campaign: any) => campaign.name
      );

      const newCampaignName = generateCampaignName(campaignNames);

      setUIState((prev) => ({
        ...prev,
        showCampaignNameModal: true,
        generatedCampaignName: newCampaignName,
      }));
    } else {
      setUIState((prev) => ({
        ...prev,
        generatedCampaignName: alreadyCampaignName,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!fromDraftState) {
      dispatch(resetCampaignState());
    }
  }, [dispatch, fromDraftState]);

  useEffect(() => {
    scrollToTop();
  }, [currentStep, scrollToTop]);

  const updateUIState = (updates: Partial<CampaignUIState>) => {
    setUIState((prev) => ({ ...prev, ...updates }));
  };

  const updateFormState = useCallback((updates: Partial<CampaignFormState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  }, []);

  const handleFormSubmit = useCallback(
    (data: any) => {
      updateFormState({ customisationsData: data });
    },
    [updateFormState]
  );

  const handleEditCampaignName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
  };

  const handleEditClick = () => {
    updateUIState({ isEditing: true });
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(0, inputRef.current.value.length);
      }
    }, 0);
  };

  const handleCampaignNameModalClose = () => {
    updateUIState({ showCampaignNameModal: false });
  };

  const handleMailboxData = (data: any) => {
    dispatch(updateMailboxData(data));
  };

  const isSaveAndProceedButtonDisabled = () => {
    if (currentStep === 1)
      return selectedCollectionIds?.length === 0 || !formState.isFormValid;
    if (currentStep === 2) return !formState.isFormValid;
    if (currentStep === 5) return !formState.isFormValid;
    if (currentStep === 6) return !formState.isFormValid;
    return false;
  };

  return {
    campaignType,
    currentSubscription,
    currentStep,
    selectedCollectionIds,
    alreadyCampaignName,
    campaignCreated,
    emailMessage,
    mailboxData,
    existingCampaignNames,
    letterTemplate,
    existingDomainDetails,
    userData,
    name,
    inputRef,
    campaignIdForCampaign,
    disableStartCampaign,
    fromDraftState,
    uiState,
    formState,
    dispatch,
    navigate,
    updateFormState,
    updateUIState,
    setName,
    handleEditCampaignName,
    handleEditClick,
    handleCampaignNameModalClose,
    handleFormSubmit,
    handleMailboxData,
    isSaveAndProceedButtonDisabled,
    scrollToTop,
  };
};
