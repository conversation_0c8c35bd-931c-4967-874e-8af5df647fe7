import lang from "lang";
import type React from "react";
import { useCampaignLayout } from "./useCampaignLayout";
import { CAMPAIGN_STEP } from "../types";
import SelectCollection from "../campaign-steps/select-collection/selectCollection";
import SetupCustomisations from "../campaign-steps/setup-customisations/setupCustmisations";
import EmailEditor from "../campaign-steps/edit-message/editMessage";
import EditLetter from "../campaign-steps/edit-letter/editLetter";
import SetUpMailbox from "../campaign-steps/setup-mailbox/setupMailbox";
import CampaignSummary from "../campaign-steps/summary/summary";
import CampaignNameModal from "components/popUps/editCampiagnNamePopUp";
import Loader from "components/common/loader";
import DiscardCampaignSetup from "components/popUps/discardCampaignSetupPopup";
import SvgEditPencilIcon from "components/common/iconComponents/editPencilIcon";
import Stepper from "../stepper";
import { SUBSCRIPTION_STATUS } from "components/subscription/types";
import UnlockFullQuotaBanner from "components/subscription/unlockFullQoutaBanner";
import UnlockFullQuotaSuccessBanner from "components/subscription/unlockFullQuotaSuccessBanner";
import { Alert } from "components/shadcn/ui/alert";
import { Button } from "components/shadcn/ui/button";

const CampaignLayout: React.FC = () => {
    const { campaign: campaignCopy } = lang;
    const {
        userData,
        steps,
        currentStep,
        name,
        isLoading,
        showCampaignNameModal,
        discardModal,
        generatedCampaignName,
        errorMessage,
        currentSubscription,
        disableStartCampaign,
        letterTemplateError,
        letterTemplateErrorMessage,
        showSuccessEndTrialSuccessBanner,
        isAddingCollectionError,
        showStepError,
        showApiError,
        setShowSuccessEndTrialSuccessBanner,
        setShowCampaignNameModal,
        handleSave,
        handleFormSubmit,
        handleSaveAndProceedClick,
        handleBackClick,
        handleExitClick,
        handleSaveAndExitClick,
        isSaveAndProceedButtonDisabled,
        setIsFormValid,
        handleMailboxData,
        setDiscardModal,
        handleStepClick,
    } = useCampaignLayout();

    const isLastStep = currentStep === steps[steps.length - 1]?.id;

    const fulfilledSteps = steps
        .filter((step) => step.id < currentStep)
        .map((step) => step.id);

    const renderStepContent = () => {
        const currentStepName = steps.find((step) => step.id === currentStep)?.name;

        switch (currentStepName) {
            case CAMPAIGN_STEP.SELECT_COLLECTION:
                return <SelectCollection setIsFormValid={setIsFormValid} />;

            case CAMPAIGN_STEP.CUSTOMISE:
                return (
                    <SetupCustomisations
                        onFormSubmit={handleFormSubmit}
                        setIsFormValid={setIsFormValid}
                    />
                );

            case CAMPAIGN_STEP.EDIT_EMAIL:
                return <EmailEditor />;

            case CAMPAIGN_STEP.EDIT_LETTER:
                return <EditLetter />;

            case CAMPAIGN_STEP.MAILBOXES:
                return (
                    <SetUpMailbox
                        setIsFormValid={setIsFormValid}
                        onDataSubmit={handleMailboxData}
                    />
                );

            case CAMPAIGN_STEP.SUMMARY:
                return <CampaignSummary setIsFormValid={setIsFormValid} />;

            default:
                return null;
        }
    };

    return (
        <div className="fullScreen">
            {isLoading && <Loader />}
            <CampaignNameModal
                errorMessage={errorMessage}
                isOpen={showCampaignNameModal}
                onClose={() => setShowCampaignNameModal(false)}
                onSave={(name) => handleSave(name)}
                value={name !== "New Campaign" ? name : generatedCampaignName}
            />
            <DiscardCampaignSetup
                currentStep={currentStep}
                isOpen={discardModal}
                onClose={() => setDiscardModal(false)}
                onSaveAndExit={handleSaveAndExitClick}
            />
            <div className="container max-w-[80%]">
                <div className={`savedFiltersScreen pb-0`}>
                    <div className="sfTitles flex flex-col gap-5">
                        <div className="display-sm semibold text-gray-900 flex flex-row align-center gap-2">
                            {name}
                            {currentStep === 1 && (
                                <button onClick={() => setShowCampaignNameModal(true)}>
                                    <SvgEditPencilIcon />
                                </button>
                            )}
                        </div>
                    </div>
                    <div className="flex flex-col gap-6 w-full h-full">
                        <Stepper
                            currentStep={currentStep}
                            steps={steps}
                            fulfilledSteps={fulfilledSteps}
                            onStepClick={handleStepClick}
                        />                        <div>
                            {currentSubscription.status === SUBSCRIPTION_STATUS.TRAILING && (
                                <UnlockFullQuotaBanner
                                    userData={userData}
                                    currentSubscriptionDetails={currentSubscription}
                                    source="campaign"
                                    onUnlockSuccess={() => {
                                        setShowSuccessEndTrialSuccessBanner(true);
                                        setTimeout(
                                            () => setShowSuccessEndTrialSuccessBanner(false),
                                            7000
                                        );
                                    }}
                                />
                            )}
                            {showSuccessEndTrialSuccessBanner && (
                                <UnlockFullQuotaSuccessBanner source="campaign" />
                            )}
                            {letterTemplateError && (
                                <Alert
                                    variant="destructive"
                                    title={letterTemplateErrorMessage}
                                />
                            )}
                            {showStepError && (
                                <Alert variant="destructive" title={showApiError} />
                            )}
                            {isAddingCollectionError && (
                                <Alert
                                    variant="destructive"
                                    title={campaignCopy.lockedCollectionError}
                                />
                            )}
                            <div className="max-h-[70%] bg-[#F2F4F7] p-14 pt-10">
                                {renderStepContent()}
                            </div>
                        </div>
                        <div className="flex flex-row justify-between gap-6">
                            <Button
                                variant="secondary"
                                className="bg-[white]"
                                onClick={handleExitClick}
                            >
                                {currentStep === 1 ? campaignCopy.saveAndExit : campaignCopy.exitWithoutSaving}
                            </Button>
                            <div className="flex flex-row justify-end gap-6">
                                {currentStep !== 1 && (
                                    <Button
                                        variant="secondary"
                                        className="bg-[white]"
                                        onClick={handleBackClick}
                                        disabled={currentStep === 1}
                                    >
                                        {campaignCopy.back}
                                    </Button>
                                )}
                                <Button
                                    variant="primary"
                                    onClick={handleSaveAndProceedClick}
                                    disabled={
                                        currentStep === 3
                                            ? false
                                            : isSaveAndProceedButtonDisabled() ||
                                            (isLastStep && disableStartCampaign)
                                    }
                                >
                                    {" "}
                                    <div className="text-white font-inter text-sm font-semibold leading-5 text-left underline-from-font">
                                        {isLastStep
                                            ? campaignCopy.startCampaign
                                            : campaignCopy.saveAndProceed}
                                    </div>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CampaignLayout;
