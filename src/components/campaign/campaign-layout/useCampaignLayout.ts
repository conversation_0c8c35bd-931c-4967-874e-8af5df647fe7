import { useEffect } from "react";

import { useCampaignSteps } from "./useCampaignSteps";
import { useCampaignAnalytics } from "./useCampaignAnalytics";
import { CAMPAIGN_STEP } from "../types";
import { useCampaignState } from "./useCampaignState";
import { useCampaignAPI } from "./useCampaignApi";
import { scrollToTop } from "helpers";
import { SUBSCRIPTION_STATUS } from "components/subscription/types";
import { fetchCollections } from "components/collection/services";
import { setCollections } from "components/collection/collectionSlice";

export const useCampaignLayout = () => {
  const {
    campaignType,
    currentSubscription,
    currentStep,
    selectedCollectionIds,
    emailMessage,
    mailboxData,
    letterTemplate,
    existingDomainDetails,
    userData,
    name,
    campaignIdForCampaign,
    disableStartCampaign,
    fromDraftState,
    uiState,
    formState,

    dispatch,
    navigate,
    updateUIState,
    setName,
    updateFormState,
    handleEditCampaignName,
    handleEditClick,
    handleCampaignNameModalClose,
    handleFormSubmit,
    handleMailboxData,
    isSaveAndProceedButtonDisabled,
  } = useCampaignState();

  const {
    isLoading,
    errors,
    createOrUpdateCampaign,
    saveCollectionIdsToAPI,
    saveCustomisationsToAPI,
    saveEmailTemplatesToAPI,
    saveLetterTemplateToAPI,
    saveMailboxDataToAPI,
    finalizeCampaign,
    fetchEmailTemplatesFromAPI,
    fetchLetterTemplateFromAPI,
  } = useCampaignAPI();

  const { getSteps, getNameOfStep } = useCampaignSteps();

  const {
    trackStepNavigation,
    trackCampaignNameSave,
    trackCampaignStart,
    trackCampaignSuccess,
    trackCampaignError,
    trackUserLeavesCampaign,
  } = useCampaignAnalytics(userData);

  useEffect(() => {
    if (!fromDraftState) {
      fetchEmailTemplatesFromAPI();
      fetchLetterTemplateFromAPI();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fromDraftState]);

  useEffect(() => {
    scrollToTop();
  }, [errors]);

  const steps = getSteps({
    campaignType,
    currentSubscription,
    existingDomainDetails,
  });

  const handleSave = async (campaignName: string) => {
    updateUIState({ isEditing: false });

    const result = await createOrUpdateCampaign(
      campaignIdForCampaign,
      campaignName,
      campaignType || ""
    );

    if (result.success) {
      setName(result.data.name);
      updateUIState({
        showCampaignNameModal: false,
        errorMessage: false,
      });
      trackCampaignNameSave();
    } else if (result.isNameError) {
      updateUIState({
        showCampaignNameModal: true,
        errorMessage: true,
      });
    }
  };

  const handleSaveAndProceedClick = async () => {
    const currentStepIndex = steps.findIndex((step) => step.id === currentStep);
    const currentStepName = steps[currentStepIndex]?.name;
    const nextStep = steps[currentStepIndex + 1];
    let shouldProceed = true;

    switch (currentStepName) {
      case CAMPAIGN_STEP.SELECT_COLLECTION:
        trackStepNavigation(
          "Save and Proceed",
          currentStepName,
          selectedCollectionIds
        );

        const collectionResult = await saveCollectionIdsToAPI(
          selectedCollectionIds,
          campaignIdForCampaign as string
        );

        shouldProceed = collectionResult.success;
        break;

      case CAMPAIGN_STEP.CUSTOMISE:
        if (!formState.isFormValid) {
          return;
        } else {
          trackStepNavigation("Save and Proceed", currentStepName);
          const customisationResult = await saveCustomisationsToAPI(
            formState.customisationsData
          );
          shouldProceed = customisationResult.success;
        }
        break;

      case CAMPAIGN_STEP.EDIT_EMAIL:
        trackStepNavigation("Save and Proceed", currentStepName);
        const emailResult = await saveEmailTemplatesToAPI(
          emailMessage,
          campaignIdForCampaign as string
        );
        shouldProceed = emailResult.success;

        if (
          shouldProceed &&
          existingDomainDetails.length > 0 &&
          currentSubscription.status !== SUBSCRIPTION_STATUS.TRAILING
        ) {
          const skipToStep =
            steps.find((step) => step.name === CAMPAIGN_STEP.EDIT_LETTER) ||
            steps.find((step) => step.name === CAMPAIGN_STEP.SUMMARY);

          if (skipToStep) {
            dispatch({
              type: "campaign/updateCurrentStep",
              payload: skipToStep.id,
            });
            return;
          }
        }
        break;

      case CAMPAIGN_STEP.MAILBOXES:
        trackStepNavigation("Save and Proceed", currentStepName, mailboxData);

        const mailboxResult = await saveMailboxDataToAPI(
          mailboxData,
          campaignIdForCampaign as string
        );
        shouldProceed = mailboxResult.success;
        break;

      case CAMPAIGN_STEP.EDIT_LETTER:
        trackStepNavigation("Save and Proceed", currentStepName);
        const letterResult = await saveLetterTemplateToAPI(
          letterTemplate,
          campaignIdForCampaign as string
        );
        shouldProceed = letterResult.success;
        break;
      case CAMPAIGN_STEP.SUMMARY:
        trackCampaignStart();

        const finalizeResult = await finalizeCampaign(
          campaignIdForCampaign as string
        );

        if (!finalizeResult.success) {
          trackCampaignError(finalizeResult.error || "Unknown error");
          navigate("../campaign", {
            state: {
              saveCampaignError: true,
              errorMessage: finalizeResult.error,
            },
          });
          return;
        }

        const collectionsResult = await fetchCollections();
        dispatch(setCollections(collectionsResult.data));

        if (!collectionsResult.success) {
          trackCampaignError(collectionsResult.error || "Unknown error");
          navigate("../campaign", {
            state: {
              saveCampaignError: true,
              errorMessage: collectionsResult.error,
            },
          });
          return;
        }

        trackCampaignSuccess();
        navigate("../campaign", { state: { saveCampaignSuccess: true } });
        return;

      default:
        break;
    }

    if (nextStep && shouldProceed) {
      dispatch({ type: "campaign/updateCurrentStep", payload: nextStep.id });
    }
  };

  const handleBackClick = () => {
    const currentStepIndex = steps.findIndex((step) => step.id === currentStep);
    const currentStepName = steps[currentStepIndex]?.name;
    const previousStep = steps[currentStepIndex - 1];

    if (
      currentStepName === CAMPAIGN_STEP.EDIT_LETTER &&
      currentSubscription.status !== SUBSCRIPTION_STATUS.TRAILING &&
      existingDomainDetails.length > 0
    ) {
      const editMessageStep = steps.find(
        (step) => step.name === CAMPAIGN_STEP.EDIT_EMAIL
      );
      if (editMessageStep) {
        dispatch({
          type: "campaign/updateCurrentStep",
          payload: editMessageStep.id,
        });
        return;
      }
    }

    if (previousStep) {
      dispatch({
        type: "campaign/updateCurrentStep",
        payload: previousStep.id,
      });
    }

    trackStepNavigation("Back", currentStepName);
  };

  const handleStepClick = (stepId: number) => {
    const targetStepIndex = steps.findIndex((step) => step.id === stepId);
    const targetStepName = steps[targetStepIndex]?.name;

    const currentStepIndex = steps.findIndex((step) => step.id === currentStep);
    const currentStepName = steps[currentStepIndex]?.name;

    if (stepId >= currentStep) return;

    if (
      currentStepName === CAMPAIGN_STEP.EDIT_LETTER &&
      currentSubscription.status !== SUBSCRIPTION_STATUS.TRAILING &&
      existingDomainDetails.length > 0
    ) {
      const editMessageStep = steps.find(
        (step) => step.name === CAMPAIGN_STEP.EDIT_EMAIL
      );

      if (editMessageStep && stepId <= editMessageStep.id) {
        dispatch({
          type: "campaign/updateCurrentStep",
          payload: editMessageStep.id,
        });
        trackStepNavigation("Stepper Jump", targetStepName);
        return;
      }
    }

    dispatch({
      type: "campaign/updateCurrentStep",
      payload: stepId,
    });

    trackStepNavigation("Stepper Jump", targetStepName);
  };

  const handleExitClick = () => {
    if (currentStep !== 1) {
      trackUserLeavesCampaign(getNameOfStep(currentStep));
      navigate("../campaign");
      return;
    }
    updateUIState({ discardModal: true });
  };

  const handleSaveAndExitClick = async () => {
    const currentStepIndex = steps.findIndex((step) => step.id === currentStep);
    const currentStepName = steps[currentStepIndex]?.name;

    if (currentStepName === CAMPAIGN_STEP.SELECT_COLLECTION) {
      await saveCollectionIdsToAPI(
        selectedCollectionIds,
        campaignIdForCampaign as string
      );
    }
    dispatch({ type: "campaign/resetCampaignState" });
    trackUserLeavesCampaign(getNameOfStep(currentStep));
    navigate("../campaign");
  };

  return {
    steps,
    currentStep,
    name,
    isLoading,
    userData,
    currentSubscription,
    disableStartCampaign,

    showCampaignNameModal: uiState.showCampaignNameModal,
    discardModal: uiState.discardModal,
    generatedCampaignName: uiState.generatedCampaignName,
    errorMessage: uiState.errorMessage,
    showSuccessEndTrialSuccessBanner: uiState.showSuccessEndTrialSuccessBanner,
    showConfirmUnlockFullQuotaModal: uiState.showConfirmUnlockFullQuotaModal,

    letterTemplateError: errors.letterTemplateError,
    letterTemplateErrorMessage: errors.letterTemplateErrorMessage,
    isAddingCollectionError: errors.collectionError,
    showStepError: errors.showStepError,
    showApiError: errors.apiError,

    setShowSuccessEndTrialSuccessBanner: (value: boolean) =>
      updateUIState({ showSuccessEndTrialSuccessBanner: value }),
    setShowConfirmUnlockFullQuotaModal: (value: boolean) =>
      updateUIState({ showConfirmUnlockFullQuotaModal: value }),
    setShowCampaignNameModal: (value: boolean) =>
      updateUIState({ showCampaignNameModal: value }),
    handleEditCampaignName,
    handleEditClick,
    handleSave,
    handleFormSubmit,
    handleSaveAndProceedClick,
    handleBackClick,
    isSaveAndProceedButtonDisabled,
    setIsFormValid: (value: boolean) => updateFormState({ isFormValid: value }),
    setIsLoading: (value: boolean) => updateUIState({ isLoading: value }),
    dispatch,
    handleMailboxData,
    handleCampaignNameModalClose,
    handleExitClick,
    setDiscardModal: (value: boolean) => updateUIState({ discardModal: value }),
    handleSaveAndExitClick,
    handleStepClick,
  };
};
