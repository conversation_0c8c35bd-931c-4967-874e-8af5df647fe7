import { useState, useMemo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { SortingState } from "@tanstack/react-table";
import { Dict } from "mixpanel-browser";
import {
  selectEmailTemplate,
  selectLetterTemplate,
  setCampaignDetailsRecipients,
  setCampaignName,
  setCampaignType,
  setEmailTemplates,
  setLetterTemplate,
  setSelectedCollectionIds,
  updateCurrentStep,
} from "../campaignSlice";
import {
  getCampaignRecipientsDetails,
  getIndividualCampignDetails,
} from "../services";
import { CAMPAIGN_TYPE, CampaignList, STATUS } from "../types";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";

export const useCampaignTable = (campaignList: CampaignList[]) => {
  const [sorting, setSorting] = useState<SortingState>([
    { id: "createdAt", desc: true },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const defaultEmailTemplateData = useSelector(selectEmailTemplate);
  const defaultLetterTemplateData = useSelector(selectLetterTemplate);

  const user = JSON.parse(localStorage.getItem("user") || "{}");
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const mixpanelProps: Dict = {
    $name: `${user?.name}`,
    $email: user?.email,
  };

  const filteredData = useMemo(() => {
    return campaignList.filter((campaign) =>
      campaign.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [campaignList, searchQuery]);

  const handleDraftCampaign = useCallback(
    (campaignDetails: any, campaignId: string) => {
      if (
        (campaignDetails.currentStep === 4 ||
          campaignDetails.currentStep === 5) &&
        campaignDetails.status === STATUS.DRAFT
      ) {
        if (campaignDetails.type !== CAMPAIGN_TYPE.EMAIL) {
          dispatch(updateCurrentStep(2));
        } else {
          dispatch(updateCurrentStep(3));
        }
      } else if (
        campaignDetails.currentStep === 2 &&
        !campaignDetails.collections
      ) {
        dispatch(updateCurrentStep(1));
      } else {
        dispatch(updateCurrentStep(campaignDetails.currentStep));
      }

      dispatch(setCampaignType(campaignDetails.type));
      dispatch(setCampaignName(campaignDetails.name));

      if (campaignDetails.collections?.length > 0) {
        dispatch(
          setSelectedCollectionIds(
            campaignDetails.collections.map((collection: any) => collection.id)
          )
        );
      }

      const emailTemplates = (
        campaignDetails?.emails ?? defaultEmailTemplateData
      ).map((message: any, index: number) => {
        let name = "";
        if (index === 0) name = "Initial Contact";
        else if (index === 1) name = "Follow Up";
        else if (index === 2) name = "Final Outreach";
        return { ...message, name };
      });

      if (campaignDetails.type !== CAMPAIGN_TYPE.LETTER) {
        dispatch(setEmailTemplates(emailTemplates));
      }
      if (campaignDetails.type !== CAMPAIGN_TYPE.EMAIL) {
        dispatch(
          setLetterTemplate(
            campaignDetails?.letters?.length === 0
              ? defaultLetterTemplateData
              : campaignDetails.letters
          )
        );
      }

      navigate(`../campaignFlow`, {
        state: { fromDraftState: true, campaignId },
      });
    },
    [defaultEmailTemplateData, defaultLetterTemplateData, dispatch, navigate]
  );

  const handleActiveCampaign = useCallback(
    async (campaignId: string, campaignDetails: any) => {
      if (
        campaignDetails.status !== STATUS.ERROR &&
        campaignDetails.status !== STATUS.PAUSED
      ) {
        const campaignRecipientsDetails = await getCampaignRecipientsDetails(
          campaignId
        );

        if (campaignRecipientsDetails) {
          dispatch(setCampaignDetailsRecipients(campaignRecipientsDetails));

          if (campaignDetails.status === STATUS.WARMING) {
            navigate(`../campaignDetails`, {
              state: {
                campaignWarmingUp: true,
                campaignType: campaignDetails.type,
              },
            });
          } else {
            navigate(`../campaignDetails`, {
              state: { campaignType: campaignDetails.type },
            });
          }
        }
      }
    },
    [dispatch, navigate]
  );

  const handleCampaignClick = useCallback(
    async (campaignId: string) => {
      if (isLoading) return;
      setIsLoading(true);

      try {
        const campaignDetails = await getIndividualCampignDetails(campaignId);

        if (!campaignDetails) return;

        if (campaignDetails.status === STATUS.DRAFT) {
          handleDraftCampaign(campaignDetails, campaignId);
        } else {
          await handleActiveCampaign(campaignId, campaignDetails);
        }

        mixpanelCustomEvent({
          mixpanelProps: {
            ...mixpanelProps,
            campaignStatus: campaignDetails.status,
          },
          id: user?.uid.toString(),
          eventName: MixpanelEventName.selectsCampaign,
        });
      } catch (error) {
        console.error("Error fetching campaign details:", error);
      } finally {
        setTimeout(() => setIsLoading(false), 500);
      }
    },
    [
      handleActiveCampaign,
      handleDraftCampaign,
      isLoading,
      mixpanelProps,
      user?.uid,
    ]
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
    []
  );

  return {
    sorting,
    isLoading,
    searchQuery,
    filteredData,

    setSorting,
    handleCampaignClick,
    handleSearchChange,
  };
};
