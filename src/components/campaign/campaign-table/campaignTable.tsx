import {
    type ColumnDef,
    flexRender,
    getCoreRowModel,
    useReactTable,
    getSortedRowModel,
} from "@tanstack/react-table";
import { But<PERSON> } from "components/shadcn/ui/button";
import { Badge } from "components/shadcn/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "components/shadcn/ui/table";
import { BanIcon, ChevronsUpDown, ListTodo } from "lucide-react";
import { CampaignList, STATUS } from "../types";
import SvgPencilIcon from "components/common/iconComponents/pencilIcon";
import { formatDate } from "helpers";
import SvgWarmingUpIcon from "components/common/iconComponents/warmingUpIcon";
import SvgCheckGreenIcon from "components/common/iconComponents/checkGreenIcon";
import { Input } from "components/shadcn/ui/input";
import { statusConfig } from "./statusConfig";
import { useCampaignTable } from "./useCampaignTable";
import Loader from "components/common/loader";
import CommsStatusCell from "./commsStatusCell";
import { FileText } from "lucide-react";
import SvgEmailIcon from "components/common/iconComponents/emailIcon";

interface CampaignsTableProps {
    campaignList: CampaignList[];
}

export function CampaignsTable({ campaignList }: CampaignsTableProps) {
    const {
        sorting,
        isLoading,
        searchQuery,
        filteredData,
        setSorting,
        handleCampaignClick,
        handleSearchChange
    } = useCampaignTable(campaignList);

    const columns: ColumnDef<CampaignList>[] = [
        {
            accessorKey: "name",
            header: ({ column }) => (
                <Button
                    variant="custom"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                    className="flex items-center pl-0"
                >
                    Name
                    <ChevronsUpDown className="ml-2 h-4 w-4" />
                </Button>
            ),
        },
        {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }) => {
                const status = row.getValue("status") as keyof typeof statusConfig;
                const { bg, text, label } = statusConfig[status] || {
                    bg: "bg-gray-100",
                    text: "text-gray-800",
                    label: status || "Unknown",
                };

                let statusIcon = null;
                if (status === STATUS.SENT || status === STATUS.RUNNING || status === STATUS.COMPLETED) {
                    statusIcon = <SvgCheckGreenIcon />;
                } else if (status === STATUS.DRAFT) {
                    statusIcon = <SvgPencilIcon />;
                } else if (status === STATUS.QUEUED) {
                    statusIcon = <ListTodo width={12} height={12} />;
                } else if (status === STATUS.WARMING) {
                    statusIcon = <SvgWarmingUpIcon />;
                } else if (status === STATUS.ERROR) {
                    statusIcon = <BanIcon width={12} height={12} />;
                }

                return (
                    <Badge variant="secondary" className={`flex flex-row gap-1 w-[fit-content] ${bg} ${text}`}>
                        {statusIcon}
                        {label}
                    </Badge>
                );
            },
        },
        {
            accessorKey: "commsStatus",
            header: () => (
                <div className="flex flex-col gap-1">
                    <div className="flex items-center gap-1">
                        <SvgEmailIcon height={16} width={16} />
                        <span>Email Status</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <FileText height={16} width={16} />
                        <span>Letter Status</span>
                    </div>
                </div>
            ),
            cell: ({ row }) => (
                <CommsStatusCell
                    emailStatus={row.original.emailStatus}
                    letterStatus={row.original.letterStatus}
                />
            ),
        },
        {
            accessorKey: "recipients",
            header: "Recipients",
        },
        {
            accessorKey: "type",
            header: "Type",
            cell: ({ row }) => {
                const type = row.getValue("type") as string;
                return type.charAt(0).toUpperCase() + type.slice(1);
            },
        },
        {
            accessorKey: "createdAt",
            header: "Date Launched",
            cell: ({ row }) => {
                const date = row.getValue("createdAt") as string;
                return <div>{formatDate(date)}</div>;
            },
        },
    ];

    const table = useReactTable({
        data: filteredData,
        columns,
        getCoreRowModel: getCoreRowModel(),
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        state: {
            sorting,
        },
    });

    return (
        <div className="space-y-4 w-full">
            {isLoading && <Loader />}
            <div className="rounded-md border">
                <Table className="w-full">
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} className="text-sm text-gray-600">
                                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}

                        <TableRow>
                            <TableCell colSpan={columns.length} className="p-2">
                                <Input
                                    className="w-full"
                                    placeholder="Search by campaign name"
                                    value={searchQuery}
                                    onChange={handleSearchChange}
                                />
                            </TableCell>
                        </TableRow>
                    </TableHeader>

                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"} className="text-left">
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell
                                            key={cell.id}
                                            className={
                                                cell.column.id === "name" && ["running", "warming", "draft", "completed"].includes(row.original.status)
                                                    ? "text-sm font-medium text-gray-900 hover:text-purple-800 hover:underline cursor-pointer"
                                                    : "text-sm font-medium text-gray-900"
                                            }
                                            onClick={
                                                cell.column.id === "name" && ["running", "warming", "draft", "completed"].includes(row.original.status)
                                                    ? () => handleCampaignClick(row.original.id)
                                                    : undefined
                                            }
                                        >
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-24 text-center">
                                    No campaign found.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}

export default CampaignsTable;
