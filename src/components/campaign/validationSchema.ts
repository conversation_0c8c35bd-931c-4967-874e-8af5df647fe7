import * as Yup from "yup";

export const validationSchema = Yup.object({
  holdingCompany: Yup.string().required("Company name is required"),
  phoneNumber: Yup.string()
    .required("Phone number is required")
    .matches(
      /^\+\d{1,3}\s?\d{4}\s?\d{3}\s?\d{3}$/,
      "Phone number must be valid"
    ),
  website: Yup.string()
    .required("Website is required")
    .matches(
      /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/.*)?$/,
      "Please enter a valid website URL"
    ),
  address: Yup.object({
    line1: Yup.string().required("Address Line 1 is required"),
    line2: Yup.string().optional(),
    line3: Yup.string().optional(),
    city: Yup.string().required("City is required"),
    postalCode: Yup.string().required("Postcode is required"),
    country: Yup.string().required("Country is required"),
  }),
});

export const domainRegex =
  /^([a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])\.(co\.uk|com|net|co|uk)$/;
