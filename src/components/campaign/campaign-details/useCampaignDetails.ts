import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { SortingState } from "@tanstack/react-table";
import { selectCampaignDetailRecipients } from "../campaignSlice";
import lang from "lang";
import { LEAD_STATUS_KEY } from "./types";

interface CampaignData {
  company: string | null;
  recipient: string;
  email: string;
  comms: string;
  statuses: string[];
}

export const useCampaignDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const campaignDetail = useSelector(selectCampaignDetailRecipients);

  const { campaign: campaignCopy } = lang;

  const [tableData, setTableData] = useState<CampaignData[]>([]);
  const [sorting, setSorting] = useState<SortingState>([]);

  const isCampaignWarmingUp = location?.state?.campaignWarmingUp;
  const campaignType = location?.state?.campaignType;
  console.log(campaignType);
  useEffect(() => {
    if (campaignDetail) {
      if (campaignDetail.recipients) {
        const transformedData = campaignDetail.recipients.map((item: any) => ({
          company: item.companyName ?? "N/A",
          recipient: `${item.firstName} ${item.lastName}`,
          email: item.email,
          comms: item.channel,
          statuses: item.statuses || [],
        }));
        setTableData(transformedData);
      }
    }
  }, [campaignDetail]);

  const handleBackNavigation = () => {
    navigate("/campaign", { replace: true });
  };

  return {
    campaignType,
    campaignDetail,
    campaignCopy,
    tableData,
    sorting,
    isCampaignWarmingUp,
    setSorting,
    handleBackNavigation,
  };
};
export const statusLabels: Record<
  string,
  { label: string; bg: string; text: string }
> = {
  [LEAD_STATUS_KEY.EmailSent]: {
    label: "Email Sent",
    bg: "bg-blue-100",
    text: "text-blue-800",
  },
  [LEAD_STATUS_KEY.EmailOpened]: {
    label: "Email Opened",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [LEAD_STATUS_KEY.EmailClicked]: {
    label: "Email Clicked",
    bg: "bg-yellow-100",
    text: "text-yellow-800",
  },
  [LEAD_STATUS_KEY.EmailReplied]: {
    label: "Email Replied",
    bg: "bg-purple-100",
    text: "text-purple-800",
  },
  [LEAD_STATUS_KEY.EmailBounced]: {
    label: "Email Bounced",
    bg: "bg-red-100",
    text: "text-red-800",
  },
  [LEAD_STATUS_KEY.ReplyReceived]: {
    label: "Reply Received",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [LEAD_STATUS_KEY.AutoReplyReceived]: {
    label: "Auto Reply Received",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [LEAD_STATUS_KEY.LinkClicked]: {
    label: "Link Clicked",
    bg: "bg-blue-100",
    text: "text-blue-800",
  },
  [LEAD_STATUS_KEY.AccountError]: {
    label: "Account Error",
    bg: "bg-red-100",
    text: "text-red-800",
  },
  [LEAD_STATUS_KEY.CampaignCompleted]: {
    label: "Campaign Completed",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [LEAD_STATUS_KEY.Producing]: {
    label: "Producing",
    bg: "bg-blue-100",
    text: "text-blue-800",
  },
  [LEAD_STATUS_KEY.Dispatched]: {
    label: "Dispatched",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [LEAD_STATUS_KEY.Cancelled]: {
    label: "Cancelled",
    bg: "bg-red-100",
    text: "text-red-800",
  },
  [LEAD_STATUS_KEY.LocalDelivery]: {
    label: "Local Delivery",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [LEAD_STATUS_KEY.Delivered]: {
    label: "delivered",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [LEAD_STATUS_KEY.Returned]: {
    label: "Returned",
    bg: "bg-red-100",
    text: "text-red-800",
  },
  [LEAD_STATUS_KEY.Received]: {
    label: "Received",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [LEAD_STATUS_KEY.InTransit]: {
    label: "In Transit",
    bg: "bg-yellow-100",
    text: "text-yellow-800",
  },
  [LEAD_STATUS_KEY.HandedOver]: {
    label: "Handed Over",
    bg: "bg-blue-100",
    text: "text-blue-800",
  },
};
