import { Card, CardContent } from "components/shadcn/ui/card";
import useCampaignSummary from "./useSummary";
import lang from "lang";
import { HelpCircle } from "lucide-react";
import { Badge } from "components/shadcn/ui/badge";
import {
    Toolt<PERSON>,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "components/shadcn/ui/tooltip";
import { CAMPAIGN_TYPE, DOMAIN_STATUS } from "components/campaign/types";
import { SUBSCRIPTION_DISPLAY_NAME, SUBSCRIPTION_STATUS } from "components/subscription/types";
import Loader from "components/common/loader";
import { useEffect } from "react";

interface CampaignSummaryProps {
    setIsFormValid: (isValid: boolean) => void;
}

const CampaignSummary: React.FC<CampaignSummaryProps> = ({
    setIsFormValid,
}) => {
    const { campaign: campaignCopy } = lang;

    const {
        emailOutreachQuota,
        letterOutreachQuota,
        summaryResponse,
        subscriptionDetails,
        isLoading,
        campaignType,
    } = useCampaignSummary();
    const existingDomainDetails = JSON.parse(
        localStorage.getItem("domainSettings")!
    );
    const disableStartCampaign = !existingDomainDetails.every(
        (domain: any) =>
            [DOMAIN_STATUS.DOMAIN_READY, DOMAIN_STATUS.SET_PREFERRED].includes(domain.status)
    );

    useEffect(() => {
        setIsFormValid(summaryResponse.letterRecipients > 0 || summaryResponse.emailRecipients > 0);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [summaryResponse]);

    return (
        <div className="w-[80%] mx-auto p-6 text-left flex flex-col gap-6">
            {isLoading && <Loader />}
            <div className="flex justify-between items-center mb-4">
                <div className="flex flex-col gap-2">
                    <div className="text-xl font-semibold">
                        {campaignCopy.campaignSummary}
                    </div>
                    <p className="text-sm text-muted-foreground">
                        {campaignCopy.campaignSummarySubtext}
                    </p>
                </div>
            </div>

            {disableStartCampaign && campaignType !== CAMPAIGN_TYPE.LETTER && (
                <div className="rounded-[16px] flex justify-center border border-[#FEEE95] p-[12px] gap-[24px] bg-[#FEF7C3] text-sm font-semibold">
                    {campaignCopy.domainRegistrationInProgress}
                </div>
            )}
            <Card className="w-full max-w-sm p-4 shadow-md min-w-[100%]  border border-[#EAECF0]">
                <CardContent className="space-y-4 min-w-[100%]">
                    {campaignType !== CAMPAIGN_TYPE.EMAIL && (
                        <p className="text-sm text-muted-foreground">
                            {campaignCopy.letterOutreachSubtextSummary}
                        </p>
                    )}

                    <div className="flex justify-between items-center h-[40px] border-b pb-2">
                        <span className="text-sm font-regular text-gray-950">
                            {campaignCopy.collectionsSelected}
                        </span>
                        <span className="font-semibold text-lg text-gray-700">
                            {summaryResponse.totalCollections}
                        </span>
                    </div>
                    {campaignType !== CAMPAIGN_TYPE.EMAIL && (
                        <div className="flex justify-between items-center h-[40px] border-b pb-2">
                            <span className="text-sm font-regular text-gray-950">
                                {campaignCopy.totalNoOfLetterRecipients}
                            </span>
                            <span className="font-semibold text-lg text-gray-700">
                                {summaryResponse.letterRecipients}
                            </span>
                        </div>
                    )}
                    {campaignType !== CAMPAIGN_TYPE.LETTER && (
                        <div className="flex justify-between items-center h-[40px] border-b pb-2">
                            <span className="text-sm font-regular text-gray-950">
                                {campaignCopy.totalNoOfEmailRecipients}
                            </span>
                            <span className="font-semibold text-lg text-gray-700">
                                {summaryResponse.emailRecipients}
                            </span>
                        </div>
                    )}
                    {campaignType !== CAMPAIGN_TYPE.LETTER && (
                        <div className="flex justify-between text-red-500 items-center h-[40px] border-b pb-2">
                            <span className="text-sm font-regular text-gray-950">
                                {campaignCopy.emailQoutaUsage}
                            </span>
                            <span className="font-semibold text-lg text-gray-700">
                                {summaryResponse.emailRecipients}/{emailOutreachQuota}
                            </span>
                        </div>
                    )}
                    {campaignType !== CAMPAIGN_TYPE.EMAIL && (
                        <div className="flex justify-between text-red-500 items-center h-[40px] border-b pb-2">
                            <span className="text-sm font-regular text-gray-950">
                                {campaignCopy.letterQoutaUsage}
                            </span>
                            <span className="font-semibold text-lg text-gray-700">
                                {summaryResponse.letterRecipients}/{letterOutreachQuota}
                            </span>
                        </div>
                    )}
                    {campaignType !== CAMPAIGN_TYPE.LETTER && (
                        <div className="flex justify-between items-center h-[40px] border-b pb-2">
                            <span className="text-sm font-regular text-gray-950">
                                {campaignCopy.emailsPerWorkflow}
                            </span>
                            <span className="font-semibold text-lg text-gray-700">
                                {summaryResponse.emailsPerWorkflow}
                            </span>
                        </div>
                    )}

                    {campaignType !== CAMPAIGN_TYPE.LETTER && (
                        <TooltipProvider>
                            <div className="flex justify-between items-center h-[40px] border-b pb-2">
                                <span className="text-sm font-regular text-gray-950">
                                    Sending limit:
                                </span>
                                <span className="font-semibold text-lg text-gray-700 flex items-center gap-1">
                                    {subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING
                                        ? subscriptionDetails.productDisplayName === SUBSCRIPTION_DISPLAY_NAME.SOURCE
                                            ? "120 emails per day"
                                            : "60 emails per day"
                                        : "25 Emails(Trial limit)"}
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <HelpCircle
                                                size={16}
                                                className="text-gray-400 hover:cursor-pointer"
                                            />
                                        </TooltipTrigger>
                                        <TooltipContent side="top">
                                            {campaignCopy.toPreventSpam}
                                        </TooltipContent>
                                    </Tooltip>
                                </span>
                            </div>
                        </TooltipProvider>
                    )}
                    {campaignType !== CAMPAIGN_TYPE.LETTER &&
                        subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING && (
                            <div className="flex justify-between items-center h-[40px] border-b pb-2">
                                <span className="text-sm font-regular text-gray-950">
                                    Domain & mailbox setup:
                                </span>
                                {(() => {
                                    const status = summaryResponse?.domainStatus;
                                    const statuses = Array.isArray(status)
                                        ? status
                                        : status
                                            ? [status]
                                            : [];

                                    if (
                                        statuses.length > 0 &&
                                        statuses.every((s) => s === DOMAIN_STATUS.DOMAIN_READY)
                                    ) {
                                        return (
                                            <Badge
                                                variant="outline"
                                                className="bg-green-100 text-green-600 border-green-400"
                                            >
                                                Domain ready
                                            </Badge>
                                        );
                                    } else if (statuses.every((s) => s?.includes("failed"))) {
                                        return (
                                            <Badge
                                                variant="outline"
                                                className="bg-red-100 text-red-600 border-red-400"
                                            >
                                                Failed
                                            </Badge>
                                        );
                                    } else if (statuses.length > 0) {
                                        return (
                                            <Badge
                                                variant="outline"
                                                className="bg-yellow-100 text-yellow-600 border-yellow-400"
                                            >
                                                In progress
                                            </Badge>
                                        );
                                    } else {
                                        return (
                                            <Badge
                                                variant="outline"
                                                className="bg-gray-100 text-gray-600 border-gray-400"
                                            >
                                                Unknown
                                            </Badge>
                                        );
                                    }
                                })()}
                            </div>
                        )}
                    {campaignType !== CAMPAIGN_TYPE.LETTER && (
                        <TooltipProvider>
                            <div className="flex justify-between items-center h-[40px]">
                                <span className="text-sm font-regular text-gray-950">
                                    Campaign status
                                </span>
                                <span className="flex flex-row gap-2 items-center">
                                    <Badge
                                        variant="outline"
                                        className="bg-yellow-100 text-yellow-600 border-yellow-400"
                                    >
                                        Pending
                                    </Badge>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <HelpCircle
                                                size={16}
                                                className="text-gray-400 hover:cursor-pointer"
                                            />
                                        </TooltipTrigger>
                                        <TooltipContent side="top">
                                            {campaignCopy.notifyViaEmail}
                                        </TooltipContent>
                                    </Tooltip>
                                </span>
                            </div>
                        </TooltipProvider>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default CampaignSummary;
