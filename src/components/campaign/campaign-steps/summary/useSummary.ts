import axiosWithToken from "axiosWithToken";
import {
  selectCampaignId,
  selectCampaignType,
  selectedCollection,
} from "components/campaign/campaignSlice";
import { getSummaryForCamapign } from "components/campaign/services";
import { CampaignSummaryDto } from "components/campaign/types";
import { selectCurrentSubscription } from "components/subscription/subscriptionSlice";
import { useState, useEffect } from "react";
import { useSelector } from "react-redux";

const useCampaignSummary = () => {
  const baseURL2 = process.env.REACT_APP_BASEURL2;

  const campaignId = useSelector(selectCampaignId);
  const selectedCollectionIds = useSelector(selectedCollection);
  const subscriptionDetails = useSelector(selectCurrentSubscription);
  const campaignType = useSelector(selectCampaignType);

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const emailOutreachQuota =
    subscriptionDetails.userFeatureQuotas.find(
      (feature: any) => feature.featureName === "Email Outreach"
    )?.remainingQuota || 0;

  const letterOutreachQuota =
    subscriptionDetails.userFeatureQuotas.find(
      (feature: any) => feature.featureName === "Letter Outreach"
    )?.remainingQuota || 0;

  const [summaryResponse, setSummaryResponse] = useState<CampaignSummaryDto>(
    {} as CampaignSummaryDto
  );

  useEffect(() => {
    const fetchSummaryAndDomainSettings = async () => {
      setIsLoading(true);
      try {
        const [campaignData, domainSettingsResponse] = await Promise.all([
          getSummaryForCamapign(campaignId || ""),
          axiosWithToken.get(`${baseURL2}api/user/domain-settings`),
        ]);

        setSummaryResponse(campaignData);
        localStorage.setItem(
          "domainSettings",
          JSON.stringify(domainSettingsResponse.data)
        );
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummaryAndDomainSettings();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [campaignId]);

  return {
    summaryResponse,
    selectedCollectionIds,
    emailOutreachQuota,
    letterOutreachQuota,
    subscriptionDetails,
    isLoading,
    campaignType,
  };
};

export default useCampaignSummary;
