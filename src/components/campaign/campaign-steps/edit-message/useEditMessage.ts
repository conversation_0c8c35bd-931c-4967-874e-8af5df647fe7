import {
  selectEmailTemplate,
  setEmailTemplates,
} from "components/campaign/campaignSlice";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import { Dict } from "mixpanel-browser";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

export interface EmailTemplate {
  name: string;
  subject: string;
  sendAfterDays: number;
  body: string;
}

export function useEmailTemplates() {
  const emailTemplatesFromRedux = useSelector(selectEmailTemplate);
  const [emails, setEmails] = useState<EmailTemplate[]>([]);
  const dispatch = useDispatch();
  let user = JSON.parse(localStorage.getItem("user") || "{}");

  const mixpanelProps: Dict = {
    $name: `${user.name}`,
    //$distinct_id: user.id,
    $email: user.email,
  };

  useEffect(() => {
    setEmails(emailTemplatesFromRedux as EmailTemplate[]);
  }, [emailTemplatesFromRedux]);

  const updateEmail = (index: number, updatedEmail: Partial<EmailTemplate>) => {
    setEmails((prevEmails) => {
      const newEmails = prevEmails.map((email, i) =>
        i === index ? { ...email, ...updatedEmail } : email
      );
      dispatch(setEmailTemplates(newEmails));
      mixpanelCustomEvent({
        mixpanelProps,
        id: user?.uid.toString(),
        eventName: MixpanelEventName.userSavesEmailMessage,
      });
      return newEmails;
    });
  };

  return { emails, updateEmail };
}
