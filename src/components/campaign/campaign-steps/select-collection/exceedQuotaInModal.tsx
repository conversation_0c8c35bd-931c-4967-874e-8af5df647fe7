import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";
import lang from "lang";
import { Mail, FileText } from "lucide-react";

interface ExceedQuotaModalProps {
    open: boolean;
    onClose?: () => void;
    totalEmailQuota?: number;
    selectedEmailRecipientsCount?: number;
    emailOutreachQuota?: number;
    letterOutreachQuota?: number;
    selectedLetterRecipientsCount?: number;
    totalLetterQuota?: number;
    showUpgradeQuota?: () => void;
    showEmailQuotaBlock?: boolean;
    showLetterQuotaBlock?: boolean;
}

const ExceedQuotaModal: React.FC<ExceedQuotaModalProps> = ({
    open,
    totalEmailQuota,
    selectedEmailRecipientsCount,
    emailOutreachQuota,
    letterOutreachQuota,
    totalLetterQuota,
    showEmailQuotaBlock,
    showLetterQuotaBlock,
    selectedLetterRecipientsCount,
    onClose,
    showUpgradeQuota,
}) => {
    const { payment: paymentCopy } = lang;

    const getQuotaMessage = () => {
        if (showEmailQuotaBlock && showLetterQuotaBlock) {
            return paymentCopy.exceddedBothQuota
        } else if (showEmailQuotaBlock) {
            return paymentCopy.exceededEmailQuota;
        } else if (showLetterQuotaBlock) {
            return paymentCopy.exceededLetterQuota;
        }
        return "";
    };

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="max-w-xl p-6 rounded-2xl bg-white shadow-xl">
                <button className="w-full flex justify-end mb-2" onClick={onClose}>
                    <SvgCloseIcon />
                </button>

                <DialogHeader>
                    <DialogTitle className="text-center text-2xl font-bold text-gray-800">
                        {paymentCopy.exceedMonthlyQuota}
                    </DialogTitle>
                </DialogHeader>

                <p className="text-center text-sm text-gray-500 mt-2 mb-6">
                    {getQuotaMessage()} {paymentCopy.upgradeHelperText}
                </p>

                <div className="space-y-4">
                    {showEmailQuotaBlock && (
                        <div className="rounded-xl border bg-blue-50 p-4 flex gap-4 shadow-sm">
                            <div className="bg-blue-100 rounded-full p-2">
                                <Mail className="text-blue-600 w-5 h-5" />
                            </div>
                            <div>
                                <p className="font-semibold text-blue-800">{paymentCopy.emailQuota}</p>
                                <div className="text-sm text-gray-700">
                                    {paymentCopy.total} <strong>{totalEmailQuota}</strong> | {' '}
                                    {paymentCopy.available} <strong>{emailOutreachQuota}</strong> | {' '}
                                    {paymentCopy.required} <strong>{Math.max(0, (selectedEmailRecipientsCount ?? 0) - (emailOutreachQuota ?? 0))}</strong>
                                </div>
                            </div>
                        </div>
                    )}

                    {showLetterQuotaBlock && (
                        <div className="rounded-xl border bg-purple-50 p-4 flex gap-4 shadow-sm">
                            <div className="bg-purple-100 rounded-full p-2">
                                <FileText className="text-purple-600 w-5 h-5" />
                            </div>
                            <div>
                                <p className="font-semibold text-purple-800">{paymentCopy.letterQuota}</p>
                                <div className="text-sm text-gray-700">
                                    {paymentCopy.total}<strong>{totalLetterQuota}</strong> | {' '}
                                    {paymentCopy.available} <strong>{letterOutreachQuota}</strong> | {' '}
                                    {paymentCopy.required} <strong>{Math.max(0, (selectedLetterRecipientsCount ?? 0) - (letterOutreachQuota ?? 0))}</strong>
                                </div>

                            </div>
                        </div>
                    )}
                </div>

                <p className="text-sm text-gray-700">
                    <p className="text-sm text-gray-700">
                        {paymentCopy.createCollectionHelperText}
                    </p>
                </p>

                <div className="mt-6 text-center">
                    <Button
                        variant="primary"
                        className="w-full py-3 text-white bg-[--primary-600] hover:bg-[--primary-700] rounded-full font-medium text-base"
                        onClick={showUpgradeQuota}
                    >
                        {paymentCopy.upgrade}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default ExceedQuotaModal;
