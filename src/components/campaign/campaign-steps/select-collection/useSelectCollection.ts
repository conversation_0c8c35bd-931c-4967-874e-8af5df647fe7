import { useState, useMemo, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setSelectedCollectionIds,
  toggleCollectionId,
  selectedCollection,
  setSelectAllClickedForUncheck,
  selectCampaignType,
} from "../../campaignSlice";
import { selectCollections } from "components/collection/collectionSlice";
import { SortingState } from "@tanstack/react-table";
import {
  selectCurrentSubscription,
  setTopUpProduct,
} from "components/subscription/subscriptionSlice";
import { SUBSCRIPTION_STATUS } from "components/subscription/types";
import { getProductTopUp } from "components/subscription/services";
import { Dict } from "mixpanel-browser";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import { CAMPAIGN_TYPE } from "components/campaign/types";

export const useSelectCollection = () => {
  const dispatch = useDispatch();
  let user = JSON.parse(localStorage.getItem("user") || "{}");

  const collectionData = useSelector(selectCollections);
  const selectedCollectionIds = useSelector(selectedCollection);
  const subscriptionDetails = useSelector(selectCurrentSubscription);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([
    { id: "date", desc: true },
  ]);
  const campaignType = useSelector(selectCampaignType);
  const mixpanelProps: Dict = {
    $name: `${user.name}`,
    //$distinct_id: user.id,
    $email: user.email,
  };

  const emailOutreachQuota =
    subscriptionDetails.userFeatureQuotas.find(
      (feature: any) => feature.featureName === "Email Outreach"
    )?.remainingQuota || 0;

  const totalEmailQuota =
    subscriptionDetails.userFeatureQuotas.find(
      (feature: any) => feature.featureName === "Email Outreach"
    )?.totalQuota || 0;

  const letterOutreachQuota =
    subscriptionDetails.userFeatureQuotas.find(
      (feature: any) => feature.featureName === "Letter Outreach"
    )?.remainingQuota || 0;

  const totalLetterQuota =
    subscriptionDetails.userFeatureQuotas.find(
      (feature: any) => feature.featureName === "Letter Outreach"
    )?.totalQuota || 0;

  const filteredData = useMemo(() => {
    return collectionData.filter((collection) =>
      collection.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [collectionData, searchQuery]);

  const data = useMemo(
    () =>
      filteredData.map((item) => ({
        id: item.id,
        collection: item.name,
        companyCount: item.totalCompanies,
        letterRecipients: item.letterRecipients,
        emailRecipients: item.emailRecipients,
        date: item.createdAt ?? "",
        isAlreadyInCampaign: item.isAddedToCampaign,
        isLockedForEmail: item.isLockedForEmail,
        isLockedForLetter: item.isLockedForLetter,
      })),
    [filteredData]
  );

  const selectedEmailRecipientsCount = useMemo(
    () =>
      data.reduce(
        (total, row) =>
          selectedCollectionIds?.includes(row.id)
            ? total + (row.emailRecipients || 0)
            : total,
        0
      ),
    [data, selectedCollectionIds]
  );

  const selectedLetterRecipientsCount = useMemo(
    () =>
      data.reduce(
        (total, row) =>
          selectedCollectionIds?.includes(row.id)
            ? total + (row.letterRecipients || 0)
            : total,
        0
      ),
    [data, selectedCollectionIds]
  );

  const selectedCompaniesCount = useMemo(
    () =>
      data.reduce(
        (total, row) =>
          selectedCollectionIds?.includes(row.id)
            ? total + row.companyCount
            : total,
        0
      ),
    [data, selectedCollectionIds]
  );

  const isEmailQuotaExceeded = useMemo(
    () =>
      selectedEmailRecipientsCount > emailOutreachQuota &&
      subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING &&
      (campaignType === CAMPAIGN_TYPE.EMAIL ||
        campaignType === CAMPAIGN_TYPE.BOTH),
    [
      selectedEmailRecipientsCount,
      emailOutreachQuota,
      subscriptionDetails.status,
      campaignType,
    ]
  );

  const isLetterQuotaExceeded = useMemo(
    () =>
      selectedLetterRecipientsCount > letterOutreachQuota &&
      subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING &&
      (campaignType === CAMPAIGN_TYPE.LETTER ||
        campaignType === CAMPAIGN_TYPE.BOTH),
    [
      selectedLetterRecipientsCount,
      letterOutreachQuota,
      subscriptionDetails.status,
      campaignType,
    ]
  );

  const isAnyQuotaExceeded = useMemo(
    () => isEmailQuotaExceeded || isLetterQuotaExceeded,
    [isEmailQuotaExceeded, isLetterQuotaExceeded]
  );

  useEffect(() => {
    const fetchTopUps = async () => {
      if (subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING) {
        try {
          const topUpsResponse = await getProductTopUp();
          dispatch(setTopUpProduct(topUpsResponse));
        } catch (error) {
          console.error("Error fetching top-ups:", error);
        }
      }
    };

    fetchTopUps();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscriptionDetails.status]);

  const toggleRowSelection = (rowId: string) => {
    dispatch(toggleCollectionId(rowId));
    const updatedSelectedIds = selectedCollectionIds?.includes(rowId)
      ? selectedCollectionIds?.filter((id) => id !== rowId)
      : [...selectedCollectionIds, rowId];
    setIsAllSelected(updatedSelectedIds?.length === filteredData?.length);

    mixpanelCustomEvent({
      mixpanelProps,
      id: user.uid.toString(),
      eventName: MixpanelEventName.userAddsCollectionToCampaign,
    });
  };

  const toggleSelectAll = (checked: boolean) => {
    const allIds = filteredData
      ?.filter((item: any) => {
        if (campaignType === CAMPAIGN_TYPE.EMAIL) {
          return !item.isLockedForEmail;
        } else if (campaignType === CAMPAIGN_TYPE.LETTER) {
          return !item.isLockedForLetter;
        } else if (campaignType === CAMPAIGN_TYPE.BOTH) {
          return !item.isLockedForEmail && !item.isLockedForLetter;
        }
        return true;
      })
      .map((item: any) => item.id);

    if (checked) {
      dispatch(setSelectedCollectionIds(allIds));
      mixpanelCustomEvent({
        mixpanelProps,
        id: user?.uid.toString(),
        eventName: MixpanelEventName.userAddsCollectionToCampaign,
      });
    } else {
      dispatch(setSelectAllClickedForUncheck(true));
      dispatch(setSelectedCollectionIds([]));
    }

    setIsAllSelected(checked);
  };

  return {
    isAllSelected,
    selectedCollectionIds,
    emailOutreachQuota,
    letterOutreachQuota,
    totalLetterQuota,
    searchQuery,
    data,
    sorting,
    campaignType,
    totalEmailQuota,
    subscriptionDetails,
    toggleSelectAll,
    toggleRowSelection,
    setSorting,
    setSearchQuery,
    selectedEmailRecipientsCount,
    selectedLetterRecipientsCount,
    selectedCompaniesCount,
    isEmailQuotaExceeded,
    isLetterQuotaExceeded,
    isAnyQuotaExceeded,
  };
};
