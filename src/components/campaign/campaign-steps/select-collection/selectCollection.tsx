import React, { useEffect, useMemo, useRef, useState } from "react";
import {
    Table,
    TableBody,
    TableCell,
    TableHeader,
    TableRow,
} from "components/shadcn/ui/table";
import { Checkbox } from "components/shadcn/ui/checkbox";
import {
    useReactTable,
    flexRender,
    getCoreRowModel,
    getSortedRowModel,
    ColumnDef,
} from "@tanstack/react-table";
import SvgChevronSelectorIcon from "components/common/iconComponents/chevronSelectorIcon";
import { useSelectCollection } from "./useSelectCollection";
import lang from "lang";
import { Card, CardContent } from "components/shadcn/ui/card";
import { formatDate } from "helpers";
import { Input } from "components/shadcn/ui/input";
import { CurrentSubscriptionDetails, SUBSCRIPTION_STATUS } from "components/subscription/types";
import { getCurrentSubscription, upgradeSubscription, upgradeTopUp } from "components/subscription/services";
import { useDispatch } from "react-redux";
import { updateCurrentSubscription } from "components/subscription/subscriptionSlice";
import Loader from "components/common/loader";
import { Alert } from "components/shadcn/ui/alert";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import { CAMPAIGN_TYPE, FinalStat } from "components/campaign/types";
import { LockIcon } from "lucide-react";
import { UpgradeSubscriptionModal } from "components/subscription/upgrade-subscription";
import UpdateQuotaModal from "components/subscription/update-quota-modal/updateQuotaModal";
import ExceedQuotaModal from "./exceedQuotaInModal";
import { setSelectedCollectionIds } from "components/campaign/campaignSlice";
import { getDomainSettings } from "components/campaign/services";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/shadcn/ui/tooltip";

interface SelectCollectionProps {
    setIsFormValid: (isValid: boolean) => void;
}

const SelectCollection: React.FC<SelectCollectionProps> = ({ setIsFormValid }) => {
    const { campaign: campaignCopy } = lang;

    const dispatch = useDispatch();
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    const hasShownExceedingQuotaModalRef = useRef(false);

    const [loading, setLoading] = useState(false);
    const [showTopUpSuccessAlert, setShowTopUpSuccessAlert] = useState(false);
    const [isAllRowsInCampaign, setIsAllRowsInCampaign] = useState(false);
    const [showZeroRecipientsAlert, setShowZeroRecipientsAlert] = useState(false);
    const [showExceedingQuotaModal, setShowExceedingQuotaModal] = useState(false);
    const [showIndividualQuotaModal, setShowIndividualQuotaModal] = useState(false);
    const [showUpgradeQuotaModal, setShowUpgradeQuotaModal] = useState(false);

    const {
        isAllSelected,
        selectedCollectionIds,
        emailOutreachQuota,
        totalLetterQuota,
        letterOutreachQuota,
        searchQuery,
        campaignType,
        data,
        sorting,
        totalEmailQuota,
        subscriptionDetails,
        selectedEmailRecipientsCount,
        selectedLetterRecipientsCount,
        selectedCompaniesCount,
        isAnyQuotaExceeded,
        isEmailQuotaExceeded,
        isLetterQuotaExceeded,
        toggleSelectAll,
        toggleRowSelection,
        setSorting,
        setSearchQuery,
    } = useSelectCollection();

    const stats: FinalStat[] = [
        {
            label: campaignCopy.companiesSelected,
            value: selectedCompaniesCount.toString(),
        },
    ];

    if (campaignType === CAMPAIGN_TYPE.EMAIL) {
        stats.push(
            {
                label: campaignCopy.noOfEmailRecipients,
                value: selectedEmailRecipientsCount.toString(),
            },
            {
                label: campaignCopy.emailRecipientQuota,
                value: `${selectedEmailRecipientsCount} / ${emailOutreachQuota}`,
            }
        );
    } else if (campaignType === CAMPAIGN_TYPE.LETTER) {
        stats.push(
            {
                label: campaignCopy.noOfLetterRecipients,
                value: selectedLetterRecipientsCount.toString(),
            },
            {
                label: campaignCopy.letterRecipientQuota,
                value: `${selectedLetterRecipientsCount} / ${letterOutreachQuota}`,
            }
        );
    } else {
        stats.push({
            combined: true,
            lines: [
                {
                    label: campaignCopy.email,
                    value: selectedEmailRecipientsCount.toString(),
                },
                {
                    label: campaignCopy.letter,
                    value: selectedLetterRecipientsCount.toString(),
                },
            ],
        });

        stats.push({
            combined: true,
            lines: [
                {
                    label: campaignCopy.email,
                    value: `${selectedEmailRecipientsCount} / ${emailOutreachQuota}`,
                },
                {
                    label: campaignCopy.letter,
                    value: `${selectedLetterRecipientsCount} / ${letterOutreachQuota}`,
                },
            ],
        });
    }


    const columns: ColumnDef<any>[] = useMemo(() => {
        const baseColumns: ColumnDef<any>[] = [
            {
                header: "Collection name",
                accessorKey: "collection",
            },
            {
                header: "Created date",
                accessorKey: "date",
                cell: ({ row }) => {
                    const date = row.getValue("date") as string;
                    return <div>{formatDate(date)}</div>;
                },
            },
        ];

        if (campaignType !== CAMPAIGN_TYPE.EMAIL) {
            baseColumns.push({
                header: "Letter recipients",
                accessorKey: "letterRecipients",
            });
        }
        if (campaignType !== CAMPAIGN_TYPE.LETTER) {
            baseColumns.push({
                header: "Email recipients",
                accessorKey: "emailRecipients",
            });
        }
        return baseColumns;
    }, [campaignType]);

    const hasNoRecipients = useMemo(() => {
        if (campaignType === CAMPAIGN_TYPE.EMAIL) {
            return selectedEmailRecipientsCount === 0 && selectedCollectionIds.length > 0;
        } else if (campaignType === CAMPAIGN_TYPE.LETTER) {
            return selectedLetterRecipientsCount === 0 && selectedCollectionIds.length > 0;
        } else {
            return (selectedEmailRecipientsCount === 0 || selectedLetterRecipientsCount === 0) && selectedCollectionIds.length > 0;
        }
    }, [campaignType, selectedEmailRecipientsCount, selectedLetterRecipientsCount, selectedCollectionIds]);



    useEffect(() => {
        if (isAnyQuotaExceeded && !hasShownExceedingQuotaModalRef.current) {
            hasShownExceedingQuotaModalRef.current = true;
            setShowExceedingQuotaModal(true);
            setIsFormValid(false);
            setShowZeroRecipientsAlert(false);
        } else if (!isAnyQuotaExceeded) {
            hasShownExceedingQuotaModalRef.current = false;
            if (hasNoRecipients) {
                setIsFormValid(false);
                setShowZeroRecipientsAlert(true);
            } else if (selectedCollectionIds.length === 0) {
                setIsFormValid(false);
                setShowZeroRecipientsAlert(false);
            } else {
                setIsFormValid(true);
                setShowZeroRecipientsAlert(false);
            }
        }
    }, [isAnyQuotaExceeded, hasNoRecipients, selectedCollectionIds, setIsFormValid]);

    const closeExceedingQuotaModal = () => {
        hasShownExceedingQuotaModalRef.current = false;
        dispatch(setSelectedCollectionIds([]));
        setShowExceedingQuotaModal(false);
    };

    const showUpgradeQuota = () => {
        setShowIndividualQuotaModal(true);
    };

    const handleChangePlan = () => {
        setShowIndividualQuotaModal(false);
        setShowUpgradeQuotaModal(true);
    };

    const handleUpgradeSubscription = async (subscription: CurrentSubscriptionDetails, productPricingId: string) => {
        setShowIndividualQuotaModal(false);
        setShowUpgradeQuotaModal(false);
        setShowExceedingQuotaModal(false);
        setLoading(true);
        try {
            const upgradedSubscription = await upgradeSubscription(subscription.id, productPricingId);
            if (upgradedSubscription) {
                const currentSubscription = await getCurrentSubscription();
                if (currentSubscription) {
                    await getDomainSettings();
                    dispatch(updateCurrentSubscription(currentSubscription));
                    setLoading(false);
                }
            }
        } catch (error) {
            setLoading(false);
        }
    };

    const handleTopUpClick = async (topupDetails: any) => {
        setLoading(true);
        setShowExceedingQuotaModal(false);
        setShowIndividualQuotaModal(false);

        const mixpanelProps = {
            $name: user?.name,
            //$distinct_id: user?.uid,
            $email: user?.email,
            quota_type: "campaign",
            topupDetails: topupDetails,
        };

        mixpanelCustomEvent({
            mixpanelProps: {
                ...mixpanelProps,
            },
            id: user?.uid?.toString(),
            eventName: MixpanelEventName.clicksOnTopUpQouta,
        });

        try {
            setLoading(true);
            const updatedTopupDetails = await upgradeTopUp(topupDetails);
            if (updatedTopupDetails) {
                setLoading(true);
                const currentSubscription = await getCurrentSubscription();
                if (currentSubscription) {
                    dispatch(updateCurrentSubscription(currentSubscription));
                    setLoading(false);
                    setShowTopUpSuccessAlert(true);
                    setTimeout(() => {
                        setShowTopUpSuccessAlert(false);
                    }, 5000);
                }
                mixpanelCustomEvent({
                    mixpanelProps: {
                        ...mixpanelProps,
                    },
                    id: user?.uid?.toString(),
                    eventName: MixpanelEventName.topUpSuccessFullCampaign,
                });
            }
        } catch (error) {
            setLoading(false);
            setShowExceedingQuotaModal(false);
            console.error(error);
            mixpanelCustomEvent({
                mixpanelProps: {
                    ...mixpanelProps,
                },
                id: user?.uid?.toString(),
                eventName: MixpanelEventName.topUpError,
            });
        }
    }


    const table = useReactTable({
        data,
        columns,
        state: { sorting },
        onSortingChange: setSorting,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        enableRowSelection: true,
        getRowId: (row: any) => row.id,
    });


    useEffect(() => {
        const allRowsInCampaign = table.getRowModel().rows.every(
            (row) => row.original.isLockedForLetter || row.original.isLockedForEmail
        );
        setIsAllRowsInCampaign(allRowsInCampaign);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [table.getRowModel().rows]);


    return (
        <div className="border border-gray-200 rounded-[12px] max-h-[100%] overflow-y-auto w-full flex gap-6 flex-col">
            {loading && <Loader />}
            {showExceedingQuotaModal && (
                <ExceedQuotaModal
                    open={showExceedingQuotaModal}
                    onClose={closeExceedingQuotaModal}
                    showUpgradeQuota={showUpgradeQuota}
                    totalEmailQuota={totalEmailQuota}
                    totalLetterQuota={totalLetterQuota}
                    emailOutreachQuota={emailOutreachQuota}
                    letterOutreachQuota={letterOutreachQuota}
                    selectedEmailRecipientsCount={selectedEmailRecipientsCount}
                    selectedLetterRecipientsCount={selectedLetterRecipientsCount}
                    showEmailQuotaBlock={isEmailQuotaExceeded}
                    showLetterQuotaBlock={isLetterQuotaExceeded}
                />
            )}
            {showIndividualQuotaModal && (
                <UpdateQuotaModal
                    open={showIndividualQuotaModal}
                    close={() => setShowIndividualQuotaModal(false)}
                    handleTopUpClick={(topUpDetails) => handleTopUpClick(topUpDetails)}
                    changePlan={handleChangePlan}
                    showEmailQuotaBlock={isEmailQuotaExceeded}
                    showLetterQuotaBlock={isLetterQuotaExceeded}
                />
            )}
            {showUpgradeQuotaModal && (
                <UpgradeSubscriptionModal
                    open={showUpgradeQuotaModal}
                    close={() => setShowUpgradeQuotaModal(false)}
                    handleUpgradeSubscription={(subscription, productPricingId) =>
                        handleUpgradeSubscription(subscription, productPricingId)
                    }
                />
            )}
            <div className="display-sm semibold text-gray-900 text-left">
                {campaignCopy.selectCollection}
            </div>
            {showZeroRecipientsAlert && (
                <Alert
                    variant="warning"
                    className="mb-3"
                    title={campaignCopy.zeroRecipientWarning}
                />
            )
            }
            {
                showTopUpSuccessAlert &&
                <Alert
                    variant="success"
                    title={campaignCopy.quotaIncreasedSucesfully}
                />
            }

            <div className="flex flex-col sm:flex-row gap-4">
                {stats.map((stat, index) => (
                    <Card key={index} className="w-full sm:w-1/3 md:w-1/3 p-4 text-center flex items-center justify-center">
                        <CardContent className="p-2 flex flex-col justify-center items-center space-y-2 h-[fit-content]">

                            {"combined" in stat && <div className="text-md font-semibold">{index === 1 ? campaignCopy.noOfRecipients : index === 2 ? campaignCopy.recipientQuota : ''}</div>}
                            {"combined" in stat ? (
                                stat.lines.map((line, lineIndex) => (
                                    <div key={lineIndex} className="flex flex-row gap-4 justify-center items-center">
                                        <p className="text-sm text-gray-500">{line.label}</p>
                                        <p className="text-xl font-semibold">{line.value}</p>
                                    </div>
                                ))
                            ) : (
                                <div className="p-2 flex flex-row justify-between items-center gap-2">
                                    <p className="text-sm text-gray-500">{stat.label}</p>
                                    <p className="text-xl font-semibold">{stat.value}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                ))}

            </div>

            {
                subscriptionDetails.status === SUBSCRIPTION_STATUS.TRAILING && (
                    <div className="rounded-[16px] border border-[#FEEE95] p-[12px] gap-[24px] bg-[#FEF7C3] text-sm font-semibold">
                        {campaignType === CAMPAIGN_TYPE.EMAIL && campaignCopy.subtextForTrialUserEmailSelection}
                        {campaignType === CAMPAIGN_TYPE.LETTER && campaignCopy.subtextForTrialUserLetterSelection}
                        {campaignType === CAMPAIGN_TYPE.BOTH && campaignCopy.subtextForTrialUserCombinedSelection}
                    </div>
                )
            }

            <Table className="border-separate border-spacing-0 w-full bg-white">
                <TableHeader>
                    <TableRow>
                        <TableCell className="border-b border-gray-200">
                            <Checkbox
                                checked={isAllSelected}
                                onCheckedChange={(checked: any) => toggleSelectAll(!!checked)}
                                aria-label="Select all collections"
                                disabled={isAllRowsInCampaign}
                            />
                        </TableCell>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <React.Fragment key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableCell
                                        key={header.id}
                                        className="cursor-pointer border-b border-gray-200"
                                        onClick={header.column.getToggleSortingHandler()}
                                    >
                                        <div className="flex flex-row gap-2 items-center text-sm font-medium text-gray-600">
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                            {header.column.getIsSorted() ? (
                                                header.column.getIsSorted() === "desc" ? (
                                                    <SvgChevronSelectorIcon />
                                                ) : (
                                                    <SvgChevronSelectorIcon />
                                                )
                                            ) : (
                                                <SvgChevronSelectorIcon />
                                            )}
                                        </div>
                                    </TableCell>
                                ))}
                            </React.Fragment>
                        ))}
                    </TableRow>
                    <TableRow>
                        <TableCell colSpan={columns.length + 1} className="p-2">
                            <Input
                                className="w-full"
                                placeholder="Search by collection name"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </TableCell>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {table.getRowModel().rows.map((row) => {
                        const isDisabled =
                            (campaignType === CAMPAIGN_TYPE.EMAIL &&
                                !!row.original.isLockedForEmail) ||
                            (campaignType === CAMPAIGN_TYPE.LETTER &&
                                !!row.original.isLockedForLetter) ||
                            (campaignType === CAMPAIGN_TYPE.BOTH &&
                                (!!row.original.isLockedForEmail ||
                                    !!row.original.isLockedForLetter));

                        return (
                            <TableRow key={row.id}>
                                <TableCell
                                    className={`border-b border-gray-200 ${selectedCollectionIds?.includes(row.id) ? "bg-muted/50" : ""
                                        }`}
                                >
                                    <Checkbox
                                        checked={selectedCollectionIds?.includes(row.id)}
                                        onCheckedChange={() => toggleRowSelection(row.id)
                                        }
                                        aria-label={`Select ${row.original.collection}`}
                                    //disabled={isDisabled}
                                    />
                                </TableCell>

                                {row.getVisibleCells().map((cell) => (
                                    <TableCell
                                        key={cell.id}
                                        className={`border-b border-gray-200 text-left ${selectedCollectionIds?.includes(row.id)
                                            ? "bg-muted/50"
                                            : ""
                                            }`}
                                    >
                                        {cell.column.id === "collection" ? (
                                            <div>
                                                <div
                                                    className={`flex flex-row gap-2 font-medium font-inter ${selectedCollectionIds?.includes(row.id)
                                                        ? "text-purple-600"
                                                        : "text-gray-900"
                                                        }`}
                                                >
                                                    {row.original.collection}
                                                    {isDisabled && (
                                                        <TooltipProvider>

                                                            <Tooltip>
                                                                <TooltipTrigger asChild>
                                                                    <LockIcon height={16} width={16} className="text-gray-500 cursor-default" />
                                                                </TooltipTrigger>
                                                                <TooltipContent>
                                                                    <span>
                                                                        {row.original.isLockedForEmail && row.original.isLockedForLetter
                                                                            ? "Locked for email and letter campaigns"
                                                                            : row.original.isLockedForEmail
                                                                                ? "Locked for email campaign"
                                                                                : "Locked for letter campaigns"}
                                                                    </span>
                                                                </TooltipContent>
                                                            </Tooltip>
                                                        </TooltipProvider>

                                                    )}                                                </div>
                                                <div
                                                    className={`text-regular font-inter ${selectedCollectionIds?.includes(row.id)
                                                        ? "text-purple-600"
                                                        : "text-gray-600"
                                                        }`}
                                                >
                                                    {row.original.companyCount} companies
                                                </div>
                                            </div>
                                        ) : (
                                            <div
                                                className={`text-regular font-inter ${selectedCollectionIds?.includes(row.id) ? "text-purple-600" : "text-gray-600"
                                                    }`}
                                            >
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </div>
                                        )}
                                    </TableCell>
                                ))}
                            </TableRow>
                        );
                    })}
                </TableBody>
            </Table>
        </div >
    );
};

export default SelectCollection;
