import type React from "react";
import { useEffect, useRef, useState } from "react";
import lang from "lang";
import { Card, CardContent } from "components/shadcn/ui/card";
import { Button } from "components/shadcn/ui/button";
import VariableSelectionDialog from "components/popUps/variableSelctionDialogue";
import PreviewLetterDialog from "./previewLetterDialogue";
import { useSelector } from "react-redux";
import { selectLetterTemplate } from "components/campaign/campaignSlice";
import { useEditLetter } from "./useEditLetter";

const EditLetter: React.FC = () => {
    const { campaign: campaignCopy } = lang;
    const letterTemplate = useSelector(selectLetterTemplate);
    const [sections, setSections] = useState(letterTemplate);
    const [showModal, setShowModal] = useState(false);
    const [showPreviewModal, setShowPreviewModal] = useState(false);
    const refs = useRef<Record<number, HTMLDivElement | null>>({});
    const [charCounts, setCharCounts] = useState<Record<number, number>>({});
    const isManualEdit = useRef(false);
    const { updateLetter } = useEditLetter();

    const highlightVariables = (text: string) => {
        return text
            .replace(
                /{{(.*?)}}/g,
                '<span class="highlighted" contenteditable="false">{{$1}}</span>'
            )
            .replace(/\n/g, "<br>");
    };

    const cleanHtmlToPlainText = (html: string) => {
        let text = html.replace(/<br\s*\/?>/gi, "\n");
        text = text.replace(/<\/?[^>]+(>|$)/g, "");
        const textarea = document.createElement("textarea");
        textarea.innerHTML = text;
        text = textarea.value.replace(/\u00A0/g, " ");
        return text.trim();
    };

    const getCharCountExcludingVariables = (text: string) => {
        return text.replace(/{{.*?}}/g, "").length;
    };

    const placeCaretAtEnd = (el: HTMLElement) => {
        el.focus();
        const range = document.createRange();
        range.selectNodeContents(el);
        range.collapse(false);
        const sel = window.getSelection();
        if (sel) {
            sel.removeAllRanges();
            sel.addRange(range);
        }
    };

    const handleInput = (e: React.FormEvent<HTMLDivElement>, index: number) => {
        const el = e.currentTarget;
        const rawHtml = el.innerHTML;
        const plain = cleanHtmlToPlainText(rawHtml);

        const currentSection = sections[index];
        const charCount = getCharCountExcludingVariables(plain);

        if (charCount > 300) {
            if (currentSection && refs.current[index]) {
                refs.current[index]!.innerHTML = highlightVariables(currentSection.body);
                placeCaretAtEnd(refs.current[index]!);
            }
            return;
        }

        isManualEdit.current = true;
        setSections((prev: any) =>
            prev.map((s: any, i: any) => (i === index ? { ...s, body: plain } : s))
        );
    };

    const handleBlur = () => {
        isManualEdit.current = false;

        const updatedPayload = {
            letters: sections.map((section: any) => ({
                body: section.body,
            })),
        };

        updateLetter(updatedPayload);
    };

    const handleKeyDown = (
        e: React.KeyboardEvent<HTMLDivElement>,
        index: number
    ) => {
        const sel = window.getSelection();
        if (!sel || sel.rangeCount === 0) return;

        const range = sel.getRangeAt(0);
        const startNode = range.startContainer;
        const endNode = range.endContainer;

        if (e.key === "Enter") {
            e.preventDefault();
            return;
        }

        if (e.key.length === 1) {
            const allowedChars = /[a-zA-Z0-9\s.,!?'"\-(){}#@$%&*+=/_:;]/;
            if (!allowedChars.test(e.key)) {
                e.preventDefault();
                return;
            }
        }

        const findHighlightedParent = (node: Node): HTMLElement | null => {
            let current: Node | null = node;
            while (current && current.nodeType !== Node.ELEMENT_NODE) {
                current = current.parentNode;
            }

            if (
                current instanceof HTMLElement &&
                current.classList.contains("highlighted")
            ) {
                return current;
            }

            if (current && current.parentNode) {
                return findHighlightedParent(current.parentNode);
            }

            return null;
        };

        const startHighlighted = findHighlightedParent(startNode);
        const endHighlighted = findHighlightedParent(endNode);

        if (
            (e.key === "Backspace" || e.key === "Delete") &&
            (startHighlighted || endHighlighted)
        ) {
            e.preventDefault();

            const spanToRemove = startHighlighted || endHighlighted;
            if (spanToRemove) {
                spanToRemove.remove();
                setTimeout(() => {
                    const el = refs.current[index];
                    if (el) {
                        const rawHtml = el.innerHTML;
                        const plain = cleanHtmlToPlainText(rawHtml);
                        isManualEdit.current = true;
                        setSections((prev: any) =>
                            prev.map((s: any, i: number) => (i === index ? { ...s, body: plain } : s))
                        );
                    }
                }, 0);
            }
            return;
        }

        if (startHighlighted || endHighlighted) {
            const allowedKeys = [
                "ArrowLeft",
                "ArrowRight",
                "ArrowUp",
                "ArrowDown",
                "Tab",
                "Escape",
                "Home",
                "End",
            ];
            if (!allowedKeys.includes(e.key)) {
                e.preventDefault();
            }
        }
    };

    const handlePaste = (
        e: React.ClipboardEvent<HTMLDivElement>,
        index: number
    ) => {
        e.preventDefault();
        e.currentTarget.focus(); // Ensure the element is focused

        const pastedText = e.clipboardData.getData("text/plain");

        const hasVariablePattern = /\{\{.*?\}\}/.test(pastedText);
        const selection = window.getSelection();
        if (!selection || !selection.rangeCount) return;

        const range = selection.getRangeAt(0);
        range.deleteContents();

        if (hasVariablePattern) {
            // Replace variables with styled span elements
            const htmlString = pastedText.replace(/\{\{(.*?)\}\}/g, (match) => {
                return `<span class="highlighted" contenteditable="false">${match}</span>`;
            });

            const fragment = range.createContextualFragment(htmlString);
            range.insertNode(fragment);

            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);

            const inputEvent = new Event("input", { bubbles: true });
            e.currentTarget.dispatchEvent(inputEvent);
            return;
        }

        // Regular text without variables — filter allowed characters
        const allowedChars = /[a-zA-Z0-9\s.,!?'"\-(){}#@$%&*+=/_:;]/g;
        const matches = pastedText.match(allowedChars);
        const filteredText = matches ? matches.join('') : '';

        const textNode = document.createTextNode(filteredText);
        range.insertNode(textNode);

        range.setStartAfter(textNode);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);

        const inputEvent = new Event("input", { bubbles: true });
        e.currentTarget.dispatchEvent(inputEvent);
    };


    useEffect(() => {
        sections.forEach((section: any, index: number) => {
            const el = refs.current[index];
            if (el && !isManualEdit.current) {
                el.innerHTML = highlightVariables(section.body);
            }
        });
    }, [sections]);

    useEffect(() => {
        const updatedCounts: Record<number, number> = {};
        sections.forEach((section: any, index: number) => {
            updatedCounts[index] = getCharCountExcludingVariables(section.body);
        });
        setCharCounts(updatedCounts);
    }, [sections]);

    const renderEditorSection = (
        section: { body: string },
        index: number
    ) => (
        <div key={index} className="flex flex-col gap-2">
            <h3 className="mb-1 text-gray-700 font-bold text-left text-lg ml-2">
                {campaignCopy.paragraph} {index + 1}
            </h3>
            <div
                ref={(el) => (refs.current[index] = el)}
                contentEditable
                suppressContentEditableWarning
                className="w-full p-4 text-md font-medium text-black min-h-[fit-content] text-left focus:outline-none border rounded-lg shadow-sm"
                onInput={(e) => handleInput(e, index)}
                onBlur={() => handleBlur()}
                onKeyDown={(e) => handleKeyDown(e, index)}
                onPaste={(e) => handlePaste(e, index)}
            />
            <div className="text-sm text-right text-gray-500 mt-1">
                {charCounts[index] || 0}{campaignCopy.maxCharacterLimit}
            </div>
        </div>
    );

    return (
        <div className="w-full">
            <div className="text-xl font-semibold mb-6 text-left">
                {campaignCopy.reviewAndRefine}
            </div>

            <Card className="w-full">
                <CardContent className="p-6">
                    <div className="w-full flex justify-end mb-4">
                        <div className="flex flex-row justify-between w-full">
                            <Button onClick={() => setShowPreviewModal(true)} variant={"primary"} size="sm" className="mb-2">
                                {campaignCopy.preview}
                            </Button>
                            <Button
                                variant="primary"
                                size="sm"
                                onClick={() => setShowModal(true)}
                            >
                                <div className="text-white font-inter text-sm font-semibold underline-from-font">
                                    {campaignCopy.parameters}
                                </div>
                            </Button>
                        </div>
                    </div>

                    <VariableSelectionDialog
                        showModal={showModal}
                        setShowModal={setShowModal}
                        onClose={() => setShowModal(false)}
                    />

                    <Card className="border-none">
                        <CardContent className="pb-0 border-none shadow-none">
                            <ul className="list-disc pl-6 text-left border-none">
                                {campaignCopy.letterSubText.map((item, index) => (
                                    <li key={index} className="text-gray-700 text-sm mb-1">
                                        {item}
                                    </li>
                                ))}
                            </ul>
                        </CardContent>
                    </Card>
                    {sections.map((section: any, index: number) =>
                        renderEditorSection(section, index)
                    )}
                </CardContent>
            </Card>
            <PreviewLetterDialog
                showModal={showPreviewModal}
                sections={sections}
                onClose={() => setShowPreviewModal(false)}
            />

            <style>{`
                .highlighted {
                    color: #C01048;
                    cursor: pointer;
                    user-select: all;
                }
            `}</style>
        </div>
    );
};

export default EditLetter;
