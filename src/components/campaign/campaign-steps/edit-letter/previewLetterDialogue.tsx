import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    Di<PERSON><PERSON>eader,
    Di<PERSON>Title,
    DialogFooter,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import lang from "lang";
import { getDateForLetterPreview } from "helpers";

interface LetterPreviewDialogProps {
    showModal: boolean;
    onClose: () => void;
    sections: { body: string }[];
}

const LetterPreviewDialog: React.FC<LetterPreviewDialogProps> = ({
    showModal,
    onClose,
    sections,
}) => {
    let userData = JSON.parse(localStorage.getItem("user")!);
    const { campaign: campaignCopy } = lang;
    return (
        <Dialog open={showModal} onOpenChange={onClose}>
            <DialogContent className="max-w-2xl max-h-[95vh] overflow-y-auto p-6 sm:p-8">
                <DialogHeader>
                    <DialogTitle className="text-left text-xl font-semibold">
                        {campaignCopy.letterPreview}
                    </DialogTitle>
                </DialogHeader>

                <div className="mt-4 text-sm text-gray-900">
                    <div className="text-right mb-6 space-y-1 font-medium">
                        <div>{userData?.holdingCompany}</div>
                        <div>{userData?.address?.line1}</div>
                        <div>{userData?.address?.line2}</div>
                        <div>{userData?.address?.line3}</div>
                        <div>{userData?.address?.city}</div>
                        <div>{userData?.address?.postalCode}</div>
                        <div>{userData?.address?.country}</div>
                        <div>{userData?.phoneNumber}</div>
                    </div>

                    <div className="text-left mb-6 space-y-1 font-medium">
                        <div>{"{{Recipient Name}}"}</div>
                        <div>{"{{Company Name}}"}</div>
                        <div>{"{{Address1}}"}</div>
                        <div>{"{{Address2}}, {{Address3}}"}</div>
                        <div>{"{{City}}"}</div>
                        <div>{"{{Postcode}}"}</div>
                        <div>{"{{Country}}"}</div>
                    </div>

                    <div className="text-right mb-6 font-medium">
                        {getDateForLetterPreview()}
                    </div>

                    <div className="space-y-6 text-left">
                        <span>{`Dear {{Recipient Name}},`}</span>
                        {sections.map((section, index) => (
                            <p key={index} className="whitespace-pre-wrap">
                                {section.body}
                            </p>
                        ))}
                    </div>
                </div>
                <div className="text-left mb-6 space-y-1 font-normal text-sm">
                    <div>{"Regards,"}</div>
                    <div>{"{{sender.firstName}} {{sender.lastName}}"}</div>
                    <div>{"{{sender.company}}"}</div>
                    <div>{"{{sender.email}}"}</div>
                    <div>{"{{sender.phoneNumber}}"}</div>
                </div>
                <DialogFooter className="mt-6">
                    <Button variant="outline" onClick={onClose}>
                        {campaignCopy.close}
                    </Button>
                </DialogFooter>
            </DialogContent >
        </Dialog >
    );
};

export default LetterPreviewDialog;
