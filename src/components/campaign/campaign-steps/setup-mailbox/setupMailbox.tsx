/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from "react";
import { CardContent } from "components/shadcn/ui/card";
import { Input } from "components/shadcn/ui/input";
import { Label } from "components/shadcn/ui/label";
import lang from "lang";
import { domainRegex } from "components/campaign/validationSchema";
import { HelpCircle } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/shadcn/ui/tooltip";
import { useSelector } from "react-redux";
import { selectCurrentSubscription } from "components/subscription/subscriptionSlice";
import { SUBSCRIPTION_STATUS } from "components/subscription/types";

interface SetUpMailboxProps {
    setIsFormValid: (isValid: boolean) => void;
    onDataSubmit: (data: any) => void;
}

const SetUpMailbox: React.FC<SetUpMailboxProps> = ({ setIsFormValid, onDataSubmit }) => {
    const userData = JSON.parse(localStorage.getItem("user")!);
    const { campaign: campaignCopy } = lang;
    const [domain, setDomain] = useState<string>('');
    const [domainError, setDomainError] = useState<string | null>(null);
    const subscriptionDetails = useSelector(selectCurrentSubscription);

    const trialDomain = process.env.REACT_APP_CAMPAIGN_TRIAL_DOMAIN;

    useEffect(() => {
        if (subscriptionDetails.status === SUBSCRIPTION_STATUS.TRAILING) {
            const domainToSet = trialDomain || '';
            setDomain(domainToSet);
            setIsFormValid(true);
            if (domainToSet && domainRegex.test(domainToSet)) {
                handleBlur(domainToSet);
                setDomainError(null);
            }
        }
    }, [subscriptionDetails]);


    const handleDomainChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.trim();
        setDomain(value);

        if (value === "" || !domainRegex.test(value)) {
            setDomainError(campaignCopy.domainError);
            setIsFormValid(false);
        } else {
            setDomainError(null);
            setIsFormValid(true);
            handleBlur(value);
        }
    };
    useEffect(() => {
        if (subscriptionDetails.status !== SUBSCRIPTION_STATUS.TRAILING && !domain.trim()) {
            setIsFormValid(false);
        }
    }, [domain, setIsFormValid, subscriptionDetails]);

    const handleWebsiteBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        const formattedValue = e.target.value;
        setDomain(formattedValue);
    };

    const handleWebsitePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
        e.preventDefault();
        const pastedText = e.clipboardData.getData('text');
        setDomain(pastedText);

        if (pastedText && domainRegex.test(pastedText)) {
            setDomainError(null);
            setIsFormValid(true);
            handleBlur(pastedText);
        } else {
            setDomainError(campaignCopy.domainError);
            setIsFormValid(false);
        }
    };

    const handleBlur = (value: string) => {
        const formattedDomain = value;
        const formattedData = {
            replyTo: userData?.email,
            preferredDomain: formattedDomain,
        };
        onDataSubmit(formattedData);
    };

    return (
        <div className="w-full h-full min-h-[310px]">
            <CardContent className="flex flex-col gap-6">
                <div className="w-[100%]">
                    <div className="flex justify-between items-start flex-row gap-6">
                        <div className="text-left flex flex-col gap-4 w-[100%]">
                            <Label htmlFor="domain">
                                {campaignCopy.labelPreferredDomain}
                            </Label>
                            <div className="flex flex-col gap-1">
                                <div className="w-full p-2 gap-2 mb-2 bg-[#EAECF0] flex flex-col justify-center font-inter text-sm font-medium leading-[20px] text-left text-gray-700">
                                    <ul className="list-disc list-inside space-y-1">
                                        <li>{campaignCopy.websiteDomain}</li>
                                        <li>
                                            {campaignCopy.allowedTopLevelDomains}
                                            <span className="font-normal">
                                                {campaignCopy.allowedTlds}
                                            </span>
                                        </li>
                                        <li>{campaignCopy.registerNewDomainHelperText}</li>
                                    </ul>
                                </div>
                                <Input
                                    id="domain"
                                    placeholder="Type your domain"
                                    value={
                                        subscriptionDetails.status ===
                                            SUBSCRIPTION_STATUS.TRAILING
                                            ? trialDomain
                                            : domain
                                    }
                                    onChange={handleDomainChange}
                                    onBlur={handleWebsiteBlur}
                                    onPaste={handleWebsitePaste}
                                    autoComplete="off"
                                    disabled={
                                        subscriptionDetails.status ===
                                        SUBSCRIPTION_STATUS.TRAILING
                                    }
                                />
                                {domainError && (
                                    <div className="text-sm text-red-500 mt-1">
                                        {domainError}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
                <TooltipProvider>
                    <div className="flex items-left gap-2 flex-col">
                        <div className="flex flex-row gap-2">
                            <Label className="text-sm text-left">
                                {campaignCopy.repliesToEmail}
                            </Label>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <HelpCircle
                                        size={16}
                                        className="text-gray-400 cursor-pointer"
                                    />
                                </TooltipTrigger>
                                <TooltipContent side="top">
                                    {campaignCopy.repliesToEmailSubtextTooltip}
                                </TooltipContent>
                            </Tooltip>
                        </div>
                        <Input disabled className="text-sm" value={userData?.email} />
                    </div>
                </TooltipProvider>
            </CardContent>
        </div>
    );
};

export default SetUpMailbox;
