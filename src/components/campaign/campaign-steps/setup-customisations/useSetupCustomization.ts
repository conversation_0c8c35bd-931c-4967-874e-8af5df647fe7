import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { formatPhoneNumber, formatWebsite } from "helpers";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import { validationSchema } from "components/campaign/validationSchema";

type FormInputs = Yup.InferType<typeof validationSchema>;

export const useSetupCustomisations = (
  onFormSubmit: (data: FormInputs) => void,
  setIsFormValid: (isValid: boolean) => void
) => {
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const [customizations, setCustomizations] = useState<FormInputs>(
    {} as FormInputs
  );
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  const [parsedAddress, setParsedAddress] = useState({
    line1: "",
    line2: "",
    line3: "",
    city: "",
    postalCode: "",
    country: "",
  });
  const [isAddressLoading, setIsAddressLoading] = useState(false);
  const [showAddressFields, setShowAddressFields] = useState(false);

  const addressInputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<any>(null);
  const lastIsFormValid = useRef<boolean | null>(null);

  const {
    register,
    watch,
    setValue,
    reset,
    formState: { errors, isValid },
  } = useForm<FormInputs>({
    resolver: yupResolver(validationSchema),
    mode: "onBlur",
  });

  useEffect(() => {
    if (window.google && addressInputRef.current && !autocompleteRef.current) {
      setIsAddressLoading(true);
      autocompleteRef.current = new window.google.maps.places.Autocomplete(
        addressInputRef.current,
        {
          types: ["geocode"],
          fields: ["address_components", "formatted_address"],
        }
      );

      autocompleteRef.current.addListener("place_changed", () => {
        const place = autocompleteRef.current?.getPlace();
        if (!place?.address_components) return;

        const components: Record<string, string> = {};
        place.address_components.forEach((component: any) => {
          const type = component.types[0];
          components[type] = component.long_name;
        });

        const address = {
          line1: `${components.street_number || ""} ${
            components.route || ""
          }`.trim(),
          line2: components.premise || components.subpremise || "",
          line3: components.neighborhood || "",
          city: components.locality || components.sublocality || "",
          postalCode: components.postal_code || "",
          country: components.country || "",
        };

        setParsedAddress(address);
        setSelectedAddress(place);
        setValue("address", address, { shouldValidate: true });
        Object.entries(address).forEach(([key, value]) => {
          setValue(`address.${key}` as any, value, {
            shouldValidate: true,
          });
        });
        setShowAddressFields(true);
        setTimeout(() => setIsAddressLoading(false), 500);
      });
    }
  }, [setValue]);

  useEffect(() => {
    if (user) {
      const addressData = user.address || null;
      setCustomizations({
        holdingCompany: user.holdingCompany,
        phoneNumber: user.phoneNumber,
        website: user.website,
        address: addressData,
      });
      if (addressData) {
        setParsedAddress(addressData);
        setShowAddressFields(true);
        setIsAddressLoading(false);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    reset(customizations);
  }, [customizations, reset]);

  const formValues = watch();
  const prevFormValues = useRef<FormInputs | null>(null);

  useEffect(() => {
    const requiredAddressFilled =
      !!parsedAddress?.line1 &&
      !!parsedAddress?.city &&
      !!parsedAddress?.postalCode &&
      !!parsedAddress?.country;

    const shouldBeValid =
      isValid &&
      requiredAddressFilled &&
      !!formValues.holdingCompany &&
      !!formValues.phoneNumber &&
      !!formValues.website;

    if (lastIsFormValid.current !== shouldBeValid) {
      setIsFormValid(shouldBeValid);
      lastIsFormValid.current = shouldBeValid;
    }

    if (
      shouldBeValid &&
      prevFormValues.current &&
      JSON.stringify(prevFormValues.current) !== JSON.stringify(formValues)
    ) {
      const changedFields: string[] = [];

      if (
        JSON.stringify(prevFormValues.current.address) !==
        JSON.stringify(formValues.address)
      ) {
        Object.entries(formValues.address || {}).forEach(([key, val]) => {
          if (
            prevFormValues.current?.address?.[
              key as keyof typeof formValues.address
            ] !== val
          ) {
            changedFields.push(`address.${key}`);
          }
        });
      }

      Object.entries(formValues).forEach(([key, val]) => {
        if (
          key !== "address" &&
          prevFormValues.current?.[key as keyof FormInputs] !== val
        ) {
          changedFields.push(key);
        }
      });

      changedFields.forEach((field) => {
        mixpanelCustomEvent({
          mixpanelProps: {
            $name: user.name,
            $email: user.email,
            fieldName: field,
          },
          id: user.uid.toString(),
          eventName: MixpanelEventName.userSetupCustomization,
        });
      });

      prevFormValues.current = JSON.parse(JSON.stringify(formValues));
      onFormSubmit(formValues);
    } else if (!prevFormValues.current) {
      prevFormValues.current = JSON.parse(JSON.stringify(formValues));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isValid, formValues, parsedAddress, setIsFormValid]);

  const handleWebsiteBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const formattedValue = formatWebsite(e.target.value);
    setValue("website", formattedValue, { shouldValidate: true });
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedNumber = formatPhoneNumber(e.target.value);
    setValue("phoneNumber", formattedNumber, { shouldValidate: true });
  };

  const handleAddressFieldChange = (field: string, value: string) => {
    const updated = { ...parsedAddress, [field]: value };
    setParsedAddress(updated);
    setValue(`address.${field}` as any, value, {
      shouldValidate: true,
    });
  };

  return {
    register,
    errors,
    addressInputRef,
    selectedAddress,
    parsedAddress,
    isAddressLoading,
    setParsedAddress,
    setValue,
    handleWebsiteBlur,
    handlePhoneChange,
    handleAddressFieldChange,
    showAddressFields,
  };
};
