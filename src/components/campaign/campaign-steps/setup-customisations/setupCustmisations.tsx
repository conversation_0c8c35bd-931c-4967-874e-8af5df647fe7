import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "components/shadcn/ui/card";
import { Input } from "components/shadcn/ui/input";
import { Label } from "components/shadcn/ui/label";
import lang from "lang";
import { useSetupCustomisations } from "./useSetupCustomization";

interface Props {
    onFormSubmit: (data: any) => void;
    setIsFormValid: (isValid: boolean) => void;
}

const SetupCustomisations: React.FC<Props> = ({
    onFormSubmit,
    setIsFormValid,
}) => {
    const { campaign: camapignCopy } = lang;
    const {
        register,
        errors,
        addressInputRef,
        parsedAddress,
        isAddressLoading,
        handleAddressFieldChange,
        handlePhoneChange,
        handleWebsiteBlur,
        showAddressFields,
    } = useSetupCustomisations(onFormSubmit, setIsFormValid);

    return (
        <div className="w-full text-left shadow-none">
            <CardHeader>
                <CardTitle className="text-xl font-semibold">
                    Setup Customisations
                </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-6">
                <div className="flex flex-col gap-2">
                    <Label htmlFor="companyName" className="text-sm text-[#344054]">
                        {camapignCopy.labelForCompanyNAme}
                    </Label>
                    <Input id="companyName" {...register("holdingCompany")} placeholder="Bizcrunch" />
                    {errors.holdingCompany && (
                        <p className="text-red-500 text-sm">{errors.holdingCompany.message}</p>
                    )}
                </div>

                <div className="flex flex-col gap-2">
                    <Label htmlFor="phone" className="text-sm text-[#344054]">
                        {camapignCopy.phoneNumber}
                    </Label>
                    <Input
                        id="phone"
                        type="tel"
                        {...register("phoneNumber", { onChange: handlePhoneChange })}
                        placeholder="1234 123 123"
                    />
                    {errors.phoneNumber && (
                        <p className="text-red-500 text-sm">{errors.phoneNumber.message}</p>
                    )}
                </div>

                <div className="flex flex-col gap-2">
                    <Label htmlFor="website" className="text-sm text-[#344054]">
                        {camapignCopy.website}
                    </Label>
                    <Input
                        id="website"
                        type="url"
                        {...register("website")}
                        placeholder="www.bizcrunch.co"
                        onBlur={handleWebsiteBlur}
                    />
                    {errors.website && (
                        <p className="text-red-500 text-sm">{errors.website.message}</p>
                    )}
                </div>

                <div className="flex flex-col gap-2">
                    <Label htmlFor="autocomplete" className="text-sm text-[#344054]">
                        Address
                    </Label>
                    <Input
                        id="autocomplete"
                        placeholder="Start typing address..."
                        type="text"
                        ref={addressInputRef}
                    />
                </div>

                {showAddressFields && parsedAddress && (
                    <div className="flex flex-col gap-2 mt-4">
                        <Label className="text-sm text-[#344054]">Address Details</Label>

                        {isAddressLoading ? (
                            <div className="animate-accordion-down space-y-2">
                                {[...Array(6)].map((_, i) => (
                                    <div key={i} className="h-10 bg-gray-200 rounded-md" />
                                ))}
                            </div>
                        ) : (
                            (["line1", "line2", "line3", "city", "postalCode", "country"] as const).map((field) => (
                                <div key={field} className="flex flex-col gap-1">
                                    <Input
                                        placeholder={field.charAt(0).toUpperCase() + field.slice(1)}
                                        {...register(`address.${field}`)}
                                        value={parsedAddress[field]}
                                        onChange={(e) => handleAddressFieldChange(field, e.target.value)}
                                    />
                                    {errors.address?.[field] && (
                                        <p className="text-red-500 text-sm">{errors.address[field]?.message}</p>
                                    )}
                                </div>
                            ))
                        )}
                    </div>
                )}
            </CardContent>
        </div>
    );
};

export default SetupCustomisations;
