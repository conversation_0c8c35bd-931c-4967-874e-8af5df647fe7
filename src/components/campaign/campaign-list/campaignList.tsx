import React, { useEffect } from "react";
import "../../../styling/saved.css";
import { But<PERSON> } from "components/shadcn/ui/button";
import { CampaignsTable } from "../campaign-table/campaignTable";
import SvgFolderIcon from "components/common/iconComponents/folderIcon";
import Loader from "components/common/loader";
import { Alert } from "components/shadcn/ui/alert";
import SvgWarningIconWithOutline from "components/common/iconComponents/warningIconWithOutline";
import { MailCheckIcon } from "lucide-react";
import { useCampaignList } from "./useCampaignList";
import confetti from "canvas-confetti";
import { STATUS } from "../types";

interface CampaignListProps { }

const CampaignList: React.FC<CampaignListProps> = () => {
    const {
        campaignCopy,
        user,
        collections,
        campaignList,
        loading,
        loadingCampaign,
        isCampaignSaved,
        existingDomainDetails,
        disableStartCampaign,
        handleSearchNavigation,
        handleNewCampaign,
    } = useCampaignList();

    const isLoading = loading || loadingCampaign;
    const hasCampaigns = collections.length > 0 && campaignList.length > 0;
    const showDomainWarning =
        disableStartCampaign && !loading && existingDomainDetails.length > 0;

    useEffect(() => {
        const showConfetti = campaignList.some((c: any) => c.status === STATUS.WARMING);
        if (isCampaignSaved?.saveCampaignSuccess && showConfetti) {
            const timeout = setTimeout(() => {
                confetti({
                    particleCount: 100,
                    spread: 360,
                    gravity: 0.5,
                    shapes: ["star"],
                    colors: ["#FFE400", "#FFBD00", "#E89400"],
                });
            }, 500);

            return () => clearTimeout(timeout);
        }
    }, [isCampaignSaved?.saveCampaignSuccess, campaignList]);

    return (
        <div className="fullScreen">
            {isLoading && <Loader />}

            <div className="container max-w-[80%]">
                <div className="savedFiltersScreen">
                    <div className="sfTitles flex flex-row gap-5 justify-between">
                        <div className="display-sm semibold text-gray-900">
                            {campaignCopy.campaigns}
                        </div>

                        {collections.length > 0 && (
                            <Button variant="primary" onClick={handleNewCampaign}>
                                <div className="text-white font-inter text-sm font-semibold leading-5 text-left underline-from-font">
                                    {campaignCopy.newCampaign}
                                </div>
                            </Button>
                        )}
                    </div>

                    {showDomainWarning && (
                        <div className="w-full flex justify-center items-center">
                            <div className="rounded-[16px] w-[fit-content] flex justify-center items-center align-middle border border-[#FEEE95] p-[12px] gap-[24px] bg-[#FEF7C3] text-sm font-semibold">
                                {campaignCopy.domainRegistrationInProgressForEmailCampaign}
                            </div>
                        </div>
                    )}
                    {isCampaignSaved?.saveCampaignSuccess && (
                        <Alert
                            variant={"success"}
                            icon={<MailCheckIcon className="text-[#067647]" />}
                            title={`Congrats, ${user?.name}! You have launched your campaign!`}
                            description={campaignCopy.campaignSuccessBannerSubtext}
                        />
                    )}

                    {isCampaignSaved?.subscriptionActivated && (
                        <Alert
                            variant={"success"}
                            icon={<MailCheckIcon className="text-[#067647]" />}
                            title={campaignCopy.subscriptionActivated}
                            description={campaignCopy.subscriptionActivatedSubtext}
                        />
                    )}

                    {isCampaignSaved?.saveCampaignError && (
                        <Alert
                            variant={"destructive"}
                            icon={<SvgWarningIconWithOutline className="h-5 w-5 mr-3" />}
                            title={campaignCopy.campaignErrorBannerHeader}
                            description={isCampaignSaved.errorMessage}
                        />
                    )}

                    {!loading &&
                        (hasCampaigns ? (
                            <CampaignsTable campaignList={campaignList} />
                        ) : (
                            <div className="w-full flex flex-col gap-8 items-center min-h-[50vh] justify-center">
                                <SvgFolderIcon />
                                <div className="flex flex-col gap-2">
                                    <p className="text-lg font-semibold font-[InstrumentSans] text-center leading-[28px]">
                                        {campaignCopy.noCollectionsYet}
                                    </p>
                                    <p className="text-sm font-normal font-[Inter] text-center leading-[20px] decoration-none decoration-[from-font]">
                                        {campaignCopy.noCollectionsSubText}
                                    </p>
                                </div>
                                <Button variant="primary" onClick={handleSearchNavigation}>
                                    <div className="font-inter text-sm font-semibold leading-5 text-left text-white">
                                        {campaignCopy.goToSearch}
                                    </div>
                                </Button>
                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
};

export default CampaignList;