import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { selectCollections } from "components/collection/collectionSlice";
import {
  setCampaignListWithDetails,
  setEmailTemplates,
  setLetterTemplate,
} from "../campaignSlice";
import {
  getCampaignList,
  getEmailTemplates,
  getLetterTemplate,
} from "../services";
import { DOMAIN_STATUS } from "../types";
import lang from "lang";

export const useCampaignList = () => {
  const { campaign: campaignCopy } = lang;
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const collections = useSelector(selectCollections);

  const [campaignList, setCampaignList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingCampaign, setLoadingCampaign] = useState(false);

  const isCampaignSaved = location.state;
  const existingDomainDetails = JSON.parse(
    localStorage.getItem("domainSettings") || "{}"
  );

  const domainStatus = Array.isArray(existingDomainDetails)
    ? existingDomainDetails.every(
        (domain) =>
          domain.status === DOMAIN_STATUS.DOMAIN_READY ||
          domain.status === DOMAIN_STATUS.SET_PREFERRED
      )
    : false;

  const disableStartCampaign = !domainStatus;

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        const [emailTemplateData, letterTemplateResponse] = await Promise.all([
          getEmailTemplates(),
          getLetterTemplate(),
        ]);

        if (emailTemplateData) {
          dispatch(setEmailTemplates(emailTemplateData));
        }

        if (letterTemplateResponse) {
          dispatch(setLetterTemplate(letterTemplateResponse));
        }

        if (collections.length > 0) {
          setLoadingCampaign(true);
          const fetchedCampaignList = await getCampaignList();

          if (fetchedCampaignList.length === 0) {
            navigate(`../campaignLanding`, { replace: true });
          } else {
            setCampaignList(fetchedCampaignList);
            dispatch(setCampaignListWithDetails(fetchedCampaignList));
          }
          setLoadingCampaign(false);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSearchNavigation = () => {
    navigate(`../`, { replace: true });
  };

  const handleNewCampaign = () => {
    navigate(`../campaignLanding`, { replace: true });
  };

  return {
    campaignCopy,
    user,
    navigate,
    collections,
    campaignList,
    loading,
    loadingCampaign,
    isCampaignSaved,
    existingDomainDetails,
    disableStartCampaign,
    handleSearchNavigation,
    handleNewCampaign,
  };
};
