import React, { useContext } from "react";
import { cn } from "components/lib/utils"
import { ParentContext } from "components/constants/ParentContext";

interface Step {
    id: number;
    name: string;
}

interface StepperProps {
    currentStep: number;
    steps: Step[];
    fulfilledSteps?: number[];
    onStepClick?: (stepId: number) => void;
}

export default function Stepper({ currentStep = 1, steps, fulfilledSteps, onStepClick }: StepperProps) {
    const context = useContext<any>(ParentContext);

    return (
        <div className="w-full max-w-4xl mx-auto px-4">
            <div className="flex items-center justify-between relative">
                <div
                    className="absolute left-0 top-1/2 h-[2px] w-full -translate-y-1/2 bg-[#EAECF0]"
                    style={{
                        left: `calc(4rem / 2)`,
                        right: `calc(4rem / 2)`,
                        width: `calc(100% - 4rem)`,
                        transform: "translateY(-50%)",
                        top: "14px",
                    }}
                />
                {steps.map((step) => {
                    const isFulfilled = fulfilledSteps?.includes(step.id);
                    const isCurrent = currentStep === step.id;

                    const isClickable = isFulfilled && onStepClick;

                    return (
                        <div
                            key={step.id}
                            className={cn(
                                "relative flex flex-col items-center cursor-default",
                                isClickable && "cursor-pointer"
                            )}
                            onClick={() => {
                                if (isClickable) {
                                    onStepClick?.(step.id);
                                }
                            }}
                        >
                            <div
                                className={cn(
                                    "w-8 h-8 rounded-full flex items-center justify-center z-10 border-2 transition-all",
                                    currentStep >= step.id
                                        ? context.isHarbour
                                            ? "bg-[#E6F7FB] border-[#0078AC]"
                                            : "bg-[#FFF1F3] border-[#E31B53]"
                                        : "bg-white border-[#EAECF0]"
                                )}
                            >
                                {currentStep > step.id ? (
                                    <CheckIcon
                                        className={context.isHarbour
                                            ? "w-5 h-5 text-[#0078AC]"
                                            : "w-5 h-5 text-[#E31B53]"
                                        }
                                    />
                                ) : isCurrent ? (
                                    <div
                                        className={context.isHarbour
                                            ? "w-2.5 h-2.5 rounded-full bg-[#0078AC]"
                                            : "w-2.5 h-2.5 rounded-full bg-[#E31B53]"
                                        }
                                    />
                                ) : (
                                    <div className="w-2.5 h-2.5 rounded-full bg-[#EAECF0]" />
                                )}
                            </div>
                            {isCurrent}
                            <span
                                className={cn(
                                    "mt-2 text-sm whitespace-nowrap transition-all",
                                    isCurrent
                                        ? "text-[--primary-700] font-medium"
                                        : "text-[#344054]",
                                    isClickable && "hover:underline"
                                )}
                            >
                                {step.name}
                            </span>
                        </div>
                    );
                })}

            </div>
        </div>
    );
}

function CheckIcon({ className }: { className?: string }) {
    return (
        <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
    );
}
