import "../styling/header.css";
import React, { useEffect, useState, useContext } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import HeaderPopUp from "./popUps/headerPopUp";
import { ParentContext } from "./constants/ParentContext";
import {
  bizCrunchSiteUrl,
  fundMyDealUrl,
  showCampaignFeature,
  showDealTracker,
  showNewPaymentFlow,
  showOutreachFeature,
} from "./utils/network/endpoints";
import { useDispatch, useSelector } from "react-redux";
import { resetCampaignState } from "./campaign/campaignSlice";
import { Progress } from "./shadcn/ui/progress";
import { selectCurrentSubscription } from "./subscription/subscriptionSlice";
import { SUBSCRIPTION_STATUS } from "./subscription/types";
import TutorialBubble from "./Search/Basic/Search Bubbles/TutorialBubble";
import { Dict } from "mixpanel-browser";
import { mixpanelCustomEvent } from "./mixpanel/eventTriggers";
import { MixpanelEventName } from "./mixpanel/types";
import { auth } from "index";

interface HeaderProps {
  loggedIn: any;
  user: any;
}

const Header: React.FC<HeaderProps> = ({ loggedIn, user }) => {
  // #region CONSTANTS & STATE VARIABLES
  const navigate = useNavigate();
  const location = useLocation();

  const [initials, setInitials] = useState("");
  const [userVar, setUserVar] = useState(user);
  const [selected, setSelected] = useState<any | null>(null);
  const [showPopUp, setShowPopUp] = useState(false);
  const [endpoint, setEndpoint] = useState("");
  const context = useContext<any>(ParentContext);

  const currentSubscription = useSelector(selectCurrentSubscription);
  const dispatch = useDispatch();

  const mixpanelProps: Dict = {
    $name: `${user?.name}`,
    //$distinct_id: user?.uid,
    $email: user?.email,
  };

  const theme = context.isHarbour
    ? "harbour"
    : context.isBiz4Biz
      ? "biz4biz"
      : "bizcrunch";

  // #endregion

  useEffect(() => {
    setEndpoint(window.location?.pathname);
  }, [window.location.pathname]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user !== null) {
      let name = user.name;

      let initialsArray: any = [];

      let split = name.split(" ");
      initialsArray.push(split[0][0]);
      if (split.length > 1) {
        initialsArray.push(split[1][0]);
      }
      setInitials(initialsArray.join(""));
      setUserVar(user);
    } else {
      setInitials("");
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    switch (location.pathname) {
      case "/":
        setSelected("search");
        break;
      case "/exports":
        setSelected("exports");
        break;
      case "/savedFilters":
        setSelected("saved");
        break;
      case "/billing":
        setSelected("billing");
        break;
      case "/campaignLanding":
        setSelected("campaign");
        break;
      case "/collections":
        setSelected("collections");
        break;

      default:
        break;
    }
  }, [location]);

  // #region SHOW COMPONENTS

  const handleMixpanelEvent = (tabName: string) => {
    mixpanelCustomEvent({
      mixpanelProps: mixpanelProps,
      id: user?.uid.toString(),
      eventName:
        `User Clicks ${tabName} on Header` as unknown as MixpanelEventName,
    });
  };

  // #endregion

  // #region WEB REQUESTS

  // #endregion

  // #region BUTTONS CLICKED

  const goHome = () => {
    if (!auth?.currentUser?.emailVerified) {
      navigate(`../verify-email`, { replace: true });
    } else {
      if (window.location.pathname === "/login") {
        if (theme === "bizcrunch") {
          window.open(bizCrunchSiteUrl, "_self");
        }
      } else {
        navigate(`../`, { replace: true });
      }
    }
    setSelected("search");

  };

  const goExports = () => {
    if (!auth?.currentUser?.emailVerified) {
      navigate(`../verify-email`, { replace: true });
    } else {
      handleMixpanelEvent("Collection");
      const targetPath = showOutreachFeature ? "/collections" : "/exports";
      navigate(targetPath, { replace: true });
    }
    setSelected(showOutreachFeature ? "collections" : "exports");
  };

  const goAdmin = () => {
    navigate(`../admin`, { replace: true });
    setSelected("admin");
  };

  const goSaved = () => {
    if (!auth?.currentUser?.emailVerified) {
      navigate(`../verify-email`, { replace: true });
    } else {
      handleMixpanelEvent("Saved Filters");
      navigate(`../savedFilters`, { replace: true });
    }
    setSelected("saved");
  };

  const navigateToCampaign = () => {
    if (!auth?.currentUser?.emailVerified) {
      navigate(`../verify-email`, { replace: true });
    } else {
      handleMixpanelEvent("Campaign");
      dispatch(resetCampaignState());
      navigate(`../campaign`, { replace: true });
    }
    setSelected("campaign");
  };

  const goFund = () => {
    if (currentSubscription.status !== SUBSCRIPTION_STATUS.TRAILING) {
      handleMixpanelEvent("Fund My Deal");
      window.open(fundMyDealUrl, "_blank", "noopener,noreferrer");
    }
  };

  const login = () => {
    handleMixpanelEvent("Login");
    navigate(`../login`, { replace: true });
  };

  const goDealTracker = () => {
    if (!auth?.currentUser?.emailVerified) {
      navigate(`../verify-email`, { replace: true });
    } else {
      handleMixpanelEvent("Deal tracker");
      navigate(`../deals`, { replace: true });
    }
    setSelected("deal");
  };

  const nextClickTutorialBubble = (e: any, path: string) => {
    e.preventDefault();
    context.setTutorialStep(path);
  };

  const renderProgressBars = () => {
    return (
      <div className="flex flex-row gap-4">
        {currentSubscription?.userFeatureQuotas?.map((quota) => {
          const progress =
            quota?.usedQuota && quota?.totalQuota
              ? (quota.usedQuota / quota.totalQuota) * 100
              : 0;
          return (
            <div
              key={quota.id}
              className="flex flex-col bg-gray-300 rounded-[8px] p-2"
            >
              <span className="text-gray-700 text-xs font-semibold">
                {quota.featureName}{" "}
                {currentSubscription.status === SUBSCRIPTION_STATUS.TRAILING &&
                  " (Trial)"}
                :
                <span className="font-bold">
                  {" "}
                  {quota.usedQuota}/{quota.totalQuota}
                </span>
              </span>
              <Progress
                value={progress}
                className="mt-2 bg-green-200 h-2 #A11043"
              />
            </div>
          );
        })}
      </div>
    );
  };

  // #endregion

  // #region OTHER

  // #endregion

  return (
    <div className="header">
      <div className="headerLeftSection">
        <img
          className="logoImg"
          src={
            theme === "bizcrunch"
              ? "/assets/logo.png"
              : theme === "harbour"
                ? "/assets/logoHC.png"
                : "/assets/logoB4B.jpg"
          }
          onClick={goHome}
          alt="logo"
        />
        {endpoint !== "/login" && (
          <div
            className={`${currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
              ? "headerLink gray-900"
              : "gray-400 cursorDefault"
              } ${selected === "search" ? "selected" : ""
              } text-sm semibold tooltip`}
            onClick={
              currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
                ? goHome
                : undefined
            }
          >
            <span>Search</span>

            {currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED && (
              <span className="tooltiptext header text-sm regular">
                You must be on a plan to use this feature
              </span>
            )}
          </div>
        )}

        {loggedIn && userVar && showOutreachFeature && showCampaignFeature && (
          <div
            className={`${currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
              ? "headerLink gray-900"
              : "gray-400 cursorDefault"
              } ${selected === "saved" ? "selected" : ""
              } text-sm semibold tooltip`}
            onClick={
              currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
                ? goSaved
                : undefined
            }
          >
            <span>Saved filters </span>
            {currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED && (
              <span className="tooltiptext header text-sm regular">
                You must be on a plan to use this feature
              </span>
            )}
          </div>
        )}
        {loggedIn && userVar && (
          <>
            <div
              className={`${currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
                ? "headerLink gray-900"
                : "gray-400 cursorDefault mr-1 ml-1"
                } ${selected === "exports" || selected === "collections"
                  ? "selected"
                  : ""
                } text-sm semibold tooltip relative`}
              onClick={
                currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
                  ? goExports
                  : undefined
              }
            >
              <span>{showOutreachFeature ? "Collections" : "Exports"}</span>

              {currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED && (
                <span className="tooltiptext header text-sm regular">
                  You must be on a plan to use this feature
                </span>
              )}

              {context.tutorialStep === "collectionlist" && (
                <div
                  className="absolute top-[-40px]"
                  onClick={(e) => e.stopPropagation()}
                >
                  <TutorialBubble
                    step={context.tutorialStep}
                    close={() => context.setTutorialStep("")}
                    next={() =>
                      nextClickTutorialBubble(
                        new MouseEvent("click"),
                        "campaign"
                      )
                    }
                  />
                </div>
              )}
            </div>
          </>
        )}

        {loggedIn && userVar && showOutreachFeature && showCampaignFeature && (
          <>
            <div
              className={`${currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
                ? "headerLink gray-900"
                : "gray-400 cursorDefault mr-1 ml-1"
                } ${selected === "campaign" ? "selected" : ""
                } text-sm semibold tooltip relative`}
              onClick={
                currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
                  ? navigateToCampaign
                  : undefined
              }
            >
              <span>Campaigns</span>
              {currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED && (
                <span className="tooltiptext header text-sm regular">
                  You must be on a plan to use this feature
                </span>
              )}

              {context.tutorialStep === "campaign" && (
                <div
                  className="absolute top-[-40px]"
                  onClick={(e) => e.stopPropagation()}
                >
                  <TutorialBubble
                    step={context.tutorialStep}
                    close={() => context.setTutorialStep("")}
                    next={() =>
                      nextClickTutorialBubble(new MouseEvent("click"), "")
                    }
                  />
                </div>
              )}
            </div>
          </>
        )}

        {loggedIn &&
          userVar &&
          showOutreachFeature &&
          showCampaignFeature &&
          showDealTracker && (
            <div
              className={`${currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED
                ? "gray-400 cursorDefault ml-2"
                : "headerLink gray-900"
                } ${selected === "deal" ? "selected" : ""
                } text-sm semibold tooltip`}
              onClick={
                currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
                  ? goDealTracker
                  : undefined
              }
            >
              <span>Deal Tracker</span>

              {
                currentSubscription.status ===
                SUBSCRIPTION_STATUS.CANCELLED && (
                  <span className="tooltiptext header text-sm regular">
                    You must be on a plan to use this feature
                  </span>
                )}
            </div>
          )}

        {!showNewPaymentFlow && loggedIn && userVar && (
          <div
            className={`${userVar.plan !== "free"
              ? "headerLink gray-900"
              : "gray-400 cursorDefault"
              } ${selected === "fundMyDeal" ? "selected" : ""
              } text-sm semibold tooltip`}
            onClick={goFund}
          >
            <span>Fund My Deal</span>
            {userVar.plan === "free" && (
              <span className="tooltiptext header text-sm regular">
                You must be on a plan to use this feature
              </span>
            )}
          </div>
        )}

        {showNewPaymentFlow && loggedIn && userVar && (
          <div
            className={`${currentSubscription.status === SUBSCRIPTION_STATUS.TRAILING ||
              currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED
              ? "gray-400 cursorDefault ml-2"
              : "headerLink gray-900"
              } ${selected === "fundMyDeal" ? "selected" : ""
              } text-sm semibold tooltip`}
            onClick={
              currentSubscription.status !== SUBSCRIPTION_STATUS.TRAILING &&
                currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED
                ? goFund
                : undefined
            }
          >
            <span>Fund My Deal</span>

            {(currentSubscription.status === SUBSCRIPTION_STATUS.TRAILING ||
              currentSubscription.status === SUBSCRIPTION_STATUS.CANCELLED) && (
                <span className="tooltiptext header text-sm regular">
                  You must be on a plan to use this feature
                </span>
              )}
          </div>
        )}

        {loggedIn && user?.isAdmin && (
          <div
            className={`headerLink ${selected === "admin" ? "selected" : ""
              } text-sm semibold gray-900`}
            onClick={goAdmin}
          >
            Admin
          </div>
        )}
      </div>

      {!loggedIn && (
        <button className="secondaryButton" onClick={login}>
          Login
        </button>
      )}
      {loggedIn && !userVar && (
        <span className="text-sm">Loading account...</span>
      )}
      {loggedIn && userVar && (
        <div className="column ai-start">
          <div className="headerLoggedIn">
            {showNewPaymentFlow &&
              currentSubscription &&
              currentSubscription.status !== SUBSCRIPTION_STATUS.CANCELLED &&
              renderProgressBars()}
            <HeaderPopUp
              user={user}
              isOpen={showPopUp}
              setIsOpen={setShowPopUp}
              trigger={
                <div
                  className="headerName pointer"
                  onClick={() => setShowPopUp(!showPopUp)}
                >
                  {initials}
                </div>
              }
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Header;
