import axiosWithToken from "axiosWithToken";

const baseURL2 = process.env.REACT_APP_BASEURL2;

export const fetchCollections = async () => {
  try {
    const response = await axiosWithToken.get(`${baseURL2}api/collections`);
    return {
      success: true,
      data: response.data,
    };
  } catch (error: any) {
    console.error("Error getting campaign list:", error);
    return {
      success: false,
      error: error?.response?.data?.message || error.message || "Unknown error",
    };
  }
};

export const deleteCollection = async (collectionId: string) => {
  try {
    const response = await axiosWithToken.delete(
      `${baseURL2}api/collections/${collectionId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting collection:", error);
    throw error;
  }
};

export const deleteCompaniesFromCollection = async (
  collectionId: string,
  companyIds: string[]
) => {
  try {
    const response = await axiosWithToken.delete(
      `${baseURL2}api/collections/${collectionId}/companies`,
      {
        data: { companyNumbers: companyIds },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting companies from collection:", error);
    throw error;
  }
};
