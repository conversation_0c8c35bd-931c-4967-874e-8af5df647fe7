import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { fetchCollections, deleteCollection } from "../services";
import { selectCollections, setCollections } from "../collectionSlice";
import { resetCampaignState } from "components/campaign/campaignSlice";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";
import { Dict } from "mixpanel-browser";
import { Collection } from "../types";
import { scrollToTop } from "helpers";
import lang from "lang";

export function useCollectionList() {
  const { collection: collectionCopy } = lang;

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const collections = useSelector(selectCollections);
  const [searchQuery, setSearchQuery] = useState("");
  const [sorting, setSorting] = useState<{ id: string; desc: boolean }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(null);
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deleteCollectionError, setDeleteCollectionError] = useState<{
    showError: boolean;
    errorMessage: string | null;
    statusCode?: number;
    showGuidanceBanner?: boolean;
  }>({
    showError: false,
    errorMessage: null,
    statusCode: undefined,
    showGuidanceBanner: false,
  });

  const user = JSON.parse(localStorage.getItem("user") || "null");

  const mixpanelProps: Dict = {
    $name: user?.name,
    $email: user?.email,
  };

  useEffect(() => {
    const loadCollections = async () => {
      setIsLoading(true);
      const result = await fetchCollections();
      dispatch(setCollections(result.data));
      setIsLoading(false);
    };
    loadCollections();
  }, [dispatch]);

  const filteredData = useMemo(() => {
    if (!searchQuery) return collections;
    return collections.filter((c) =>
      c.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [collections, searchQuery]);

  const handleView = (collection: Collection) => {
    mixpanelCustomEvent({
      mixpanelProps: mixpanelProps,
      id: user?.uid.toString(),
      eventName: MixpanelEventName.userClickedViewCollection,
    });
    navigate(`/collectionDetail`, {
      state: { collectionId: collection.id, collection },
    });
  };

  const handleDelete = async (id: string) => {
    try {
      setIsLoading(true);
      await deleteCollection(id);
      const updatedCollections = await fetchCollections();
      dispatch(setCollections(updatedCollections.data));
      mixpanelCustomEvent({
        mixpanelProps: mixpanelProps,
        id: user?.uid.toString(),
        eventName: MixpanelEventName.userClickedViewCollection,
      });
      setShowDeleteSuccess(true);
      setTimeout(() => setShowDeleteSuccess(false), 5000);
    } catch (error: any) {
      setDeleteCollectionError({
        showError: true,
        errorMessage: error?.response?.data?.message || "An error occurred",
        statusCode: error?.response?.data?.statusCode || 0,
      });
      setTimeout(() => {
        setDeleteCollectionError({
          showError: false,
          errorMessage: null,
          statusCode: error?.response?.data?.statusCode || 0,
        });
      }, 5000);
      mixpanelCustomEvent({
        mixpanelProps: mixpanelProps,
        id: user?.uid.toString(),
        eventName: MixpanelEventName.userGotErrorDeletingCollection,
      });
      console.error("Failed to delete collection:", error);
    } finally {
      setIsLoading(false);
      scrollToTop();
    }
  };

  const handleCampaignNavigation = () => {
    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        newCampaign: "Collection Page",
      },
      id: user?.uid?.toString(),
      eventName: MixpanelEventName.userClicksNewCampaign,
    });
    dispatch(resetCampaignState());
    navigate("../campaignLanding", { replace: true });
  };

  return {
    collectionCopy,
    filteredData,
    sorting,
    setSorting,
    isLoading,
    searchQuery,
    setSearchQuery,
    showDeleteModal,
    setShowDeleteModal,
    selectedCollectionId,
    setSelectedCollectionId,
    handleView,
    handleDelete,
    handleCampaignNavigation,
    showDeleteSuccess,
    deleteCollectionError,
  };
}
