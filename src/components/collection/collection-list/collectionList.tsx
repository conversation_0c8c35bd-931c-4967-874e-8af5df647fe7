import React from "react";
import {
    useReactTable,
    getCoreRowModel,
    getSortedRowModel,
    flexRender,
    ColumnDef,
} from "@tanstack/react-table";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "components/shadcn/ui/table";
import { Button } from "components/shadcn/ui/button";
import { Input } from "components/shadcn/ui/input";
import { ChevronsUpDown, EyeIcon, LockIcon, Trash2 } from "lucide-react";
import DeleteCollectionModal from "../delete-collection/deleteCollectionModal";
import {
    Too<PERSON><PERSON>,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "components/shadcn/ui/tooltip";
import { Alert } from "components/shadcn/ui/alert";
import Loader from "components/common/loader";
import { useCollectionList } from "./useCollectionList";
import { formatDate } from "helpers";
import { Card, CardContent } from "components/shadcn/ui/card";

interface Collection {
    id: string;
    name: string;
    totalCompanies: number;
    emailRecipients: number;
    letterRecipients: number;
    createdAt: string;
    isAddedToCampaign?: boolean;
    isLockedForLetter?: number;
    isLockedForEmail?: number;
    isDeletionApplicable?: number;
}

export function CollectionList() {
    const {
        collectionCopy,
        filteredData,
        sorting,
        setSorting,
        isLoading,
        searchQuery,
        setSearchQuery,
        showDeleteModal,
        setShowDeleteModal,
        selectedCollectionId,
        setSelectedCollectionId,
        handleView,
        handleDelete,
        handleCampaignNavigation,
        showDeleteSuccess,
        deleteCollectionError,
    } = useCollectionList();

    const columns = React.useMemo<ColumnDef<Collection>[]>(
        () => [
            {
                accessorKey: "name",
                header: ({ column }) => (
                    <Button
                        variant="custom"
                        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                        className="flex items-center gap-1 pl-0"
                    >
                        Collection Name
                        <ChevronsUpDown className="h-4 w-4" />
                    </Button>
                ),
                cell: ({ row }) => (
                    <span
                        className="text-sm font-medium text-gray-900 text-left"
                        onClick={() => handleView(row.original)}
                    >
                        {row.getValue("name")}
                    </span>
                ),
            },
            {
                accessorKey: "totalCompanies",
                header: "Total Companies",
            },
            {
                accessorKey: "emailRecipients",
                header: "Email Recipients",
            },
            {
                accessorKey: "letterRecipients",
                header: "Letter Recipients",
            },
            {
                accessorKey: "createdAt",
                header: "Created At",
                cell: ({ getValue }) => formatDate(getValue() as string),
            },
            {
                id: "actions",
                header: "Actions",
                cell: ({ row }) => (
                    <div className="flex gap-2">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        onClick={() => handleView(row.original)}
                                        variant="ghost"
                                    >
                                        <EyeIcon className="h-4 w-4 text-primary-700" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>View collection</TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <div>
                                        <Button
                                            onClick={() => {
                                                if (row.original.isDeletionApplicable) {
                                                    setSelectedCollectionId(row.original.id);
                                                    setShowDeleteModal(true);
                                                }
                                            }}
                                            variant="ghost"
                                            disabled={!Boolean(row.original.isDeletionApplicable)}
                                        >
                                            <Trash2
                                                className={`h-4 w-4 ${!row.original.isDeletionApplicable
                                                    ? "text-gray-400"
                                                    : "text-red-600"
                                                    }`}
                                            />
                                        </Button>
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <span>
                                        {!Boolean(row.original.isDeletionApplicable)
                                            ? "This collection is used in campaign and cannot be deleted"
                                            : "Delete collection"}
                                    </span>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                ),
            },
        ],
        [handleView, setSelectedCollectionId, setShowDeleteModal]
    );

    const table = useReactTable({
        data: filteredData,
        columns,
        state: { sorting },
        onSortingChange: setSorting,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
    });

    return (
        <div className="fullScreen">
            <div className="container max-w-[80%]">
                <div className="savedFiltersScreen space-y-4">
                    <div className="sfTitles flex flex-row items-center justify-between">
                        {isLoading && <Loader />}
                        <h2 className="display-sm semibold gray-900">Collections</h2>
                        <Button variant="primary" onClick={handleCampaignNavigation}>
                            <span className="text-white text-sm font-semibold">
                                New Campaign
                            </span>
                        </Button>
                    </div>

                    {showDeleteSuccess && (
                        <Alert variant="success" title="Collection deleted successfully!" />
                    )}
                    {deleteCollectionError.showError && (
                        <Alert
                            variant="destructive"
                            title="Unable to delete Collection"
                            description={deleteCollectionError.errorMessage as string}
                        />
                    )}

                    {deleteCollectionError.statusCode === 400 &&
                        <Card className="border-none">
                            <CardContent className="pb-0 border-none shadow-none">
                                <ul className="list-disc pl-6 text-left border-none">
                                    {collectionCopy.guidanceBannerSubtext.map((item, index) => (
                                        <li key={index} className="text-gray-700 text-sm mb-1">
                                            {item}
                                        </li>
                                    ))}
                                </ul>
                            </CardContent>
                        </Card>
                    }
                    <Table className="w-full rounded-md border">
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead
                                            key={header.id}
                                            className="text-sm text-gray-600"
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                            <TableRow>
                                <TableCell colSpan={columns.length} className="p-2">
                                    <Input
                                        placeholder="Search collections by name..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="w-full"
                                    />
                                </TableCell>
                            </TableRow>
                        </TableHeader>

                        <TableBody>
                            {table.getRowModel().rows.length > 0 ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell
                                                key={cell.id}
                                                className={`text-sm font-medium text-gray-900 ${cell.column.id === "name" ? "text-left" : ""
                                                    }`}
                                            >
                                                <div className="flex flex-row gap-2 items-center">
                                                    {flexRender(
                                                        cell.column.columnDef.cell,
                                                        cell.getContext()
                                                    )}
                                                    {cell.column.id === "name" &&
                                                        (Boolean(row.original.isLockedForEmail) ||
                                                            Boolean(row.original.isLockedForLetter)) && (
                                                            <TooltipProvider>
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <LockIcon
                                                                            height={16}
                                                                            width={16}
                                                                            className="text-gray-500"
                                                                        />
                                                                    </TooltipTrigger>
                                                                    <TooltipContent>
                                                                        {Boolean(row.original.isLockedForEmail) &&
                                                                            Boolean(row.original.isLockedForLetter)
                                                                            ? "Locked for email and letter campaign"
                                                                            : Boolean(row.original.isLockedForEmail)
                                                                                ? "Locked for email campaign"
                                                                                : "Locked for letter campaign"}
                                                                    </TooltipContent>
                                                                </Tooltip>
                                                            </TooltipProvider>
                                                        )}
                                                </div>
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell
                                        colSpan={columns.length}
                                        className="h-24 text-center text-gray-500"
                                    >
                                        No collections found.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>

                    <DeleteCollectionModal
                        showModal={showDeleteModal}
                        onDelete={() => {
                            if (selectedCollectionId) {
                                handleDelete(selectedCollectionId);
                                setShowDeleteModal(false);
                            }
                        }}
                        onCancel={() => setShowDeleteModal(false)}
                    />
                </div>
            </div>
        </div>
    );
}

export default CollectionList;
