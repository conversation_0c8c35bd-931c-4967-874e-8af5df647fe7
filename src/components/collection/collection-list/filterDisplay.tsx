import React from "react";

interface FiltersDisplayProps {
    savedFilter?: any;
}

const FiltersDisplay: React.FC<FiltersDisplayProps> = React.memo(({ savedFilter }) => {
    if (!savedFilter?.filters?.length) {
        return <div className="text-gray-500 text-sm">No filters applied</div>;
    }

    const getFinancialStrLocal = (turnover: any, turnoverStatus: any) => {
        let str = "";
        if (turnover !== null && turnover !== undefined) {
            str += "£";
            if (Math.abs(turnover) >= 1000000) {
                let millions = turnover / 1000000;
                str += millions + "M";
            } else {
                str += turnover.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }
            if (turnoverStatus === "Estimated") {
                str = "~" + str;
            }
        }
        return str;
    };

    return (
        <>
            {savedFilter.filters.slice(0, 4).map((filter: any, index: number) => {
                let icon = "/assets/singleBuilding.png";
                let text = "";

                if (filter.subType === "location") {
                    text = filter.value.locations.join(", ");
                } else if (filter.type === "range") {
                    text =
                        getFinancialStrLocal(filter.value.min, "") +
                        "-" +
                        getFinancialStrLocal(filter.value.max, "");
                } else if (filter.type === "slider") {
                    text = filter.value.min + "-" + filter.value.max + "%";
                } else if (filter.type === "multiple") {
                    if (filter.id === "2I") {
                        text = filter.value.inputs.join(", ");
                    } else if (filter.id === "2A" && filter.subType === "companyName") {
                        text = filter?.value?.names.join(", ");
                    } else if (
                        filter.title === "SIC Industry name" ||
                        filter.title === "ISIC Industry name"
                    ) {
                        text = filter.value[0]?.title || "";
                    } else {
                        text = filter.value.join(", ");
                    }
                }

                return (
                    <div key={index} className="sfrFilters item flex items-center gap-1">
                        <img className="sfrItemIcon w-4 h-4" src={icon} alt="filterIcon" />
                        <span className="text-sm regular gray-600 sfrItemText">{text}</span>
                    </div>
                );
            })}
        </>
    );
});

export default FiltersDisplay;
