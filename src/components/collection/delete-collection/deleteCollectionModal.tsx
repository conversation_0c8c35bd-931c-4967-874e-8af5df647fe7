import React from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON>ooter, DialogTitle, DialogDescription } from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";

interface DeleteCollectionModalProps {
    showModal: boolean;
    onDelete: () => void;
    onCancel: () => void;
}

const DeleteCollectionModal: React.FC<DeleteCollectionModalProps> = ({ showModal, onDelete, onCancel }) => {
    return (
        <Dialog open={showModal} onOpenChange={onCancel}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle className='text-left text-xl font-semibold'>Confirm Delete Collection ? </DialogTitle>
                    <DialogDescription>
                        Are you sure you want to delete this collection? Once deleted, collection will be deleted permanently.
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button variant="primary" onClick={onDelete}>
                        Delete
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default DeleteCollectionModal;