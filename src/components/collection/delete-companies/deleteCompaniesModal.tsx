import React from 'react';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";

interface DeleteCompaniesModalProps {
    showModal: boolean;
    onDelete: () => void;
    onCancel: () => void;
    selectedCount: number;
}

const DeleteCompaniesModal: React.FC<DeleteCompaniesModalProps> = ({
    showModal,
    onDelete,
    onCancel,
    selectedCount,
}) => {

    return (
        <Dialog open={showModal} onOpenChange={onCancel}>
            <DialogContent className="sm:max-w-[40%]">
                <div className="flex flex-row justify-end">
                    <button onClick={onCancel}>
                        <SvgCloseIcon />
                    </button>
                </div>
                <DialogHeader className="flex flex-row items-center justify-between">
                    <DialogTitle className="text-lg font-semibold text-gray-900">
                        {`Delete ${selectedCount} ${selectedCount === 1 ? 'Company' : 'Companies'}?`}
                    </DialogTitle>
                </DialogHeader>
                <div className="text-gray-900 text-sm font-normal">
                    {
                        `Are you sure you want to delete ${selectedCount} ${selectedCount === 1 ? 'company' : 'companies'} from this collection? This action cannot be undone.`}
                </div>
                <div className="flex justify-end gap-4 mt-4">
                    <Button variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button variant="destructive" onClick={onDelete}>
                        Delete
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default DeleteCompaniesModal;