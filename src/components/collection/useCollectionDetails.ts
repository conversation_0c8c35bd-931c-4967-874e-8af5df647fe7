/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState, useMemo, useEffect, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { SortingState, PaginationState } from "@tanstack/react-table";
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  ColumnDef,
  getPaginationRowModel,
} from "@tanstack/react-table";
import { useDispatch, useSelector } from "react-redux";
import axiosWithToken from "axiosWithToken";
import { getSICTitle } from "components/Search/Basic/SearchBar";
import { SIC_CODES } from "components/Search/Advanced/FilterTypes/siccodes";
import { createExcel } from "localFunctions/exportSheet";
import { convertResponse } from "./convertResponse";
import { columnDataForCollectionDetails } from "./columnData";
import { resetCampaignState } from "components/campaign/campaignSlice";
import { updateCollectionDetails } from "components/campaign/services";
import { updateCollectionDetailsRequest } from "components/campaign/types";
import lang from "lang";
import { showNewPaymentFlow } from "components/utils/network/endpoints";
import { SUBSCRIPTION_STATUS } from "components/subscription/types";
import { selectCurrentSubscription } from "components/subscription/subscriptionSlice";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { Dict } from "mixpanel-browser";
import { MixpanelEventName } from "components/mixpanel/types";
import { deleteCompaniesFromCollection, fetchCollections } from "./services";
import { selectCollections, setCollections } from "./collectionSlice";

interface RowData {
  id: string;
  company: string;
  industry: string;
  revenue: string;
  netProfit: string;
  netProfitPercentage: string;
  debtRatio: string;
  turnover: string;
  turnoverStatus: string;
  netProfitEstimate: string;
  companyNumber: string;
}

export const useCollectionDetails = () => {
  const baseURL2 = process.env.REACT_APP_BASEURL2;
  const { collection: collectionCopy } = lang;

  const location = useLocation();
  const { collectionId, collection } = location.state || {};
  const [tableList, setTableList] = useState<any>([]);
  const [companyJSONList, setCompanyJSONList] = useState<any>([]);
  const [PSCJSONList, setPSCJSONList] = useState<any>([]);
  const [contactJSONList, setContactJSONList] = useState<any>([]);
  const [financialJSONList, setFinancialJSONList] = useState<any>([]);
  const [combinedData, setCombinedData] = useState<RowData[]>([]);
  const [isLoading, setIsloading] = useState(false);
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deleteCompaniesError, setDeleteCompaniesError] = useState<{
    showError: boolean;
    errorMessage: string | null;
    statusCode?: number;
  }>({
    showError: false,
    errorMessage: null,
    statusCode: undefined,
  });

  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const subscriptionDetails = useSelector(selectCurrentSubscription);
  const [sorting, setSorting] = useState<SortingState>([]);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [pageSizeOptions] = useState([10, 20, 50, 100]);

  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedCompanyIds, setSelectedCompanyIds] = useState<string[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const mixpanelProps: Dict = {
    $name: `${user?.name}`,
    $email: user?.email,
  };

  useEffect(() => {
    if (collection?.id) {
      fetchCollectionData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [collectionId, collection]);

  useEffect(() => {
    if (!isEditMode) {
      setSelectedCompanyIds([]);
    }
  }, [isEditMode]);

  useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [combinedData.length]);

  const fetchCollectionData = async () => {
    try {
      setIsloading(true);

      const [companiesResponse, financialsResponse, contactsResponse] =
        await Promise.all([
          axiosWithToken.get(
            `${baseURL2}api/collections/${collectionId}/companies`
          ),
          axiosWithToken.get(
            `${baseURL2}api/collections/${collectionId}/financials`
          ),
          axiosWithToken.get(`${baseURL2}api/collections/${collectionId}/pscs`),
        ]);

      const companies = companiesResponse.data;
      const financials = financialsResponse.data;
      const contacts = contactsResponse.data;

      const latestFinancialsMap = financials.reduce(
        (map: any, financial: any) => {
          const existing = map[financial.companyNumber];
          if (
            !existing ||
            new Date(financial.financialYear) > new Date(existing.financialYear)
          ) {
            map[financial.companyNumber] = financial;
          }
          return map;
        },
        {}
      );

      const mergedDataWithLatestFinancial = companies.map((company: any) => {
        const financial = latestFinancialsMap[company.companyNumber] || {};
        const { turnover, turnoverStatus } = formatTurnover(financial);
        const { netProfitPercentage } = getNetProfitPercentage(financial);
        return {
          ...company,
          ...financial,
          industry: getSICTitle(company.SIC, SIC_CODES)?.title,
          turnover,
          turnoverStatus,
          netProfitPercentage,
        };
      });

      setCombinedData(mergedDataWithLatestFinancial);

      const mergedDataWithAllRecords = companies.flatMap((company: any) => {
        const uniqueFinancialsForCompany = Array.from(
          new Map(
            financials
              .filter(
                (financial: any) =>
                  financial.companyNumber === company.companyNumber
              )
              .map((financial: any) => [
                `${financial.companyNumber}-${financial.financialYear}`,
                financial,
              ])
          ).values()
        );

        const uniqueContacts = contacts.filter(
          (contact: any) => contact.companyNumber === company.companyNumber
        );

        const financialRecords =
          uniqueFinancialsForCompany.length > 0
            ? uniqueFinancialsForCompany
            : [{ financialYear: "N/A" }];

        return financialRecords.flatMap((financial: any) => {
          const formattedFinancial = {
            ...company,
            ...financial,
            industry: getSICTitle(company.SIC, SIC_CODES)?.title,
            ...formatTurnover(financial),
          };

          if (uniqueContacts.length === 0) {
            return formattedFinancial;
          }

          return uniqueContacts.map((contact: any) => ({
            ...formattedFinancial,
            ...contact,
          }));
        });
      });

      const {
        tableList,
        companyList,
        PSCTableList,
        PSCList,
        contactList,
        financialsList,
      } = convertResponse(mergedDataWithAllRecords);

      setIsloading(false);
      setTableList(tableList);
      setCompanyJSONList(companyList);
      setPSCJSONList(PSCList);
      setContactJSONList(contactList);
      setFinancialJSONList(financialsList);
    } catch (error) {
      console.error("Error fetching data:", error);
      setIsloading(false);
    }
  };

  const formatTurnover = (company: any) => {
    let turnOverStatus = "Reported";
    if (company.revenue === null) {
      var tradeDebtors = company.tradeDebtors;
      if (!company.tradeDebtors) {
        tradeDebtors = company.currentAssets * 0.75;
        company.tradeDebtors = tradeDebtors;
      }

      let lowRange = Math.floor((tradeDebtors * 6) / 100000) * 100000;
      let highRange = Math.floor((tradeDebtors * 10) / 100000) * 100000;

      company.turnover = lowRange + " - " + highRange;
      turnOverStatus = "Estimated";
    } else {
      company.turnover = company.revenue;
    }
    return { turnover: company.turnover, turnoverStatus: turnOverStatus };
  };

  const getNetProfitPercentage = (item: any) => {
    if (!item.netProfitPercentage && item.netProfit) {
      item.netProfitPercentage = (
        (item.netProfit / item.turnover) *
        100
      ).toFixed(2);
      if ((item.turnover + "").includes(" - ")) {
        let split = item.turnover.split(" - ");
        let middle = (+split[0] + +split[1]) / 2;
        item.netProfitPercentage = ((item.netProfit / middle) * 100).toFixed(2);
      }
    }

    if (item.netProfitPercentage === null) {
      item.netProfitPercentage = "Not Available";
    }

    if (item.netProfitPercentage && parseFloat(item.netProfitPercentage) > 80) {
      item.netProfitPercentage = null;
      item.netProfit = null;
      item.netProfitEstimate = false;
    }
    return item;
  };

  const createExcelExport = () => {
    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        source: "Collection Detail Page",
      },
      id: user?.uid.toString(),
      eventName: MixpanelEventName.userClicksOnDownloadCollection,
    });
    const requestData: updateCollectionDetailsRequest = {
      name: collection.name,
      description: collection.description,
      exportedOn: new Date().toISOString(),
    };
    updateCollectionDetails(requestData, collectionId);

    companyJSONList.forEach((element: any) => {
      element.emailCount = null;
      element.validEmailCount = null;
    });

    contactJSONList.forEach((element: any) => {
      if (element.email === "null") {
        element.email = "";
      }
      if (element.linkedIn === "null") {
        element.linkedIn = "";
      }

      const shouldRestrictEmail =
        (!showNewPaymentFlow && user.plan === "free") ||
        (showNewPaymentFlow &&
          subscriptionDetails?.status === SUBSCRIPTION_STATUS.TRAILING);

      if (shouldRestrictEmail) {
        element.email = "Upgrade to access email";
        element.emailStatus = "N/A";
      }
    });

    PSCJSONList.forEach((element: any) => {
      if (element.email === "null") {
        element.email = "";
      }

      const shouldRestrictEmail =
        (!showNewPaymentFlow && user.plan === "free") ||
        (showNewPaymentFlow &&
          subscriptionDetails?.status === SUBSCRIPTION_STATUS.TRAILING);

      if (shouldRestrictEmail) {
        element.email = "Upgrade to access email";
        element.emailStatus = "N/A";
      }
    });

    createExcel(
      `${collection.name}`,
      companyJSONList,
      contactJSONList,
      PSCJSONList,
      financialJSONList
    );
  };

  const columns = useMemo<ColumnDef<RowData>[]>(() => {
    return columnDataForCollectionDetails;
  }, []);

  const data = useMemo(() => {
    return combinedData;
  }, [combinedData]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: false,
  });

  const handleCampaignNavigation = () => {
    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
        source: "Collection Detail Page",
      },
      id: user?.uid.toString(),
      eventName: MixpanelEventName.userClicksNewCampaign,
    });
    dispatch(resetCampaignState());
    navigate("/campaignLanding", { state: { collectionId: collectionId } });
  };

  const handleBackNavigation = () => {
    navigate("/collections");
  };

  const handleSelectCompany = (companyNumber: string, checked: boolean) => {
    setSelectedCompanyIds((prev) => {
      if (checked) {
        return [...prev, companyNumber];
      } else {
        return prev.filter((id) => id !== companyNumber);
      }
    });
  };

  const handleSelectAllCompanies = (checked: boolean) => {
    if (checked) {
      const visibleCompanyIds = table
        .getRowModel()
        .rows.map((row) => row.original.companyNumber);
      setSelectedCompanyIds((prev) => {
        const uniqueIds = new Set([...prev, ...visibleCompanyIds]);
        return Array.from(uniqueIds);
      });
    } else {
      const visibleCompanyIds = new Set(
        table.getRowModel().rows.map((row) => row.original.companyNumber)
      );
      setSelectedCompanyIds((prev) =>
        prev.filter((id) => !visibleCompanyIds.has(id))
      );
    }
  };

  const isAllSelected = useMemo(() => {
    const visibleRows = table.getRowModel().rows;
    if (visibleRows.length === 0) return false;

    return visibleRows.every((row) =>
      selectedCompanyIds.includes(row.original.companyNumber)
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [table.getRowModel().rows, selectedCompanyIds]);

  const handleCancelEdit = () => {
    setIsEditMode(false);
    setSelectedCompanyIds([]);
  };

  const handleDeleteCompanies = async () => {
    if (selectedCompanyIds.length === 0) return;

    try {
      setIsloading(true);

      const deleteSucess = await deleteCompaniesFromCollection(
        collectionId,
        selectedCompanyIds
      );
      if (deleteSucess) {
        const updatedCollections = await fetchCollections();
        dispatch(setCollections(updatedCollections.data));
        mixpanelCustomEvent({
          mixpanelProps: {
            ...mixpanelProps,
            source: "Collection Detail Page",
            companies_deleted: selectedCompanyIds.length,
          },
          id: user?.uid.toString(),
          eventName: MixpanelEventName.userDeletesCompaniesFromCollection,
        });

        await fetchCollectionData();

        setShowDeleteModal(false);
        setIsEditMode(false);
        setSelectedCompanyIds([]);
        setShowDeleteSuccess(true);
        setTimeout(() => setShowDeleteSuccess(false), 5000);
      }
    } catch (error: any) {
      console.error("Error deleting companies:", error);
      setShowDeleteModal(false);
      setDeleteCompaniesError({
        showError: true,
        errorMessage: error?.response?.data?.message || "An error occurred",
        statusCode: error?.response?.data?.statusCode || 0,
      });
      setTimeout(() => {
        setDeleteCompaniesError({
          showError: false,
          errorMessage: null,
          statusCode: error?.response?.data?.statusCode || 0,
        });
      }, 5000);
    } finally {
      setIsloading(false);
    }
  };

  return {
    baseURL2,
    collectionCopy,
    location,
    collectionId,
    collection,
    tableList,
    companyJSONList,
    PSCJSONList,
    contactJSONList,
    financialJSONList,
    combinedData,
    isLoading,
    user,
    subscriptionDetails,
    mixpanelProps,
    showDeleteSuccess,
    deleteCompaniesError,
    formatTurnover,
    getNetProfitPercentage,
    createExcelExport,
    columns,
    data,
    sorting,
    setSorting,
    pagination,
    setPagination,
    pageSizeOptions,
    navigate,
    dispatch,
    table,
    handleCampaignNavigation,
    handleBackNavigation,
    isEditMode,
    setIsEditMode,
    selectedCompanyIds,
    setSelectedCompanyIds,
    handleSelectCompany,
    handleSelectAllCompanies,
    handleCancelEdit,
    handleDeleteCompanies,
    showDeleteModal,
    setShowDeleteModal,
    isAllSelected,
  };
};
