import type React from "react";
import { useState, useRef, useEffect } from "react";
import { Upload, File as FileIcon, X } from "lucide-react";
import { But<PERSON> } from "components/shadcn/ui/button";
import {
    <PERSON>,
    CardContent,
    CardHeader,
    CardTitle,
} from "components/shadcn/ui/card";
import { Alert } from "components/shadcn/ui/alert";
import { useDispatch, useSelector } from "react-redux";
import { setFileDetails, selectFileDetails } from "components/deal/dealTrackerSlice"; // <<-- Ensure these imports are correct

interface DealFileUploadProps {
    acceptedFileTypes?: string;
    maxFileSize?: number;
    isUploading?: boolean;
    isEditing?: boolean;
}

const DealFileUpload: React.FC<DealFileUploadProps> = ({
    acceptedFileTypes = ".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx,.ppt,.pptx",
    maxFileSize = 25,
    isUploading = false,
    isEditing,
}) => {
    const dispatch = useDispatch();
    const currentSelectedFile = useSelector(selectFileDetails);

    const [fileSelectionError, setFileSelectionError] = useState<string>("");
    const [dragActive, setDragActive] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (!currentSelectedFile && fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    }, [currentSelectedFile]);

    const validateFile = (selectedFile: File): string | null => {
        if (selectedFile.size > maxFileSize * 1024 * 1024) {
            return `File size must be less than ${maxFileSize}MB`;
        }
        if (acceptedFileTypes !== "*") {
            const fileExtension =
                "." + selectedFile.name.split(".").pop()?.toLowerCase();
            const acceptedTypes = acceptedFileTypes.toLowerCase().split(",");
            if (!acceptedTypes.includes(fileExtension)) {
                return `File type not supported. Accepted types: ${acceptedFileTypes}`;
            }
        }
        return null;
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFile = e.target.files?.[0];
        if (selectedFile) {
            const validationError = validateFile(selectedFile);
            if (validationError) {
                dispatch(setFileDetails(null));
                setFileSelectionError(validationError);
                return;
            }
            dispatch(setFileDetails(selectedFile));
            setFileSelectionError("");
        } else {
            dispatch(setFileDetails(null));
            setFileSelectionError("");
        }
    };

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === "dragenter" || e.type === "dragover") {
            setDragActive(true);
        } else if (e.type === "dragleave") {
            setDragActive(false);
        }
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            const droppedFile = e.dataTransfer.files[0];
            const validationError = validateFile(droppedFile);
            if (validationError) {
                dispatch(setFileDetails(null));
                setFileSelectionError(validationError);
                return;
            }
            dispatch(setFileDetails(droppedFile));
            setFileSelectionError("");
        } else {
            dispatch(setFileDetails(null));
            setFileSelectionError("");
        }
    };

    const removeFile = () => {
        dispatch(setFileDetails(null));
        setFileSelectionError("");
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return (
            Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
        );
    };

    return (
        <Card className="w-full max-w-md mx-auto">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Upload className="h-5 w-5" />
                    Select Document to Upload
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div
                    className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${dragActive
                        ? "border-primary bg-primary/5"
                        : "border-muted-foreground/25 hover:border-primary/50"
                        }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                >
                    <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground mb-2">
                        Drag and drop your file here, or click to browse
                    </p>
                    <input
                        ref={fileInputRef}
                        type="file"
                        accept={acceptedFileTypes}
                        onChange={handleFileChange}
                        className="hidden"
                        disabled={isUploading}
                    />
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                    >
                        Choose File
                    </Button>
                    <p className="text-xs text-muted-foreground mt-2">
                        Max file size: {maxFileSize}MB
                    </p>
                </div>

                {currentSelectedFile && (
                    <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                            <FileIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <div className="min-w-0 flex-1">
                                <p className="text-sm font-medium truncate">{currentSelectedFile.name}</p>
                                <p className="text-xs text-muted-foreground">
                                    {formatFileSize(currentSelectedFile.size)}
                                </p>
                            </div>
                        </div>
                        <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={removeFile}
                            disabled={isUploading}
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                )}

                {fileSelectionError && (
                    <Alert
                        variant={"destructive"}
                        title={fileSelectionError}
                    />
                )}
            </CardContent>
        </Card>
    );
};

export default DealFileUpload;