import { SVGProps } from "react";
const SvgSearchIconSubscription = ({ isHarbour, ...props }: SVGProps<SVGSVGElement> & { isHarbour?: boolean }) => (
    <svg
        width="99"
        height="100"
        viewBox="0 0 99 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M12.375 86.7656L30.3188 68.8219M53.625 24.8906C42.2341 24.8906 33 34.1248 33 45.5156M20.625 45.5156C20.625 63.741 35.3996 78.5156 53.625 78.5156C71.8504 78.5156 86.625 63.741 86.625 45.5156C86.625 27.2902 71.8504 12.5156 53.625 12.5156C35.3996 12.5156 20.625 27.2902 20.625 45.5156Z"
            stroke={isHarbour ? "#0BA5EC" : "#F63D68"}
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <defs>
            <linearGradient
                id="paint0_linear_3406_2924"
                x1="86.6262"
                y1="86.7652"
                x2="12.3759"
                y2="12.5147"
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#89123E" />
                <stop offset="1" stopColor="#E31B53" />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgSearchIconSubscription;
