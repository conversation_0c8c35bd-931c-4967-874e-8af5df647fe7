import { SVGProps } from "react";
const SvgSourceIconSubscription = ({ isHarbour, ...props }: SVGProps<SVGSVGElement> & { isHarbour?: boolean }) => (
    <svg
        width="84"
        height="85"
        viewBox="0 0 84 85"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M46.6235 38.5152L7.43602 77.7027M50.8245 7.73636C55.8488 11.0702 60.6781 15.0006 65.1908 19.5133C69.7424 24.065 73.7017 28.9386 77.0539 34.0097M31.1752 25.7118L19.3164 21.7588C17.9507 21.3036 16.4464 21.5933 15.3474 22.5232L3.56167 32.4958C1.14885 34.5374 1.83434 38.4227 4.80014 39.5154L15.9673 43.6296M41.1828 68.8444L45.2971 80.0115C46.3897 82.9773 50.2751 83.6628 52.3167 81.25L62.2893 69.4642C63.2192 68.3653 63.5089 66.861 63.0536 65.4953L59.1007 53.6364M72.8114 2.50693L52.5722 5.88013C50.3868 6.24437 48.3823 7.31855 46.8687 8.93652L19.5898 38.0967C12.5189 45.6553 12.7155 57.4593 20.0343 64.7781C27.3531 72.0968 39.1571 72.2935 46.7157 65.2226L75.8759 37.9437C77.4938 36.4301 78.568 34.4256 78.9323 32.2402L82.3055 12.001C83.2361 6.4172 78.3952 1.5763 72.8114 2.50693Z"
            stroke={isHarbour ? "#0BA5EC" : "#F63D68"}
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <defs>
            <linearGradient
                id="paint0_linear_3406_3032"
                x1="2.09872"
                y1="82.7112"
                x2="82.4191"
                y2="2.38992"
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#89123E" />
                <stop offset="1" stopColor="#E31B53" />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgSourceIconSubscription;
