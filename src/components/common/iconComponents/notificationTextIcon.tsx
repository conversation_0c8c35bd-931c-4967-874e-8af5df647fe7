
import * as React from 'react';
import { SVGProps } from 'react';
const SvgNotificationTextIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg width="24" height="23" viewBox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10.8333 2.16667H7.1C5.13982 2.16667 4.15972 2.16667 3.41103 2.54814C2.75247 2.8837 2.21703 3.41913 1.88148 4.0777C1.5 4.82639 1.5 5.80648 1.5 7.76667V16.4C1.5 18.3602 1.5 19.3403 1.88148 20.089C2.21703 20.7475 2.75247 21.283 3.41103 21.6185C4.15972 22 5.13982 22 7.1 22H15.7333C17.6935 22 18.6736 22 19.4223 21.6185C20.0809 21.283 20.6163 20.7475 20.9519 20.089C21.3333 19.3403 21.3333 18.3602 21.3333 16.4V12.6667M13.1667 17.3333H6.16667M15.5 12.6667H6.16667M21.4749 2.02513C22.8417 3.39196 22.8417 5.60804 21.4749 6.97487C20.108 8.34171 17.892 8.34171 16.5251 6.97487C15.1583 5.60804 15.1583 3.39196 16.5251 2.02513C17.892 0.658291 20.108 0.658291 21.4749 2.02513Z" stroke="#344054" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>


);
export default SvgNotificationTextIcon;
