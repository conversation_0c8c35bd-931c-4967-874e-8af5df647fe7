import { SVGProps } from "react";
const SvgSearchCollectionIcon = ({
    isHarbour,
    ...props
}: SVGProps<SVGSVGElement> & { isHarbour?: boolean }) => (
    <svg
        width="24"
        height="25"
        viewBox="0 0 24 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M21 21.8594L16.65 17.5094M11 8.85938V14.8594M8 11.8594H14M19 11.8594C19 16.2777 15.4183 19.8594 11 19.8594C6.58172 19.8594 3 16.2777 3 11.8594C3 7.4411 6.58172 3.85938 11 3.85938C15.4183 3.85938 19 7.4411 19 11.8594Z"
            stroke={isHarbour ? "#0BA5EC" : "#F63D68"}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
export default SvgSearchCollectionIcon;
