import * as React from 'react';
import { SVGProps } from 'react';

interface CloseIconProps extends SVGProps<SVGSVGElement> {
    color?: string;
}

const SvgCloseIcon = ({ color = "#667085", ...props }: CloseIconProps) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M18 6L6 18M6 6L18 18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
);
export default SvgCloseIcon;
