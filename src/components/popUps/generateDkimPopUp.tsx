import { useState, useCallback, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "components/shadcn/ui/dialog";
import { Input } from "components/shadcn/ui/input";
import { Button } from "components/shadcn/ui/button";
import SvgCloseIcon from "../common/iconComponents/closeIcon";
import lang from "lang";
import { useNavigate } from "react-router-dom";
import SvgNotificationTextIcon from "components/common/iconComponents/notificationTextIcon";
import { Label } from "components/shadcn/ui/label";
import { addDkimRecord } from "components/admin/services";
import { googleWorkspaceUrl } from "components/utils/network/endpoints";

interface CampaignModalProps {
    isOpen: boolean;
    rowData: any;
    onClose: () => void;
    onSave: (name: string) => void;
}

export default function GenrateDKIMModal({ isOpen, rowData, onClose, onSave }: CampaignModalProps) {
    const navigate = useNavigate();
    const [key, setKey] = useState<string>("");
    const [value, setValue] = useState<string>("");

    const adminCopy = useMemo(() => lang.admin, []);

    const handleSave = useCallback(async () => {
        try {
            const payload = {
                name: key,
                value: value,
            };

            const addingDkimRecord = await addDkimRecord(rowData.userId, rowData.recordId, payload);
            if (addingDkimRecord) {
                onClose();
            }
        } catch (e) {
            onClose();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [key, value]);


    const handleCancel = () => {
        onClose();
        navigate("../admin", { replace: true });
    }

    const handleNavigationToWorkspace = useCallback(() => {
        window.open(googleWorkspaceUrl);
    }, []);

    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="min-w-[40%] z-[9999] rounded-[32px] bg-[#F2F4F7]">
                <div className="flex flex-row align-center justify-between">
                    <SvgNotificationTextIcon />
                    <button onClick={onClose}><SvgCloseIcon /></button>
                </div>
                <DialogHeader className="flex flex-row items-center justify-between">
                    <DialogTitle className="text-lg font-semibold text-gray-950">{adminCopy.dkim}</DialogTitle>
                </DialogHeader>

                <div className="border border-[#F2F4F7] bg-white p-6 rounded-3xl flex flex-row items-center content-between gap-3">
                    <div className="text-sm font-regular text-gray-600 w-[50%]">{adminCopy.gotoWorkSpaceText}</div>
                    <button onClick={() => handleNavigationToWorkspace()} className="w-[50%] bg-[#FFF1F3] hover:bg-[#FFF1F3]/90 border border-[#FECCD6] text-primary-700 h-10 rounded-[32px]">
                        <div className="text-primary-700 font-inter text-sm font-semibold leading-5 text-center underline-from-font ">
                            {adminCopy.gotoWorkspace}
                        </div>
                    </button>
                </div>
                <div className="border border-[#F2F4F7] bg-white p-6 rounded-3xl flex flex-col gap-3">
                    <Label>{adminCopy.enterKey}</Label>
                    <Input value={key} onChange={(e) => setKey(e.target.value)} placeholder="default._domainkey" className="w-full" />
                </div>
                <div className="border border-[#F2F4F7] bg-white p-6 rounded-3xl flex flex-col gap-3">
                    <Label>{adminCopy.enterValue}</Label>
                    <Input value={value} onChange={(e) => setValue(e.target.value)} placeholder="v=DKIM1; k=rsa; p=<your-public-dkim-key>" className="w-full" />
                </div>
                <div className="flex justify-end gap-4">
                    <Button variant="outline" onClick={handleCancel}>
                        {adminCopy.cancel}
                    </Button>
                    <Button onClick={() => handleSave()} disabled={!key && !value} variant="primary" className="bg-[#E6007A] hover:bg-[#E6007A]/90 text-white">
                        {adminCopy.save}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
