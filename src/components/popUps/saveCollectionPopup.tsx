import React, { useState, useEffect } from "react";
import {
    Select,
    SelectTrigger,
    SelectContent,
    SelectItem,
    SelectValue,
    SelectGroup,
} from "components/shadcn/ui/select";
import { Button } from "components/shadcn/ui/button";
import SvgDownloadIcon from "components/common/iconComponents/downloadIcon";
import SvgCloseIcon from "../common/iconComponents/closeIcon";
import axiosWithToken from "axiosWithToken";
import Loader from "components/common/loader";
import { useDispatch, useSelector } from "react-redux";
import { selectCollections, setLoading } from "components/collection/collectionSlice";
import CustomNameCollectionModal from "./customNameCollectionPopup";
import { AxiosError } from "axios";
import { showNewPaymentFlow } from "components/utils/network/endpoints";
import { selectCurrentSubscription, updateCurrentSubscription } from "components/subscription/subscriptionSlice";
import { SUBSCRIPTION_STATUS } from "components/subscription/types";
import { CollectionQuotaModal } from "components/subscription/collection-quota-modal";
import { endTrialSubscription, getCurrentSubscription, upgradeTopUp } from "components/subscription/services";
import { Alert } from "components/shadcn/ui/alert";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { Dict } from "mixpanel-browser";
import { MixpanelEventName } from "components/mixpanel/types";
import UnlockFullQuotaNowModal from "components/subscription/confirm-unlock-full-quota/confirmUnlockFullQuota";
import { UpgradeSubscriptionModal } from "components/subscription/upgrade-subscription";
import { LockIcon } from "lucide-react";
import { Collection } from "components/collection/types";

export const SaveCollectionModal = ({
    onCancel,
    onSave,
    reOpenModal,
    selectedCompanies,
}: {
    onCancel: () => void;
    reOpenModal: () => void;
    onSave: (collection: string, updatedExistingCollection: boolean) => void;
    selectedCompanies: string[];
    companies: any;
    collection: any;

}) => {
    const baseURL2 = process.env.REACT_APP_BASEURL2;
    const collections = useSelector(selectCollections);
    let user = JSON.parse(localStorage.getItem("user")!);
    const [selectedCollection, setSelectedCollection] = useState<string | null>(
        null
    );
    const [isLoading, setIsLoading] = useState(false);
    const [isQuotaLoading, setIsQuotaLoading] = useState(false);
    const [companiesTobeCollected, setCompaniesTobeCollected] = useState<any>([]);
    const [isError, setIsError] = useState(false);

    const [showCustomCollectionModal, setShowCustomCollectionModal] =
        useState(false);
    const [errorAlreadyExists, setErrorAlreadyExists] = useState<boolean>(false);

    const [showUpgradeQutaModal, setShowUpgradeQuotaModal] = useState(false);
    const [collectionQuota, setCollectionQuota] = useState<any>(null);
    const subscriptionDetails = useSelector(selectCurrentSubscription);
    const [showExceedCollectionQuotaModal, setShowExceedCollectionQuotaModal] = useState(false);
    const [showTopUpSuccessAlert, setShowTopUpSuccessAlert] = useState(false);
    const [showFreeTrialSuccessAlert, setShowFreeTrialSuccessAlert] = useState(false);
    const [showConfirmUnlockFullQuotaModal, setShowConfirmUnlockFullQuotaModal] = useState(false);
    const dispatch = useDispatch();

    const mixpanelProps: Dict = {
        $name: `${user?.name}`,
        //$distinct_id: user?.uid,
        $email: user?.email,
    };

    useEffect(() => {
        fetchQuota();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (showNewPaymentFlow) {
            const companiesToCollect = subscriptionDetails.status === SUBSCRIPTION_STATUS.TRAILING
                ? selectedCompanies.slice(0, 50)
                : selectedCompanies;
            setCompaniesTobeCollected(companiesToCollect);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCompanies, collections]);

    const fetchQuota = async () => {
        setIsQuotaLoading(true);
        const payload = {
            companyNumbers: selectedCompanies,
        };

        axiosWithToken.post(`${baseURL2}api/collections/check-quota`, payload)
            .then((response) => {
                setIsQuotaLoading(true);
                const quota = response.data;
                setCollectionQuota(quota);
                setIsQuotaLoading(false);
            })
            .catch((error) => {
                console.error("Error fetching quota:", error);
                setIsQuotaLoading(false);
            });
    };

    const handleSave = async (customCollection?: string) => {
        let collectionId;
        setIsError(false);

        let updatedExistingCollection = false;

        if (customCollection && customCollection.trim() !== "") {
            try {
                setIsLoading(true);
                const createCollectionResponse = await axiosWithToken.post(
                    `${baseURL2}api/collections`,
                    {
                        name: customCollection,
                        description: "This is my custom collection.",
                    }
                );

                collectionId = createCollectionResponse.data.id;

            } catch (error: any) {
                if (
                    error instanceof AxiosError &&
                    (error.response?.status === 409 || error.response?.data?.message === "Collection with this name already exists")
                ) {
                    setIsLoading(false);
                    setIsError(true);
                    setShowCustomCollectionModal(true);
                    setErrorAlreadyExists(true);
                    mixpanelCustomEvent({
                        mixpanelProps: mixpanelProps,
                        id: user?.uid.toString(),
                        eventName: MixpanelEventName.userGetsExistingCollectionError,
                    });
                } else {
                    console.error("Error creating custom collection:", error);
                }
                return;
            }
        } else {
            collectionId = collections.find(
                (col: any) => col.name === selectedCollection
            )?.id;

            if (collectionId) {
                updatedExistingCollection = true;
            } else {
                console.error("Selected collection not found.");
                return;
            }
        }
        if (isError && !collectionId) {
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            const response = await axiosWithToken.post(
                `${baseURL2}api/collections/${collectionId}/companies`,
                {
                    companyNumbers: companiesTobeCollected,
                }
            );

            if (response.status === 200 || response.status === 201) {
                setIsLoading(false);
                onSave(
                    customCollection || (selectedCollection as string),
                    updatedExistingCollection
                );
                mixpanelCustomEvent({
                    mixpanelProps: mixpanelProps,
                    id: user?.uid.toString(),
                    eventName: !customCollection ? MixpanelEventName.userUpdatesExistingCollection : MixpanelEventName.userCreatesNewCollection,
                });
            } else {
                setIsLoading(false);
                console.error("Failed to associate companies with the collection.");
            }
        } catch (error) {
            setIsLoading(false);
            console.error("Error associating companies with the collection:", error);
        }
    };

    const handleCustomNameSave = (customCollection: string) => {
        handleSave(customCollection);
    };

    const handleChangePlan = () => {
        setShowExceedCollectionQuotaModal(false);
        setShowUpgradeQuotaModal(true);
    }

    const handleTopUpClick = async (topupDetails: any) => {
        setShowExceedCollectionQuotaModal(false);

        const mixpanelProps = {
            $name: user?.name,
            //$distinct_id: user?.uid,
            $email: user?.email,
            quota_type: "collection",
            topupDetails: topupDetails,
        };

        mixpanelCustomEvent({
            mixpanelProps: {
                ...mixpanelProps,
            },
            id: user?.uid?.toString(),
            eventName: MixpanelEventName.clicksOnTopUpQouta,
        });

        try {
            setIsLoading(true);
            const updatedTopupDetails = await upgradeTopUp(topupDetails);
            if (updatedTopupDetails) {
                setIsLoading(true);
                const currentSubscription = await getCurrentSubscription();
                if (currentSubscription) {
                    dispatch(updateCurrentSubscription(currentSubscription));
                    setIsLoading(false);
                    setShowTopUpSuccessAlert(true);
                    fetchQuota();
                }
                mixpanelCustomEvent({
                    mixpanelProps: {
                        ...mixpanelProps,
                    },
                    id: user?.uid?.toString(),
                    eventName: MixpanelEventName.topUpSuccessFull,
                });
            }
        } catch (error) {
            setIsLoading(false);
            setShowExceedCollectionQuotaModal(false);
            console.error(error);
            mixpanelCustomEvent({
                mixpanelProps: {
                    ...mixpanelProps,
                },
                id: user?.uid?.toString(),
                eventName: MixpanelEventName.topUpError,
            });
        }
    }

    const handleUnlockQuota = async () => {
        setShowConfirmUnlockFullQuotaModal(false);
        try {
            setIsLoading(true);

            const endTrialResponse = await endTrialSubscription(subscriptionDetails?.id);
            if (endTrialResponse) {
                const currentSubscription = await getCurrentSubscription();
                if (currentSubscription) {
                    dispatch(updateCurrentSubscription(currentSubscription));
                    setShowFreeTrialSuccessAlert(true);
                    setIsLoading(true);
                    await fetchQuota();
                    reOpenModal();
                    setIsLoading(false);
                }
            }
            mixpanelCustomEvent({
                mixpanelProps: mixpanelProps,
                id: user?.uid.toString(),
                eventName: MixpanelEventName.clicksUnlickFullQuotaFromSaveCollection,
            });
        } catch (error) {
            setLoading(false);
            console.error('Error ending trial:', error);
        }
    }

    const isButtonDisabled =
        selectedCollection === null || selectedCollection === "create-new";

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50">
            {(isLoading || isQuotaLoading) && <Loader />}
            <div className="bg-gray-100 p-6 shadow-lg w-full max-w-[40%] rounded-[32px]">
                <div className="flex flex-row justify-between">
                    <SvgDownloadIcon />
                    <button onClick={() => onCancel()}>
                        <SvgCloseIcon />
                    </button>
                </div>
                <div className="mb-4 flex flex-col items-start gap-1 border border-gray-100">
                    <h3 className="text-lg font-semibold text-gray-800">
                        Collect {selectedCompanies.length} companies
                    </h3>
                    <p className="text-sm text-gray-600">
                        You will find your downloadable data in the Collections tab.
                    </p>
                </div>

                {showNewPaymentFlow &&
                    !isLoading && collectionQuota?.message !== null &&
                    subscriptionDetails.status === SUBSCRIPTION_STATUS.TRAILING && (
                        <>
                            <div className="flex flex-row justify-between mb-3">
                                <span className="text-sm font-medium text-gray-950">
                                    Available quota
                                </span>
                                <span className="flex flex-row gap-2">
                                    <span className="text-sm font-medium"
                                        style={{ color: "var(--primary-600)" }}
                                    >
                                        {" "}
                                        {collectionQuota?.remainingQuota}/{collectionQuota?.totalQuota}
                                    </span>
                                    <button
                                        className="text-sm font-semibold"
                                        style={{ color: "var(--primary-700)" }}
                                        onClick={() => setShowConfirmUnlockFullQuotaModal(true)}
                                    >
                                        {" "}
                                        Unlock full quota
                                    </button>
                                </span>
                            </div>
                            <div className="mb-6 bg-[--primary-100] rounded-[12px] py-4 px-6 text-sm font-normal text-gray-950">
                                Collection exceeds {collectionQuota?.totalQuota} free trial
                                limit. Change selection or upgrade now.
                            </div>

                        </>
                    )}

                {!isLoading && !isQuotaLoading && showNewPaymentFlow &&
                    collectionQuota?.message !== null &&
                    subscriptionDetails.status === SUBSCRIPTION_STATUS.ACTIVE && (
                        <>
                            <div className="flex flex-row justify-between mb-3">
                                <span className="text-sm font-medium text-gray-950">
                                    Available quota
                                </span>
                                <span className="flex flex-row gap-2">
                                    <span className="text-sm font-medium"
                                        style={{ color: "var(--primary-600)" }}
                                    >
                                        {" "}
                                        {collectionQuota?.remainingQuota}/{collectionQuota?.totalQuota}
                                    </span>
                                    <button
                                        className="text-sm font-semibold"
                                        style={{ color: "var(--primary-700)" }}
                                        onClick={() => setShowExceedCollectionQuotaModal(true)}
                                    >
                                        {" "}
                                        Top up
                                    </button>
                                </span>
                            </div>

                            <div className="mb-6 bg-[--primary-100] rounded-[12px] py-4 px-6 text-sm font-normal text-gray-950">
                                Companies selected exceeds {collectionQuota?.totalQuota} limit.
                                Change your selection or top-up now.
                            </div>
                            <div className="mb-6 bg-[--primary-100] rounded-[12px] py-4 px-6 text-sm font-normal text-gray-950">
                                To save these companies to your collection, increase your quota limit by {collectionQuota?.requiredQuota - collectionQuota?.remainingQuota}.
                            </div>
                        </>
                    )}
                {(showTopUpSuccessAlert || showFreeTrialSuccessAlert) &&
                    <Alert
                        variant={"success"}
                        title={showTopUpSuccessAlert ? "Quota increased sucessfully" : "Free trial ended successfully"}
                    />
                }
                {!isLoading && showNewPaymentFlow && collectionQuota?.message === null && !isQuotaLoading && (
                    <>
                        <div className="flex justify-between items-center h-[40px] border-b pb-2">
                            <span className="text-sm font-regular text-gray-950">
                                Total quota
                            </span>
                            <span className="font-semibold text-lg text-gray-700">
                                {collectionQuota?.totalQuota}
                            </span>
                        </div>
                        <div className="flex justify-between items-center h-[40px] border-b pb-2">
                            <span className="text-sm font-regular text-gray-950">
                                Available quota
                            </span>
                            <span className="font-semibold text-lg text-gray-700">
                                {collectionQuota?.remainingQuota}
                            </span>
                        </div>
                        <div className="flex justify-between items-center h-[40px] border-b pb-2">
                            <span className="text-sm font-regular text-gray-950">
                                Remaining quota after collecting
                            </span>
                            <span className="font-semibold text-lg text-gray-700">
                                {collectionQuota?.remainingQuotaAfterCollection}
                            </span>
                        </div>
                    </>
                )}

                <div className="mb-6 bg-white rounded-[24px] p-6">
                    <label
                        htmlFor="collection"
                        className="flex text-sm font-medium text-gray-700 mb-2"
                    >
                        Choose Collection
                    </label>

                    {showCustomCollectionModal ? (
                        <CustomNameCollectionModal
                            isLoading={isLoading}
                            isOpen={showCustomCollectionModal || errorAlreadyExists}
                            onClose={() => setShowCustomCollectionModal(false)}
                            onSave={(name) => handleCustomNameSave(name)}
                            errorAlreadyExists={errorAlreadyExists}
                            onNewChange={() => setErrorAlreadyExists(false)}
                        />
                    ) : (
                        <Select
                            onValueChange={(value: string) => setSelectedCollection(value)}
                            disabled={collectionQuota?.remainingQuota === 0 || collectionQuota?.message}
                        >
                            <SelectTrigger
                                className={`w-full text-left border border-gray-300 rounded-lg px-4 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:outline-none ${selectedCollection === null
                                    ? "text-gray-500"
                                    : "text-gray-900"
                                    }`}
                            >
                                <SelectValue
                                    placeholder="Choose an existing collection or create a new one"
                                    className="text-left"
                                />
                            </SelectTrigger>
                            <SelectContent className="z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-h-64 overflow-y-auto">
                                <SelectGroup className="flex max-h-48 flex-col justify-start gap-2 overflow-y-auto p-2 font-inter text-base font-medium text-[#101828]">
                                    {collections.map((col: Collection) => (
                                        <SelectItem
                                            //key={col.id}
                                            value={col.name}
                                            disabled={col.isAddedToCampaign}
                                        >
                                            <span className="flex flex-row gap-2 items-center">
                                                {col.name}
                                                {(Boolean(col.isLockedForEmail) || Boolean(col.isLockedForLetter)) &&
                                                    <LockIcon height={16} width={16} className="text-gray-500" />
                                                }
                                            </span>
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                                <div
                                    onClick={() => setShowCustomCollectionModal(true)}
                                    className="sticky bg-gray-100 flex max-h-48 flex-col justify-start gap-2 overflow-y-auto p-2 font-inter text-sm font-medium pl-[2rem] text-[#101828]"
                                >
                                    + Create new one
                                </div>
                            </SelectContent>
                        </Select>
                    )}
                </div>

                {/* {errorAlreadyExists && <div className="text-sm text-primary-600 mb-5"> {errorAlreadyExists} </div>} */}

                <div>
                    <div className="flex gap-4 justify-end">
                        <Button
                            variant="secondary"
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                            onClick={onCancel}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                            onClick={() => handleSave()}
                            disabled={isButtonDisabled || collectionQuota?.message}
                        >
                            Complete
                        </Button>
                    </div>
                </div>

            </div>

            {showExceedCollectionQuotaModal && (
                <CollectionQuotaModal
                    open={showExceedCollectionQuotaModal}
                    close={() => setShowExceedCollectionQuotaModal(false)}
                    changePlan={handleChangePlan}
                    handleTopUpClick={(topUpDetails) => handleTopUpClick(topUpDetails)}
                />
            )}
            {showUpgradeQutaModal && (
                <UpgradeSubscriptionModal
                    open={showUpgradeQutaModal}
                    close={() => setShowUpgradeQuotaModal(false)}
                />
            )}
            {showConfirmUnlockFullQuotaModal &&
                <UnlockFullQuotaNowModal
                    isOpen={showConfirmUnlockFullQuotaModal}
                    onClose={() => setShowConfirmUnlockFullQuotaModal(false)}
                    handleUnlockQuota={() => handleUnlockQuota()}
                />
            }
        </div>
    );
};

export default SaveCollectionModal;
