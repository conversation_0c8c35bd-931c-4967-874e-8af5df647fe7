import React, { useEffect } from "react";
import { Loader2 } from "lucide-react";
import {
    Di<PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "components/shadcn/ui/dialog";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";

interface ShowQuotaAdditionInProgressProps {
    isOpen: boolean;
}

const ShowQuotaAdditionInProgress: React.FC<
    ShowQuotaAdditionInProgressProps
> = ({ isOpen }) => {
    const user = JSON.parse(localStorage.getItem("user")!);

    const mixpanelProps = {
        $name: `${user?.name}`,
        $email: user?.email,
    };

    useEffect(() => {
        if (isOpen) {
            mixpanelCustomEvent({
                mixpanelProps: mixpanelProps,
                id: user?.uid.toString(),
                eventName: MixpanelEventName.addingQuotaInProgressModalShownToUser,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]);

    return (
        <Dialog open={isOpen}>
            <DialogContent className="max-w-md text-center">
                <DialogHeader>
                    <DialogTitle className="text-lg font-bold text-gray-800 text-center">
                        Power-up in progress
                    </DialogTitle>
                </DialogHeader>
                <div className="flex flex-col items-center space-y-4">
                    <Loader2 className="w-12 h-12 text-blue-500 animate-spin" />
                    <p className="text-gray-600">
                        We’re activating your quota now, it’ll be ready in just a moment
                    </p>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default ShowQuotaAdditionInProgress;
