import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { signOut } from 'firebase/auth';
import '../../styling/saved.css';
import { Popover, PopoverContent, PopoverTrigger } from 'components/shadcn/ui/popover';
import { auth } from 'index';
import { Dict } from 'mixpanel-browser';
import { mixpanelCustomEvent } from 'components/mixpanel/eventTriggers';
import { MixpanelEventName } from 'components/mixpanel/types';
import SvgResetPasswordIcon from 'components/common/iconComponents/resetPasswordIcon';
import SvgBillingIcon from 'components/common/iconComponents/billingIcon';
import SvgLogOutIcon from 'components/common/iconComponents/logOutIcon';
import lang from 'lang';
import { persistor } from 'components/common/store/store';

interface User {
  name: string;
  email: string;
  plan: string;
  uid: string;
}

interface HeaderPopUpProps {
  user: User | null;
  isOpen: boolean;
  setIsOpen: (val: boolean) => void;
  trigger: React.ReactNode;
}

const HeaderPopUp: React.FC<HeaderPopUpProps> = ({ user, isOpen, setIsOpen, trigger }) => {
  const { account: accountCopy } = lang;
  const [initials, setInitials] = useState<string>('');
  const [needsVerifying, setNeedsVerifying] = useState<boolean>(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    setNeedsVerifying(location.pathname === '/verify-email');
  }, [location.pathname]);

  useEffect(() => {

    if (user) {
      const split = user.name.split(' ');
      const initialsArray = [split[0][0]];
      if (split.length > 1) initialsArray.push(split[1][0]);
      setInitials(initialsArray.join(''));
    } else {
      setInitials('');
    }
  }, [user]);

  const mixpanelProps: Dict = {
    $name: `${user?.name}`,
    //$distinct_id: user?.uid,
    $email: user?.email,
  };

  const navigateAndClose = (path: string) => {
    const cleanPath = path.replace(/^\.\.?\//, '');
    mixpanelCustomEvent({
      mixpanelProps,
      id: user?.uid?.toString() || '',
      eventName: `User clicks ${cleanPath} from account popup` as unknown as MixpanelEventName,
    });

    setIsOpen(false);
    navigate(path, { replace: true });
  };

  const logout = () => {
    setIsOpen(false);

    mixpanelCustomEvent({
      mixpanelProps,
      id: user?.uid?.toString() || '',
      eventName: `User logged out from platform` as MixpanelEventName,
    });


    signOut(auth)
      .then(() => {
        localStorage.clear();
        persistor.purge();
        navigate('../login', { replace: true });
      })
      .catch((error) => console.error('ERROR', error.message));
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>{trigger}</PopoverTrigger>
      <PopoverContent
        className="p-0 rounded-[32px] w-auto shadow-lg z-9999"
        side="bottom"
        align="end"
        sideOffset={8}
      >
        <div className="rounded-[32px]">

          <div className="width100 row padding20 gap12 borderBox">
            <div className="headerName">{initials}</div>
            <div className="column ai-start">
              <span className="text-sm semibold gray-700">{user?.name}</span>
              <span className="text-sm regular gray-600 breakword">{user?.email}</span>
            </div>
          </div>

          <div className="greyLine" />

          <div className="column ai-start padding10 width100">
            <div
              className={`row jc-start padding10 gap12 width100 ${needsVerifying ? '' : 'hover pointer'
                }`}
              onClick={
                needsVerifying
                  ? undefined
                  : () => navigateAndClose('../passwordReset')
              }
            >
              <SvgResetPasswordIcon />
              <span className={`text-sm medium ${needsVerifying ? 'gray-400' : 'gray-700'}`}>
                {accountCopy.resetPassword}
              </span>
            </div>

            <div
              className={`row jc-start padding10 gap12 width100 ${needsVerifying ? '' : 'hover pointer'
                }`}
              onClick={
                needsVerifying
                  ? undefined
                  : () => navigateAndClose('../billing')
              }
            >
              <SvgBillingIcon />
              <span className={`text-sm medium ${needsVerifying ? 'gray-400' : 'gray-700'}`}>
                {accountCopy.billing}
              </span>
            </div>

            <div className="row jc-start padding10 gap12 width100 hover pointer" onClick={logout}>
              <SvgLogOutIcon />
              <span className="text-sm medium gray-700">{accountCopy.logout}</span>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default HeaderPopUp;
