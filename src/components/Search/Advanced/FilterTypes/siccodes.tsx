export const SIC_CODES = [{ "id": " A", "sicCode": " A", "title": "AGRICULTURE, FORESTRY AND FISHING", "subValues": [{ "id": "01", "sicCode": "01", "title": "Crop and animal production, hunting and related service activities", "subValues": [{ "id": "011", "sicCode": "011", "title": "Growing of non-perennial crops", "subValues": [{ "id": "01110", "sicCode": "01110", "title": "Growing of cereals (except rice), leguminous crops and oil seeds", "subValues": [] }, { "id": "01120", "sicCode": "01120", "title": "Growing of rice", "subValues": [] }, { "id": "01130", "sicCode": "01130", "title": "Growing of vegetables and melons, roots and tubers", "subValues": [] }, { "id": "01140", "sicCode": "01140", "title": "Growing of sugar cane", "subValues": [] }, { "id": "01150", "sicCode": "01150", "title": "Growing of tobacco", "subValues": [] }, { "id": "01160", "sicCode": "01160", "title": "Growing of fibre crops", "subValues": [] }, { "id": "01190", "sicCode": "01190", "title": "Growing of other non-perennial crops", "subValues": [] }] }, { "id": "012", "sicCode": "012", "title": "Growing of perennial crops", "subValues": [{ "id": "01210", "sicCode": "01210", "title": "Growing of grapes", "subValues": [] }, { "id": "01220", "sicCode": "01220", "title": "Growing of tropical and subtropical fruits", "subValues": [] }, { "id": "01230", "sicCode": "01230", "title": "Growing of citrus fruits", "subValues": [] }, { "id": "01240", "sicCode": "01240", "title": "Growing of pome fruits and stone fruits", "subValues": [] }, { "id": "01250", "sicCode": "01250", "title": "Growing of other tree and bush fruits and nuts", "subValues": [] }, { "id": "01260", "sicCode": "01260", "title": "Growing of oleaginous fruits", "subValues": [] }, { "id": "01270", "sicCode": "01270", "title": "Growing of beverage crops", "subValues": [] }, { "id": "01280", "sicCode": "01280", "title": "Growing of spices, aromatic, drug and pharmaceutical crops", "subValues": [] }, { "id": "01290", "sicCode": "01290", "title": "Growing of other perennial crops", "subValues": [] }] }, { "id": "013", "sicCode": "013", "title": "Plant propagation", "subValues": [{ "id": "01300", "sicCode": "01300", "title": "Plant propagation", "subValues": [] }] }, { "id": "014", "sicCode": "014", "title": "Animal production", "subValues": [{ "id": "01410", "sicCode": "01410", "title": "Raising of dairy cattle", "subValues": [] }, { "id": "01420", "sicCode": "01420", "title": "Raising of other cattle and buffaloes", "subValues": [] }, { "id": "01430", "sicCode": "01430", "title": "Raising of horses and other equines", "subValues": [] }, { "id": "01440", "sicCode": "01440", "title": "Raising of camels and camelids", "subValues": [] }, { "id": "01450", "sicCode": "01450", "title": "Raising of sheep and goats", "subValues": [] }, { "id": "01460", "sicCode": "01460", "title": "Raising of swinepigs", "subValues": [] }, { "id": "01470", "sicCode": "01470", "title": "Raising of poultry", "subValues": [] }, { "id": "01490", "sicCode": "01490", "title": "Raising of other animals", "subValues": [] }] }, { "id": "015", "sicCode": "015", "title": "Mixed farming", "subValues": [{ "id": "01500", "sicCode": "01500", "title": "Mixed farming", "subValues": [] }] }, { "id": "016", "sicCode": "016", "title": "Support activities to agriculture and post-harvest crop activities", "subValues": [{ "id": "01610", "sicCode": "01610", "title": "Support activities for crop production", "subValues": [] }, { "id": "01620", "sicCode": "01620", "title": "Support activities for animal production", "subValues": [] }, { "id": "01621", "sicCode": "01621", "title": "Farm animal boarding and care", "subValues": [] }, { "id": "01629", "sicCode": "01629", "title": "Support activities for animal production (other than farm animal boarding and care) nec", "subValues": [] }, { "id": "01630", "sicCode": "01630", "title": "Post-harvest crop activities", "subValues": [] }, { "id": "01640", "sicCode": "01640", "title": "Seed processing for propagation", "subValues": [] }] }, { "id": "017", "sicCode": "017", "title": "Hunting, trapping and related service activities", "subValues": [{ "id": "01700", "sicCode": "01700", "title": "Hunting, trapping and related service activities", "subValues": [] }] }] }, { "id": "02", "sicCode": "02", "title": "Forestry and logging", "subValues": [{ "id": "021", "sicCode": "021", "title": "Silviculture and other forestry activities", "subValues": [{ "id": "02100", "sicCode": "02100", "title": "Silviculture and other forestry activities", "subValues": [] }] }, { "id": "022", "sicCode": "022", "title": "Logging", "subValues": [{ "id": "02200", "sicCode": "02200", "title": "Logging", "subValues": [] }] }, { "id": "023", "sicCode": "023", "title": "Gathering of wild growing non-wood products", "subValues": [{ "id": "02300", "sicCode": "02300", "title": "Gathering of wild growing non-wood products", "subValues": [] }] }, { "id": "024", "sicCode": "024", "title": "Support services to forestry", "subValues": [{ "id": "02400", "sicCode": "02400", "title": "Support services to forestry", "subValues": [] }] }] }, { "id": "03", "sicCode": "03", "title": "Fishing and aquaculture", "subValues": [{ "id": "031", "sicCode": "031", "title": "Fishing", "subValues": [{ "id": "03110", "sicCode": "03110", "title": "Marine fishing", "subValues": [] }, { "id": "03120", "sicCode": "03120", "title": "Freshwater fishing", "subValues": [] }] }, { "id": "032", "sicCode": "032", "title": "Aquaculture", "subValues": [{ "id": "03210", "sicCode": "03210", "title": "Marine aquaculture", "subValues": [] }, { "id": "03220", "sicCode": "03220", "title": "Freshwater aquaculture", "subValues": [] }] }] }] }, { "id": " B", "sicCode": " B", "title": "MINING AND QUARRYING", "subValues": [{ "id": "05", "sicCode": "05", "title": "Mining of coal and lignite", "subValues": [{ "id": "051", "sicCode": "051", "title": "Mining of hard coal", "subValues": [{ "id": "05100", "sicCode": "05100", "title": "Mining of hard coal", "subValues": [] }, { "id": "05101", "sicCode": "05101", "title": "Mining of hard coal from deep coal mines (underground mining)", "subValues": [] }, { "id": "05102", "sicCode": "05102", "title": "Mining of hard coal from open cast coal working (surface mining)", "subValues": [] }] }, { "id": "052", "sicCode": "052", "title": "Mining of lignite", "subValues": [{ "id": "05200", "sicCode": "05200", "title": "Mining of lignite", "subValues": [] }] }] }, { "id": "06", "sicCode": "06", "title": "Extraction of crude petroleum and natural gas", "subValues": [{ "id": "061", "sicCode": "061", "title": "Extraction of crude petroleum", "subValues": [{ "id": "06100", "sicCode": "06100", "title": "Extraction of crude petroleum", "subValues": [] }] }, { "id": "062", "sicCode": "062", "title": "Extraction of natural gas", "subValues": [{ "id": "06200", "sicCode": "06200", "title": "Extraction of natural gas", "subValues": [] }] }] }, { "id": "07", "sicCode": "07", "title": "Mining of metal ores", "subValues": [{ "id": "071", "sicCode": "071", "title": "Mining of iron ores", "subValues": [{ "id": "07100", "sicCode": "07100", "title": "Mining of iron ores", "subValues": [] }] }, { "id": "072", "sicCode": "072", "title": "Mining of non-ferrous metal ores", "subValues": [{ "id": "07210", "sicCode": "07210", "title": "Mining of uranium and thorium ores", "subValues": [] }, { "id": "07290", "sicCode": "07290", "title": "Mining of other non-ferrous metal ores", "subValues": [] }] }] }, { "id": "08", "sicCode": "08", "title": "Other mining and quarrying", "subValues": [{ "id": "081", "sicCode": "081", "title": "Quarrying of stone, sand and clay", "subValues": [{ "id": "08110", "sicCode": "08110", "title": "Quarrying of ornamental and building stone, limestone, gypsum, chalk and slate", "subValues": [] }, { "id": "08120", "sicCode": "08120", "title": "Operation of gravel and sand pits; mining of clays and kaolin", "subValues": [] }] }, { "id": "089", "sicCode": "089", "title": "Mining and quarrying nec", "subValues": [{ "id": "08910", "sicCode": "08910", "title": "Mining of chemical and fertiliser minerals", "subValues": [] }, { "id": "08920", "sicCode": "08920", "title": "Extraction of peat", "subValues": [] }, { "id": "08930", "sicCode": "08930", "title": "Extraction of salt", "subValues": [] }, { "id": "08990", "sicCode": "08990", "title": "Other mining and quarrying nec", "subValues": [] }] }] }, { "id": "09", "sicCode": "09", "title": "Mining support service activities", "subValues": [{ "id": "091", "sicCode": "091", "title": "Support activities for petroleum and natural gas extraction", "subValues": [{ "id": "09100", "sicCode": "09100", "title": "Support activities for petroleum and natural gas extraction", "subValues": [] }] }, { "id": "099", "sicCode": "099", "title": "Support activities for other mining and quarrying", "subValues": [{ "id": "09900", "sicCode": "09900", "title": "Support activities for other mining and quarrying", "subValues": [] }] }] }] }, { "id": " C", "sicCode": " C", "title": "MANUFACTURING", "subValues": [{ "id": "10", "sicCode": "10", "title": "Manufacture of food products", "subValues": [{ "id": "101", "sicCode": "101", "title": "Processing and preserving of meat and production of meat products", "subValues": [{ "id": "10110", "sicCode": "10110", "title": "Processing and preserving of meat", "subValues": [] }, { "id": "10120", "sicCode": "10120", "title": "Processing and preserving of poultry meat", "subValues": [] }, { "id": "10130", "sicCode": "10130", "title": "Production of meat and poultry meat products", "subValues": [] }] }, { "id": "102", "sicCode": "102", "title": "Processing and preserving of fish, crustaceans and molluscs", "subValues": [{ "id": "10200", "sicCode": "10200", "title": "Processing and preserving of fish, crustaceans and molluscs", "subValues": [] }] }, { "id": "103", "sicCode": "103", "title": "Processing and preserving of fruit and vegetables", "subValues": [{ "id": "10310", "sicCode": "10310", "title": "Processing and preserving of potatoes", "subValues": [] }, { "id": "10320", "sicCode": "10320", "title": "Manufacture of fruit and vegetable juice", "subValues": [] }, { "id": "10390", "sicCode": "10390", "title": "Other processing and preserving of fruit and vegetables", "subValues": [] }] }, { "id": "104", "sicCode": "104", "title": "Manufacture of vegetable and animal oils and fats", "subValues": [{ "id": "10410", "sicCode": "10410", "title": "Manufacture of oils and fats", "subValues": [] }, { "id": "10420", "sicCode": "10420", "title": "Manufacture of margarine and similar edible fats", "subValues": [] }] }, { "id": "105", "sicCode": "105", "title": "Manufacture of dairy products", "subValues": [{ "id": "10510", "sicCode": "10510", "title": "Operation of dairies and cheese making", "subValues": [] }, { "id": "10511", "sicCode": "10511", "title": "Liquid milk and cream production", "subValues": [] }, { "id": "10512", "sicCode": "10512", "title": "Butter and cheese production", "subValues": [] }, { "id": "10519", "sicCode": "10519", "title": "Manufacture of milk products (other than liquid milk and cream, butter, cheese) nec", "subValues": [] }, { "id": "10520", "sicCode": "10520", "title": "Manufacture of ice cream", "subValues": [] }] }, { "id": "106", "sicCode": "106", "title": "Manufacture of grain mill products, starches and starch products", "subValues": [{ "id": "10610", "sicCode": "10610", "title": "Manufacture of grain mill products", "subValues": [] }, { "id": "10611", "sicCode": "10611", "title": "Grain milling", "subValues": [] }, { "id": "10612", "sicCode": "10612", "title": "Manufacture of breakfast cereals and cereals-based foods", "subValues": [] }, { "id": "10620", "sicCode": "10620", "title": "Manufacture of starches and starch products", "subValues": [] }] }, { "id": "107", "sicCode": "107", "title": "Manufacture of bakery and farinaceous products", "subValues": [{ "id": "10710", "sicCode": "10710", "title": "Manufacture of bread; manufacture of fresh pastry goods and cakes", "subValues": [] }, { "id": "10720", "sicCode": "10720", "title": "Manufacture of rusks and biscuits; manufacture of preserved pastry goods and cakes", "subValues": [] }, { "id": "10730", "sicCode": "10730", "title": "Manufacture of macaroni, noodles, couscous and similar farinaceous products", "subValues": [] }] }, { "id": "108", "sicCode": "108", "title": "Manufacture of other food products", "subValues": [{ "id": "10810", "sicCode": "10810", "title": "Manufacture of sugar", "subValues": [] }, { "id": "10820", "sicCode": "10820", "title": "Manufacture of cocoa, chocolate and sugar confectionery", "subValues": [] }, { "id": "10821", "sicCode": "10821", "title": "Manufacture of cocoa, and chocolate confectionery", "subValues": [] }, { "id": "10822", "sicCode": "10822", "title": "Manufacture of sugar confectionery", "subValues": [] }, { "id": "10830", "sicCode": "10830", "title": "Processing of tea and coffee", "subValues": [] }, { "id": "10831", "sicCode": "10831", "title": "Tea processing", "subValues": [] }, { "id": "10832", "sicCode": "10832", "title": "Production of coffee and coffee substitutes", "subValues": [] }, { "id": "10840", "sicCode": "10840", "title": "Manufacture of condiments and seasonings", "subValues": [] }, { "id": "10850", "sicCode": "10850", "title": "Manufacture of prepared meals and dishes", "subValues": [] }, { "id": "10860", "sicCode": "10860", "title": "Manufacture of homogenised food preparations and dietetic food", "subValues": [] }, { "id": "10890", "sicCode": "10890", "title": "Manufacture of other food products nec", "subValues": [] }] }, { "id": "109", "sicCode": "109", "title": "Manufacture of prepared animal feeds", "subValues": [{ "id": "10910", "sicCode": "10910", "title": "Manufacture of prepared feeds for farm animals", "subValues": [] }, { "id": "10920", "sicCode": "10920", "title": "Manufacture of prepared pet foods", "subValues": [] }] }] }, { "id": "11", "sicCode": "11", "title": "Manufacture of beverages", "subValues": [{ "id": "110", "sicCode": "110", "title": "Manufacture of beverages", "subValues": [{ "id": "11010", "sicCode": "11010", "title": "Distilling, rectifying and blending of spirits", "subValues": [] }, { "id": "11020", "sicCode": "11020", "title": "Manufacture of wine from grape", "subValues": [] }, { "id": "11030", "sicCode": "11030", "title": "Manufacture of cider and other fruit wines", "subValues": [] }, { "id": "11040", "sicCode": "11040", "title": "Manufacture of other non-distilled fermented beverages", "subValues": [] }, { "id": "11050", "sicCode": "11050", "title": "Manufacture of beer", "subValues": [] }, { "id": "11060", "sicCode": "11060", "title": "Manufacture of malt", "subValues": [] }, { "id": "11070", "sicCode": "11070", "title": "Manufacture of soft drinks; production of mineral waters and other bottled waters", "subValues": [] }] }] }, { "id": "12", "sicCode": "12", "title": "Manufacture of tobacco products", "subValues": [{ "id": "120", "sicCode": "120", "title": "Manufacture of tobacco products", "subValues": [{ "id": "12000", "sicCode": "12000", "title": "Manufacture of tobacco products", "subValues": [] }] }] }, { "id": "13", "sicCode": "13", "title": "Manufacture of textiles", "subValues": [{ "id": "131", "sicCode": "131", "title": "Preparation and spinning of textile fibres", "subValues": [{ "id": "13100", "sicCode": "13100", "title": "Preparation and spinning of textile fibres", "subValues": [] }] }, { "id": "132", "sicCode": "132", "title": "Weaving of textiles", "subValues": [{ "id": "13200", "sicCode": "13200", "title": "Weaving of textiles", "subValues": [] }] }, { "id": "133", "sicCode": "133", "title": "Finishing of textiles", "subValues": [{ "id": "13300", "sicCode": "13300", "title": "Finishing of textiles", "subValues": [] }] }, { "id": "139", "sicCode": "139", "title": "Manufacture of other textiles", "subValues": [{ "id": "13910", "sicCode": "13910", "title": "Manufacture of knitted and crocheted fabrics", "subValues": [] }, { "id": "13920", "sicCode": "13920", "title": "Manufacture of made-up textile articles, except apparel", "subValues": [] }, { "id": "13921", "sicCode": "13921", "title": "Manufacture of soft furnishings", "subValues": [] }, { "id": "13922", "sicCode": "13922", "title": "Manufacture of canvas goods, sacks etc", "subValues": [] }, { "id": "13923", "sicCode": "13923", "title": "Manufacture of household textiles (other than soft furnishings of 13921)", "subValues": [] }, { "id": "13930", "sicCode": "13930", "title": "Manufacture of carpets and rugs", "subValues": [] }, { "id": "13931", "sicCode": "13931", "title": "Manufacture of woven or tufted carpets and rugs", "subValues": [] }, { "id": "13939", "sicCode": "13939", "title": "Manufacture of carpets and rugs (other than woven or tufted) nec", "subValues": [] }, { "id": "13940", "sicCode": "13940", "title": "Manufacture of cordage, rope, twine and netting", "subValues": [] }, { "id": "13950", "sicCode": "13950", "title": "Manufacture of non-wovens and articles made from non-wovens, except apparel", "subValues": [] }, { "id": "13960", "sicCode": "13960", "title": "Manufacture of other technical and industrial textiles", "subValues": [] }, { "id": "13990", "sicCode": "13990", "title": "Manufacture of other textiles nec", "subValues": [] }] }] }, { "id": "14", "sicCode": "14", "title": "Manufacture of wearing apparel", "subValues": [{ "id": "141", "sicCode": "141", "title": "Manufacture of wearing apparel, except fur apparel", "subValues": [{ "id": "14110", "sicCode": "14110", "title": "Manufacture of leather clothes", "subValues": [] }, { "id": "14120", "sicCode": "14120", "title": "Manufacture of workwear", "subValues": [] }, { "id": "14130", "sicCode": "14130", "title": "Manufacture of other outerwear", "subValues": [] }, { "id": "14131", "sicCode": "14131", "title": "Manufacture of men's outerwear, other than leather clothes and workwear", "subValues": [] }, { "id": "14132", "sicCode": "14132", "title": "Manufacture of women's outerwear, other than leather clothes and workwear", "subValues": [] }, { "id": "14140", "sicCode": "14140", "title": "Manufacture of underwear", "subValues": [] }, { "id": "14141", "sicCode": "14141", "title": "Manufacture of men's underwear", "subValues": [] }, { "id": "14142", "sicCode": "14142", "title": "Manufacture of women's underwear", "subValues": [] }, { "id": "14190", "sicCode": "14190", "title": "Manufacture of other wearing apparel and accessories", "subValues": [] }] }, { "id": "142", "sicCode": "142", "title": "Manufacture of articles of fur", "subValues": [{ "id": "14200", "sicCode": "14200", "title": "Manufacture of articles of fur", "subValues": [] }] }, { "id": "143", "sicCode": "143", "title": "Manufacture of knitted and crocheted appare", "subValues": [{ "id": "14310", "sicCode": "14310", "title": "Manufacture of knitted and crocheted hosiery", "subValues": [] }, { "id": "14390", "sicCode": "14390", "title": "Manufacture of other knitted and crocheted apparel", "subValues": [] }] }] }, { "id": "15", "sicCode": "15", "title": "Manufacture of leather and related products", "subValues": [{ "id": "151", "sicCode": "151", "title": "Manufacture of leather and related products", "subValues": [{ "id": "15110", "sicCode": "15110", "title": "Tanning and dressing of leather; dressing and dyeing of fur", "subValues": [] }, { "id": "15120", "sicCode": "15120", "title": "Manufacture of luggage, handbags and the like, saddlery and harness", "subValues": [] }] }, { "id": "152", "sicCode": "152", "title": "Manufacture of footwear", "subValues": [{ "id": "15200", "sicCode": "15200", "title": "Manufacture of footwear", "subValues": [] }] }] }, { "id": "16", "sicCode": "16", "title": "Manufacture of wood and of products of wood and cork, except furniture; manufacture of articles of straw and plaiting materials", "subValues": [{ "id": "161", "sicCode": "161", "title": "Sawmilling and planing of wood", "subValues": [{ "id": "16100", "sicCode": "16100", "title": "Sawmilling and planing of wood", "subValues": [] }] }, { "id": "162", "sicCode": "162", "title": "Manufacture of products of wood, cork, straw and plaiting materials", "subValues": [{ "id": "16210", "sicCode": "16210", "title": "Manufacture of veneer sheets and wood-based panels", "subValues": [] }, { "id": "16220", "sicCode": "16220", "title": "Manufacture of assembled parquet floors", "subValues": [] }, { "id": "16230", "sicCode": "16230", "title": "Manufacture of other builders' carpentry and joinery", "subValues": [] }, { "id": "16240", "sicCode": "16240", "title": "Manufacture of wooden containers", "subValues": [] }, { "id": "16290", "sicCode": "16290", "title": "Manufacture of other products of wood; manufacture of articles of cork, straw and plaiting materials", "subValues": [] }] }] }, { "id": "17", "sicCode": "17", "title": "Manufacture of paper and paper products", "subValues": [{ "id": "171", "sicCode": "171", "title": "Manufacture of pulp, paper and paperboard", "subValues": [{ "id": "17110", "sicCode": "17110", "title": "Manufacture of pulp", "subValues": [] }, { "id": "17120", "sicCode": "17120", "title": "Manufacture of paper and paperboard", "subValues": [] }] }, { "id": "172", "sicCode": "172", "title": "Manufacture of articles of paper and paperboard", "subValues": [{ "id": "17210", "sicCode": "17210", "title": "Manufacture of corrugated paper and paperboard and of containers of paper and paperboard", "subValues": [] }, { "id": "17211", "sicCode": "17211", "title": "Manufacture of corrugated paper and paperboard; manufacture of sacks and bags of paper", "subValues": [] }, { "id": "17219", "sicCode": "17219", "title": "Manufacture of paper and paperboard containers other than sacks and bags", "subValues": [] }, { "id": "17220", "sicCode": "17220", "title": "Manufacture of household and sanitary goods and of toilet requisites", "subValues": [] }, { "id": "17230", "sicCode": "17230", "title": "Manufacture of paper stationery", "subValues": [] }, { "id": "17240", "sicCode": "17240", "title": "Manufacture of wallpaper", "subValues": [] }, { "id": "17290", "sicCode": "17290", "title": "Manufacture of other articles of paper and paperboard", "subValues": [] }] }] }, { "id": "18", "sicCode": "18", "title": "Printing and reproduction of recorded media", "subValues": [{ "id": "181", "sicCode": "181", "title": "Printing and service activities related to printing", "subValues": [{ "id": "18110", "sicCode": "18110", "title": "Printing of newspapers", "subValues": [] }, { "id": "18120", "sicCode": "18120", "title": "Other printing", "subValues": [] }, { "id": "18121", "sicCode": "18121", "title": "Manufacture of printed labels", "subValues": [] }, { "id": "18129", "sicCode": "18129", "title": "Printing (other than printing of newspapers and printing on labels and tags) nec", "subValues": [] }, { "id": "18130", "sicCode": "18130", "title": "Pre-press and pre-media services", "subValues": [] }, { "id": "18140", "sicCode": "18140", "title": "Binding and related services", "subValues": [] }] }, { "id": "182", "sicCode": "182", "title": "Reproduction of recorded media", "subValues": [{ "id": "18200", "sicCode": "18200", "title": "Reproduction of recorded media", "subValues": [] }, { "id": "18201", "sicCode": "18201", "title": "Reproduction of sound recording", "subValues": [] }, { "id": "18202", "sicCode": "18202", "title": "Reproduction of video recording", "subValues": [] }, { "id": "18203", "sicCode": "18203", "title": "Reproduction of computer media", "subValues": [] }] }] }, { "id": "19", "sicCode": "19", "title": "Manufacture of coke and refined petroleum products", "subValues": [{ "id": "191", "sicCode": "191", "title": "Manufacture of coke oven products", "subValues": [{ "id": "19100", "sicCode": "19100", "title": "Manufacture of coke oven products", "subValues": [] }] }, { "id": "192", "sicCode": "192", "title": "Manufacture of refined petroleum products", "subValues": [{ "id": "19200", "sicCode": "19200", "title": "Manufacture of refined petroleum products", "subValues": [] }, { "id": "19201", "sicCode": "19201", "title": "Mineral oil refining", "subValues": [] }, { "id": "19209", "sicCode": "19209", "title": "Other treatment of petroleum products (excluding mineral oil refiningpetrochemicals manufacture)", "subValues": [] }] }] }, { "id": "20", "sicCode": "20", "title": "Manufacture of chemicals and chemical products", "subValues": [{ "id": "201", "sicCode": "201", "title": "Manufacture of basic chemicals, fertilisers and nitrogen compounds, plastics and synthetic rubber in primary forms", "subValues": [{ "id": "20110", "sicCode": "20110", "title": "Manufacture of industrial gases", "subValues": [] }, { "id": "20120", "sicCode": "20120", "title": "Manufacture of dyes and pigments", "subValues": [] }, { "id": "20130", "sicCode": "20130", "title": "Manufacture of other inorganic basic chemicals", "subValues": [] }, { "id": "20140", "sicCode": "20140", "title": "Manufacture of other organic basic chemicals", "subValues": [] }, { "id": "20150", "sicCode": "20150", "title": "Manufacture of fertilisers and nitrogen compounds", "subValues": [] }, { "id": "20160", "sicCode": "20160", "title": "Manufacture of plastics in primary forms", "subValues": [] }, { "id": "20170", "sicCode": "20170", "title": "Manufacture of synthetic rubber in primary forms", "subValues": [] }] }, { "id": "202", "sicCode": "202", "title": "Manufacture of pesticides and other agrochemical products", "subValues": [{ "id": "20200", "sicCode": "20200", "title": "Manufacture of pesticides and other agrochemical products", "subValues": [] }] }, { "id": "203", "sicCode": "203", "title": "Manufacture of paints, varnishes and similar coatings, printing ink and mastics", "subValues": [{ "id": "20300", "sicCode": "20300", "title": "Manufacture of paints, varnishes and similar coatings, printing ink and mastics", "subValues": [] }, { "id": "20301", "sicCode": "20301", "title": "Manufacture of paints, varnishes and similar coatings, mastics and sealants", "subValues": [] }, { "id": "20302", "sicCode": "20302", "title": "Manufacture of printing ink", "subValues": [] }] }, { "id": "204", "sicCode": "204", "title": "Manufacture of soap and detergents, cleaning and polishing preparations, perfumes and toilet preparations", "subValues": [{ "id": "20410", "sicCode": "20410", "title": "Manufacture of soap and detergents, cleaning and polishing preparations", "subValues": [] }, { "id": "20411", "sicCode": "20411", "title": "Manufacture of soap and detergents", "subValues": [] }, { "id": "20412", "sicCode": "20412", "title": "Manufacture of cleaning and polishing preparations", "subValues": [] }, { "id": "20420", "sicCode": "20420", "title": "Manufacture of perfumes and toilet preparations", "subValues": [] }] }, { "id": "205", "sicCode": "205", "title": "Manufacture of other chemical products", "subValues": [{ "id": "20510", "sicCode": "20510", "title": "Manufacture of explosives", "subValues": [] }, { "id": "20520", "sicCode": "20520", "title": "Manufacture of glues", "subValues": [] }, { "id": "20530", "sicCode": "20530", "title": "Manufacture of essential oils", "subValues": [] }, { "id": "20590", "sicCode": "20590", "title": "Manufacture of other chemical products nec", "subValues": [] }] }, { "id": "206", "sicCode": "206", "title": "Manufacture of man-made fibres", "subValues": [{ "id": "20600", "sicCode": "20600", "title": "Manufacture of man-made fibres", "subValues": [] }] }] }, { "id": "21", "sicCode": "21", "title": "Manufacture of basic pharmaceutical products and pharmaceutical preparations", "subValues": [{ "id": "211", "sicCode": "211", "title": "Manufacture of basic pharmaceutical products", "subValues": [{ "id": "21100", "sicCode": "21100", "title": "Manufacture of basic pharmaceutical products", "subValues": [] }] }, { "id": "212", "sicCode": "212", "title": "Manufacture of pharmaceutical preparations", "subValues": [{ "id": "21200", "sicCode": "21200", "title": "Manufacture of pharmaceutical preparations", "subValues": [] }] }] }, { "id": "22", "sicCode": "22", "title": "Manufacture of rubber and plastic products", "subValues": [{ "id": "221", "sicCode": "221", "title": "Manufacture of rubber products", "subValues": [{ "id": "22110", "sicCode": "22110", "title": "Manufacture of rubber tyres and tubes; retreading and rebuilding of rubber tyres", "subValues": [] }, { "id": "22190", "sicCode": "22190", "title": "Manufacture of other rubber products", "subValues": [] }] }, { "id": "222", "sicCode": "222", "title": "Manufacture of plastics products", "subValues": [{ "id": "22210", "sicCode": "22210", "title": "Manufacture of plastic plates, sheets, tubes and profiles", "subValues": [] }, { "id": "22220", "sicCode": "22220", "title": "Manufacture of plastic packing goods", "subValues": [] }, { "id": "22230", "sicCode": "22230", "title": "Manufacture of builders’ ware of plastic", "subValues": [] }, { "id": "22290", "sicCode": "22290", "title": "Manufacture of other plastic products", "subValues": [] }] }] }, { "id": "23", "sicCode": "23", "title": "Manufacture of other non-metallic mineral products", "subValues": [{ "id": "231", "sicCode": "231", "title": "Manufacture of glass and glass products", "subValues": [{ "id": "23110", "sicCode": "23110", "title": "Manufacture of flat glass", "subValues": [] }, { "id": "23120", "sicCode": "23120", "title": "Shaping and processing of flat glass", "subValues": [] }, { "id": "23130", "sicCode": "23130", "title": "Manufacture of hollow glass", "subValues": [] }, { "id": "23140", "sicCode": "23140", "title": "Manufacture of glass fibres", "subValues": [] }, { "id": "23190", "sicCode": "23190", "title": "Manufacture and processing of other glass, including technical glassware", "subValues": [] }] }, { "id": "232", "sicCode": "232", "title": "Manufacture of refractory products", "subValues": [{ "id": "23200", "sicCode": "23200", "title": "Manufacture of refractory products", "subValues": [] }] }, { "id": "233", "sicCode": "233", "title": "Manufacture of clay building materials", "subValues": [{ "id": "23310", "sicCode": "23310", "title": "Manufacture of ceramic tiles and flags", "subValues": [] }, { "id": "23320", "sicCode": "23320", "title": "Manufacture of bricks, tiles and construction products, in baked clay", "subValues": [] }] }, { "id": "234", "sicCode": "234", "title": "Manufacture of other porcelain and ceramic products", "subValues": [{ "id": "23410", "sicCode": "23410", "title": "Manufacture of ceramic household and ornamental articles", "subValues": [] }, { "id": "23420", "sicCode": "23420", "title": "Manufacture of ceramic sanitary fixtures", "subValues": [] }, { "id": "23430", "sicCode": "23430", "title": "Manufacture of ceramic insulators and insulating fittings", "subValues": [] }, { "id": "23440", "sicCode": "23440", "title": "Manufacture of other technical ceramic products", "subValues": [] }, { "id": "23490", "sicCode": "23490", "title": "Manufacture of other ceramic products", "subValues": [] }] }, { "id": "235", "sicCode": "235", "title": "Manufacture of cement, lime and plaster", "subValues": [{ "id": "23510", "sicCode": "23510", "title": "Manufacture of cement", "subValues": [] }, { "id": "23520", "sicCode": "23520", "title": "Manufacture of lime and plaster", "subValues": [] }] }, { "id": "236", "sicCode": "236", "title": "Manufacture of articles of concrete, cement and plaster", "subValues": [{ "id": "23610", "sicCode": "23610", "title": "Manufacture of concrete products for construction purposes", "subValues": [] }, { "id": "23620", "sicCode": "23620", "title": "Manufacture of plaster products for construction purposes", "subValues": [] }, { "id": "23630", "sicCode": "23630", "title": "Manufacture of ready-mixed concrete", "subValues": [] }, { "id": "23640", "sicCode": "23640", "title": "Manufacture of mortars", "subValues": [] }, { "id": "23650", "sicCode": "23650", "title": "Manufacture of fibre cement", "subValues": [] }, { "id": "23690", "sicCode": "23690", "title": "Manufacture of other articles of concrete, plaster and cement", "subValues": [] }] }, { "id": "237", "sicCode": "237", "title": "Cutting, shaping and finishing of stone", "subValues": [{ "id": "23700", "sicCode": "23700", "title": "Cutting, shaping and finishing of stone", "subValues": [] }] }, { "id": "239", "sicCode": "239", "title": "Manufacture of abrasive products and non-metallic mineral products nec", "subValues": [{ "id": "23910", "sicCode": "23910", "title": "Production of abrasive products", "subValues": [] }, { "id": "23990", "sicCode": "23990", "title": "Manufacture of other non-metallic mineral products nec", "subValues": [] }] }] }, { "id": "24", "sicCode": "24", "title": "Manufacture of basic metals", "subValues": [{ "id": "241", "sicCode": "241", "title": "Manufacture of basic iron and steel and of ferro-alloys", "subValues": [{ "id": "24100", "sicCode": "24100", "title": "Manufacture of basic iron and steel and of ferro-alloys", "subValues": [] }] }, { "id": "242", "sicCode": "242", "title": "Manufacture of tubes, pipes, hollow profiles and related fittings, of steel", "subValues": [{ "id": "24200", "sicCode": "24200", "title": "Manufacture of tubes, pipes, hollow profiles and related fittings, of steel", "subValues": [] }] }, { "id": "243", "sicCode": "243", "title": "Manufacture of other products of first processing of steel", "subValues": [{ "id": "24310", "sicCode": "24310", "title": "Cold drawing of bars", "subValues": [] }, { "id": "24320", "sicCode": "24320", "title": "Cold rolling of narrow strip", "subValues": [] }, { "id": "24330", "sicCode": "24330", "title": "Cold forming or folding", "subValues": [] }, { "id": "24340", "sicCode": "24340", "title": "Cold drawing of wire", "subValues": [] }] }, { "id": "244", "sicCode": "244", "title": "Manufacture of basic precious and other non-ferrous metals", "subValues": [{ "id": "24410", "sicCode": "24410", "title": "Precious metals production", "subValues": [] }, { "id": "24420", "sicCode": "24420", "title": "Aluminium production", "subValues": [] }, { "id": "24430", "sicCode": "24430", "title": "Lead, zinc and tin production", "subValues": [] }, { "id": "24440", "sicCode": "24440", "title": "Copper production", "subValues": [] }, { "id": "24450", "sicCode": "24450", "title": "Other non-ferrous metal production", "subValues": [] }, { "id": "24460", "sicCode": "24460", "title": "Processing of nuclear fuel", "subValues": [] }] }, { "id": "245", "sicCode": "245", "title": "Casting of metals", "subValues": [{ "id": "24510", "sicCode": "24510", "title": "Casting of iron", "subValues": [] }, { "id": "24520", "sicCode": "24520", "title": "Casting of steel", "subValues": [] }, { "id": "24530", "sicCode": "24530", "title": "Casting of light metals", "subValues": [] }, { "id": "24540", "sicCode": "24540", "title": "Casting of other non-ferrous metals", "subValues": [] }] }] }, { "id": "25", "sicCode": "25", "title": "Manufacture of fabricated metal products, except machinery and equipment", "subValues": [{ "id": "251", "sicCode": "251", "title": "Manufacture of structural metal products", "subValues": [{ "id": "25110", "sicCode": "25110", "title": "Manufacture of metal structures and parts of structures", "subValues": [] }, { "id": "25120", "sicCode": "25120", "title": "Manufacture of doors and windows of metal", "subValues": [] }] }, { "id": "252", "sicCode": "252", "title": "Manufacture of tanks, reservoirs and containers of metal", "subValues": [{ "id": "25210", "sicCode": "25210", "title": "Manufacture of central heating radiators and boilers", "subValues": [] }, { "id": "25290", "sicCode": "25290", "title": "Manufacture of other tanks, reservoirs and containers of metal", "subValues": [] }] }, { "id": "253", "sicCode": "253", "title": "Manufacture of steam generators, except central heating hot water boilers", "subValues": [{ "id": "25300", "sicCode": "25300", "title": "Manufacture of steam generators, except central heating hot water boilers", "subValues": [] }] }, { "id": "254", "sicCode": "254", "title": "Manufacture of weapons and ammunition", "subValues": [{ "id": "25400", "sicCode": "25400", "title": "Manufacture of weapons and ammunition", "subValues": [] }] }, { "id": "255", "sicCode": "255", "title": "Forging, pressing, stamping and roll-forming of metal; powder metallurgy", "subValues": [{ "id": "25500", "sicCode": "25500", "title": "Forging, pressing, stamping and roll-forming of metal; powder metallurgy", "subValues": [] }] }, { "id": "256", "sicCode": "256", "title": "Treatment and coating of metals; machining", "subValues": [{ "id": "25610", "sicCode": "25610", "title": "Treatment and coating of metals", "subValues": [] }, { "id": "25620", "sicCode": "25620", "title": "Machining", "subValues": [] }] }, { "id": "257", "sicCode": "257", "title": "Manufacture of cutlery, tools and general hardware", "subValues": [{ "id": "25710", "sicCode": "25710", "title": "Manufacture of cutlery", "subValues": [] }, { "id": "25720", "sicCode": "25720", "title": "Manufacture of locks and hinges", "subValues": [] }, { "id": "25730", "sicCode": "25730", "title": "Manufacture of tools", "subValues": [] }] }, { "id": "259", "sicCode": "259", "title": "Manufacture of other fabricated metal products", "subValues": [{ "id": "25910", "sicCode": "25910", "title": "Manufacture of steel drums and similar containers", "subValues": [] }, { "id": "25920", "sicCode": "25920", "title": "Manufacture of light metal packaging", "subValues": [] }, { "id": "25930", "sicCode": "25930", "title": "Manufacture of wire products, chain and springs", "subValues": [] }, { "id": "25940", "sicCode": "25940", "title": "Manufacture of fasteners and screw machine products", "subValues": [] }, { "id": "25990", "sicCode": "25990", "title": "Manufacture of other fabricated metal products nec", "subValues": [] }] }] }, { "id": "26", "sicCode": "26", "title": "Manufacture of computer, electronic and optical products", "subValues": [{ "id": "261", "sicCode": "261", "title": "Manufacture of electronic components and boards", "subValues": [{ "id": "26110", "sicCode": "26110", "title": "Manufacture of electronic components", "subValues": [] }, { "id": "26120", "sicCode": "26120", "title": "Manufacture of loaded electronic boards", "subValues": [] }] }, { "id": "262", "sicCode": "262", "title": "Manufacture of computers and peripheral equipment", "subValues": [{ "id": "26200", "sicCode": "26200", "title": "Manufacture of computers and peripheral equipment", "subValues": [] }] }, { "id": "263", "sicCode": "263", "title": "Manufacture of communication equipment", "subValues": [{ "id": "26300", "sicCode": "26300", "title": "Manufacture of communication equipment", "subValues": [] }, { "id": "26301", "sicCode": "26301", "title": "Manufacture of telegraph and telephone apparatus and equipment", "subValues": [] }, { "id": "26309", "sicCode": "26309", "title": "Manufacture of communication equipment (other than telegraph and telephone apparatus and equipment)", "subValues": [] }] }, { "id": "264", "sicCode": "264", "title": "Manufacture of consumer electronics", "subValues": [{ "id": "26400", "sicCode": "26400", "title": "Manufacture of consumer electronics", "subValues": [] }] }, { "id": "265", "sicCode": "265", "title": "Manufacture of instruments and appliances for measuring, testing and navigation; watches and clocks", "subValues": [{ "id": "26510", "sicCode": "26510", "title": "Manufacture of instruments and appliances for measuring, testing and navigation", "subValues": [] }, { "id": "26511", "sicCode": "26511", "title": "Manufacture of electronic instruments and appliances for measuring, testing, and navigation, except industrial process control equipment", "subValues": [] }, { "id": "26512", "sicCode": "26512", "title": "Manufacture of electronic industrial process control equipment", "subValues": [] }, { "id": "26513", "sicCode": "26513", "title": "Manufacture of non-electronic instruments and appliances for measuring, testing and navigation, except industrial process control equipment", "subValues": [] }, { "id": "26514", "sicCode": "26514", "title": "Manufacture of non-electronic industrial process control equipment", "subValues": [] }, { "id": "26520", "sicCode": "26520", "title": "Manufacture of watches and clocks", "subValues": [] }] }, { "id": "266", "sicCode": "266", "title": "Manufacture of irradiation, electromedical and electrotherapeutic equipment", "subValues": [{ "id": "26600", "sicCode": "26600", "title": "Manufacture of irradiation, electromedical and electrotherapeutic equipment", "subValues": [] }] }, { "id": "267", "sicCode": "267", "title": "Manufacture of optical instruments and photographic equipment", "subValues": [{ "id": "26700", "sicCode": "26700", "title": "Manufacture of optical instruments and photographic equipment", "subValues": [] }, { "id": "26701", "sicCode": "26701", "title": "Manufacture of optical precision instruments", "subValues": [] }, { "id": "26702", "sicCode": "26702", "title": "Manufacture of photographic and cinematographic equipment", "subValues": [] }] }, { "id": "268", "sicCode": "268", "title": "Manufacture of magnetic and optical media", "subValues": [{ "id": "26800", "sicCode": "26800", "title": "Manufacture of magnetic and optical media", "subValues": [] }] }] }, { "id": "27", "sicCode": "27", "title": "Manufacture of electrical equipment", "subValues": [{ "id": "271", "sicCode": "271", "title": "Manufacture of electric motors, generators, transformers and electricity distribution and control apparatus", "subValues": [{ "id": "27110", "sicCode": "27110", "title": "Manufacture of electric motors, generators and transformers", "subValues": [] }, { "id": "27120", "sicCode": "27120", "title": "Manufacture of electricity distribution and control apparatus", "subValues": [] }] }, { "id": "272", "sicCode": "272", "title": "Manufacture of batteries and accumulators", "subValues": [{ "id": "27200", "sicCode": "27200", "title": "Manufacture of batteries and accumulators", "subValues": [] }] }, { "id": "273", "sicCode": "273", "title": "Manufacture of wiring and wiring devices", "subValues": [{ "id": "27310", "sicCode": "27310", "title": "Manufacture of fibre optic cables", "subValues": [] }, { "id": "27320", "sicCode": "27320", "title": "Manufacture of other electronic and electric wires and cables", "subValues": [] }, { "id": "27330", "sicCode": "27330", "title": "Manufacture of wiring devices", "subValues": [] }] }, { "id": "274", "sicCode": "274", "title": "Manufacture of electric lighting equipment", "subValues": [{ "id": "27400", "sicCode": "27400", "title": "Manufacture of electric lighting equipment", "subValues": [] }] }, { "id": "275", "sicCode": "275", "title": "Manufacture of domestic appliances", "subValues": [{ "id": "27510", "sicCode": "27510", "title": "Manufacture of electric domestic appliances", "subValues": [] }, { "id": "27520", "sicCode": "27520", "title": "Manufacture of non-electric domestic appliances", "subValues": [] }] }, { "id": "279", "sicCode": "279", "title": "Manufacture of other electrical equipment", "subValues": [{ "id": "27900", "sicCode": "27900", "title": "Manufacture of other electrical equipment", "subValues": [] }] }] }, { "id": "28", "sicCode": "28", "title": "Manufacture of machinery and equipment nec", "subValues": [{ "id": "281", "sicCode": "281", "title": "Manufacture of general purpose machinery", "subValues": [{ "id": "28110", "sicCode": "28110", "title": "Manufacture of engines and turbines, except aircraft, vehicle and cycle engines", "subValues": [] }, { "id": "28120", "sicCode": "28120", "title": "Manufacture of fluid power equipment", "subValues": [] }, { "id": "28130", "sicCode": "28130", "title": "Manufacture of other pumps and compressors", "subValues": [] }, { "id": "28131", "sicCode": "28131", "title": "Manufacture of pumps", "subValues": [] }, { "id": "28132", "sicCode": "28132", "title": "Manufacture of compressors", "subValues": [] }, { "id": "28140", "sicCode": "28140", "title": "Manufacture of other taps and valves", "subValues": [] }, { "id": "28150", "sicCode": "28150", "title": "Manufacture of bearings, gears, gearing and driving elements", "subValues": [] }] }, { "id": "282", "sicCode": "282", "title": "Manufacture of other general-purpose machinery", "subValues": [{ "id": "28210", "sicCode": "28210", "title": "Manufacture of ovens, furnaces and furnace burners", "subValues": [] }, { "id": "28220", "sicCode": "28220", "title": "Manufacture of lifting and handling equipment", "subValues": [] }, { "id": "28230", "sicCode": "28230", "title": "Manufacture of office machinery and equipment (except computers and peripheral equipment)", "subValues": [] }, { "id": "28240", "sicCode": "28240", "title": "Manufacture of power-driven hand tools", "subValues": [] }, { "id": "28250", "sicCode": "28250", "title": "Manufacture of non-domestic cooling and ventilation equipment", "subValues": [] }, { "id": "28290", "sicCode": "28290", "title": "Manufacture of other general-purpose machinery nec", "subValues": [] }] }, { "id": "283", "sicCode": "283", "title": "Manufacture of agricultural and forestry machinery", "subValues": [{ "id": "28300", "sicCode": "28300", "title": "Manufacture of agricultural and forestry machinery", "subValues": [] }, { "id": "28301", "sicCode": "28301", "title": "Manufacture of agricultural tractors", "subValues": [] }, { "id": "28302", "sicCode": "28302", "title": "Manufacture of agricultural and forestry machinery (other than agricultural tractors)", "subValues": [] }] }, { "id": "284", "sicCode": "284", "title": "Manufacture of metal forming machinery and machine tools", "subValues": [{ "id": "28410", "sicCode": "28410", "title": "Manufacture of metal forming machinery", "subValues": [] }, { "id": "28490", "sicCode": "28490", "title": "Manufacture of other machine tools", "subValues": [] }] }, { "id": "289", "sicCode": "289", "title": "Manufacture of other special-purpose machinery", "subValues": [{ "id": "28910", "sicCode": "28910", "title": "Manufacture of machinery for metallurgy", "subValues": [] }, { "id": "28920", "sicCode": "28920", "title": "Manufacture of machinery for mining, quarrying and construction", "subValues": [] }, { "id": "28921", "sicCode": "28921", "title": "Manufacture of machinery for mining", "subValues": [] }, { "id": "28922", "sicCode": "28922", "title": "Manufacture of earthmoving equipment", "subValues": [] }, { "id": "28923", "sicCode": "28923", "title": "Manufacture of equipment for concrete crushing and screening roadworks", "subValues": [] }, { "id": "28930", "sicCode": "28930", "title": "Manufacture of machinery for food, beverage and tobacco processing", "subValues": [] }, { "id": "28940", "sicCode": "28940", "title": "Manufacture of machinery for textile, apparel and leather production", "subValues": [] }, { "id": "28950", "sicCode": "28950", "title": "Manufacture of machinery for paper and paperboard production", "subValues": [] }, { "id": "28960", "sicCode": "28960", "title": "Manufacture of plastics and rubber machinery", "subValues": [] }, { "id": "28990", "sicCode": "28990", "title": "Manufacture of other special-purpose machinery nec", "subValues": [] }] }] }, { "id": "29", "sicCode": "29", "title": "Manufacture of motor vehicles, trailers and semi-trailers", "subValues": [{ "id": "291", "sicCode": "291", "title": "Manufacture of motor vehicles", "subValues": [{ "id": "29100", "sicCode": "29100", "title": "Manufacture of motor vehicles", "subValues": [] }] }, { "id": "292", "sicCode": "292", "title": "Manufacture of bodies (coachwork) for motor vehicles; manufacture of trailers and semi-trailers", "subValues": [{ "id": "29200", "sicCode": "29200", "title": "Manufacture of bodies (coachwork) for motor vehicles; manufacture of trailers and semi-trailers", "subValues": [] }, { "id": "29201", "sicCode": "29201", "title": "Manufacture of bodies (coachwork) for motor vehicles (except caravans)", "subValues": [] }, { "id": "29202", "sicCode": "29202", "title": "Manufacture of trailers and semi-trailers", "subValues": [] }, { "id": "29203", "sicCode": "29203", "title": "Manufacture of caravans", "subValues": [] }] }, { "id": "293", "sicCode": "293", "title": "Manufacture of parts and accessories for motor vehicles", "subValues": [{ "id": "29310", "sicCode": "29310", "title": "Manufacture of electrical and electronic equipment for motor vehicles", "subValues": [] }, { "id": "29320", "sicCode": "29320", "title": "Manufacture of other parts and accessories for motor vehicles", "subValues": [] }] }] }, { "id": "30", "sicCode": "30", "title": "Manufacture of other transport equipment", "subValues": [{ "id": "301", "sicCode": "301", "title": "Building of ships and boats", "subValues": [{ "id": "30110", "sicCode": "30110", "title": "Building of ships and floating structures", "subValues": [] }, { "id": "30120", "sicCode": "30120", "title": "Building of pleasure and sporting boats", "subValues": [] }] }, { "id": "302", "sicCode": "302", "title": "Manufacture of railway locomotives and rolling stock", "subValues": [{ "id": "30200", "sicCode": "30200", "title": "Manufacture of railway locomotives and rolling stock", "subValues": [] }] }, { "id": "303", "sicCode": "303", "title": "Manufacture of air and spacecraft and related machinery", "subValues": [{ "id": "30300", "sicCode": "30300", "title": "Manufacture of air and spacecraft and related machinery", "subValues": [] }] }, { "id": "304", "sicCode": "304", "title": "Manufacture of military fighting vehicles", "subValues": [{ "id": "30400", "sicCode": "30400", "title": "Manufacture of military fighting vehicles", "subValues": [] }] }, { "id": "309", "sicCode": "309", "title": "Manufacture of transport equipment nec", "subValues": [{ "id": "30910", "sicCode": "30910", "title": "Manufacture of motorcycles", "subValues": [] }, { "id": "30920", "sicCode": "30920", "title": "Manufacture of bicycles and invalid carriages", "subValues": [] }, { "id": "30990", "sicCode": "30990", "title": "Manufacture of other transport equipment nec", "subValues": [] }] }] }, { "id": "31", "sicCode": "31", "title": "Manufacture of furniture", "subValues": [{ "id": "310", "sicCode": "310", "title": "Manufacture of furniture", "subValues": [{ "id": "31010", "sicCode": "31010", "title": "Manufacture of office and shop furniture", "subValues": [] }, { "id": "31020", "sicCode": "31020", "title": "Manufacture of kitchen furniture", "subValues": [] }, { "id": "31030", "sicCode": "31030", "title": "Manufacture of mattresses", "subValues": [] }, { "id": "31090", "sicCode": "31090", "title": "Manufacture of other furniture", "subValues": [] }] }] }, { "id": "32", "sicCode": "32", "title": "Other manufacturing", "subValues": [{ "id": "321", "sicCode": "321", "title": "Manufacture of jewellery, bijouterie and related articles", "subValues": [{ "id": "32110", "sicCode": "32110", "title": "Striking of coins", "subValues": [] }, { "id": "32120", "sicCode": "32120", "title": "Manufacture of jewellery and related articles", "subValues": [] }, { "id": "32130", "sicCode": "32130", "title": "Manufacture of imitation jewellery and related articles", "subValues": [] }] }, { "id": "322", "sicCode": "322", "title": "Manufacture of musical instruments", "subValues": [{ "id": "32200", "sicCode": "32200", "title": "Manufacture of musical instruments", "subValues": [] }] }, { "id": "323", "sicCode": "323", "title": "Manufacture of sports goods", "subValues": [{ "id": "32300", "sicCode": "32300", "title": "Manufacture of sports goods", "subValues": [] }] }, { "id": "324", "sicCode": "324", "title": "Manufacture of games and toys", "subValues": [{ "id": "32400", "sicCode": "32400", "title": "Manufacture of games and toys", "subValues": [] }, { "id": "32401", "sicCode": "32401", "title": "Manufacture of professional and arcade games and toys", "subValues": [] }, { "id": "32409", "sicCode": "32409", "title": "Manufacture of games and toys (other than professional and arcade games and toys) nec", "subValues": [] }] }, { "id": "325", "sicCode": "325", "title": "Manufacture of medical and dental instruments and supplies", "subValues": [{ "id": "32500", "sicCode": "32500", "title": "Manufacture of medical and dental instruments and supplies", "subValues": [] }] }, { "id": "329", "sicCode": "329", "title": "Manufacturing nec", "subValues": [{ "id": "32910", "sicCode": "32910", "title": "Manufacture of brooms and brushes", "subValues": [] }, { "id": "32990", "sicCode": "32990", "title": "Other manufacturing nec", "subValues": [] }] }] }, { "id": "33", "sicCode": "33", "title": "Repair and installation of machinery and equipment", "subValues": [{ "id": "331", "sicCode": "331", "title": "Repair of fabricated metal products, machinery and equipment", "subValues": [{ "id": "33110", "sicCode": "33110", "title": "Repair of fabricated metal products", "subValues": [] }, { "id": "33120", "sicCode": "33120", "title": "Repair of machinery", "subValues": [] }, { "id": "33130", "sicCode": "33130", "title": "Repair of electronic and optical equipment", "subValues": [] }, { "id": "33140", "sicCode": "33140", "title": "Repair of electrical equipment", "subValues": [] }, { "id": "33150", "sicCode": "33150", "title": "Repair and maintenance of ships and boats", "subValues": [] }, { "id": "33160", "sicCode": "33160", "title": "Repair and maintenance of aircraft and spacecraft", "subValues": [] }, { "id": "33170", "sicCode": "33170", "title": "Repair and maintenance of other transport equipment", "subValues": [] }, { "id": "33190", "sicCode": "33190", "title": "Repair of other equipment", "subValues": [] }] }, { "id": "332", "sicCode": "332", "title": "Installation of industrial machinery and equipment", "subValues": [{ "id": "33200", "sicCode": "33200", "title": "Installation of industrial machinery and equipment", "subValues": [] }] }] }] }, { "id": " D", "sicCode": " D", "title": "ELECTRICITY, GAS, STEAM AND AIR CONDITIONING SUPPLY", "subValues": [{ "id": "35", "sicCode": "35", "title": "Electricity, gas, steam and air conditioning supply", "subValues": [{ "id": "351", "sicCode": "351", "title": "Electric power generation, transmission and distribution", "subValues": [{ "id": "35110", "sicCode": "35110", "title": "Production of electricity", "subValues": [] }, { "id": "35120", "sicCode": "35120", "title": "Transmission of electricity", "subValues": [] }, { "id": "35130", "sicCode": "35130", "title": "Distribution of electricity", "subValues": [] }, { "id": "35140", "sicCode": "35140", "title": "Trade of electricity", "subValues": [] }] }, { "id": "352", "sicCode": "352", "title": "Manufacture of gas; distribution of gaseous fuels through mains", "subValues": [{ "id": "35210", "sicCode": "35210", "title": "Manufacture of gas", "subValues": [] }, { "id": "35220", "sicCode": "35220", "title": "Distribution of gaseous fuels through mains", "subValues": [] }, { "id": "35230", "sicCode": "35230", "title": "Trade of gas through mains", "subValues": [] }] }, { "id": "353", "sicCode": "353", "title": "Steam and air conditioning supply", "subValues": [{ "id": "35300", "sicCode": "35300", "title": "Steam and air conditioning supply", "subValues": [] }] }] }] }, { "id": " E", "sicCode": " E", "title": "WATER SUPPLY; SEWERAGE, WASTE MANAGEMENT AND REMEDIATION ACTIVITIES", "subValues": [{ "id": "36", "sicCode": "36", "title": "Water collection, treatment and supply", "subValues": [{ "id": "360", "sicCode": "360", "title": "Water collection, treatment and supply", "subValues": [{ "id": "36000", "sicCode": "36000", "title": "Water collection, treatment and supply", "subValues": [] }] }] }, { "id": "37", "sicCode": "37", "title": "Sewerage", "subValues": [{ "id": "370", "sicCode": "370", "title": "Sewerage", "subValues": [{ "id": "37000", "sicCode": "37000", "title": "Sewerage", "subValues": [] }] }] }, { "id": "38", "sicCode": "38", "title": "Waste collection, treatment and disposal activities; materials recovery", "subValues": [{ "id": "381", "sicCode": "381", "title": "Waste collection", "subValues": [{ "id": "38110", "sicCode": "38110", "title": "Collection of non-hazardous waste", "subValues": [] }, { "id": "38120", "sicCode": "38120", "title": "Collection of hazardous waste", "subValues": [] }] }, { "id": "382", "sicCode": "382", "title": "Waste treatment and disposal", "subValues": [{ "id": "38210", "sicCode": "38210", "title": "Treatment and disposal of non-hazardous waste", "subValues": [] }, { "id": "38220", "sicCode": "38220", "title": "Treatment and disposal of hazardous waste", "subValues": [] }] }, { "id": "383", "sicCode": "383", "title": "Materials recovery", "subValues": [{ "id": "38310", "sicCode": "38310", "title": "Dismantling of wrecks", "subValues": [] }, { "id": "38320", "sicCode": "38320", "title": "Recovery of sorted materials", "subValues": [] }] }] }, { "id": "39", "sicCode": "39", "title": "Remediation activities and other waste management services", "subValues": [{ "id": "390", "sicCode": "390", "title": "Remediation activities and other waste management services", "subValues": [{ "id": "39000", "sicCode": "39000", "title": "Remediation activities and other waste management services", "subValues": [] }] }] }] }, { "id": " F", "sicCode": " F", "title": "CONSTRUCTION", "subValues": [{ "id": "41", "sicCode": "41", "title": "Construction of buildings", "subValues": [{ "id": "411", "sicCode": "411", "title": "Development of building projects", "subValues": [{ "id": "41100", "sicCode": "41100", "title": "Development of building projects", "subValues": [] }] }, { "id": "412", "sicCode": "412", "title": "Construction of residential and non-residential buildings", "subValues": [{ "id": "41200", "sicCode": "41200", "title": "Construction of residential and non-residential buildings", "subValues": [] }, { "id": "41201", "sicCode": "41201", "title": "Construction of commercial buildings", "subValues": [] }, { "id": "41202", "sicCode": "41202", "title": "Construction of domestic buildings", "subValues": [] }] }] }, { "id": "42", "sicCode": "42", "title": "Civil engineering", "subValues": [{ "id": "421", "sicCode": "421", "title": "Construction of roads and railways", "subValues": [{ "id": "42110", "sicCode": "42110", "title": "Construction of roads and motorways", "subValues": [] }, { "id": "42120", "sicCode": "42120", "title": "Construction of railways and underground railways", "subValues": [] }, { "id": "42130", "sicCode": "42130", "title": "Construction of bridges and tunnels", "subValues": [] }] }, { "id": "422", "sicCode": "422", "title": "Construction of utility projects", "subValues": [{ "id": "42210", "sicCode": "42210", "title": "Construction of utility projects for fluids", "subValues": [] }, { "id": "42220", "sicCode": "42220", "title": "Construction of utility projects for electricity and telecommunications", "subValues": [] }] }, { "id": "429", "sicCode": "429", "title": "Construction of other civil engineering projects", "subValues": [{ "id": "42910", "sicCode": "42910", "title": "Construction of water projects", "subValues": [] }, { "id": "42990", "sicCode": "42990", "title": "Construction of other civil engineering projects nec", "subValues": [] }] }] }, { "id": "43", "sicCode": "43", "title": "Specialised construction activities", "subValues": [{ "id": "431", "sicCode": "431", "title": "Demolition and site preparation", "subValues": [{ "id": "43110", "sicCode": "43110", "title": "Demolition", "subValues": [] }, { "id": "43120", "sicCode": "43120", "title": "Site preparation", "subValues": [] }, { "id": "43130", "sicCode": "43130", "title": "Test drilling and boring", "subValues": [] }] }, { "id": "432", "sicCode": "432", "title": "Electrical, plumbing and other construction installation activities", "subValues": [{ "id": "43210", "sicCode": "43210", "title": "Electrical installation", "subValues": [] }, { "id": "43220", "sicCode": "43220", "title": "Plumbing, heat and air-conditioning installation", "subValues": [] }, { "id": "43290", "sicCode": "43290", "title": "Other construction installation", "subValues": [] }] }, { "id": "433", "sicCode": "433", "title": "Building completion and finishing", "subValues": [{ "id": "43310", "sicCode": "43310", "title": "Plastering", "subValues": [] }, { "id": "43320", "sicCode": "43320", "title": "Joinery installation", "subValues": [] }, { "id": "43330", "sicCode": "43330", "title": "Floor and wall covering", "subValues": [] }, { "id": "43340", "sicCode": "43340", "title": "Painting and glazing", "subValues": [] }, { "id": "43341", "sicCode": "43341", "title": "Painting", "subValues": [] }, { "id": "43342", "sicCode": "43342", "title": "Glazing", "subValues": [] }, { "id": "43390", "sicCode": "43390", "title": "Other building completion and finishing", "subValues": [] }] }, { "id": "439", "sicCode": "439", "title": "Other specialised construction activities", "subValues": [{ "id": "43910", "sicCode": "43910", "title": "Roofing activities", "subValues": [] }, { "id": "43990", "sicCode": "43990", "title": "Other specialised construction activities nec", "subValues": [] }, { "id": "43991", "sicCode": "43991", "title": "Scaffold erection", "subValues": [] }, { "id": "43999", "sicCode": "43999", "title": "Specialised construction activities (other than scaffold erection) nec", "subValues": [] }] }] }] }, { "id": " G", "sicCode": " G", "title": "WHOLESALE AND RETAIL TRADE; REPAIR OF MOTOR VEHICLES AND MOTORCYCLES", "subValues": [{ "id": "45", "sicCode": "45", "title": "Wholesale and retail trade and repair of motor vehicles and motorcycles", "subValues": [{ "id": "451", "sicCode": "451", "title": "Sale of motor vehicles", "subValues": [{ "id": "45110", "sicCode": "45110", "title": "Sale of cars and light motor vehicles", "subValues": [] }, { "id": "45111", "sicCode": "45111", "title": "Sale of new cars and light motor vehicles", "subValues": [] }, { "id": "45112", "sicCode": "45112", "title": "Sale of used cars and light motor vehicles", "subValues": [] }, { "id": "45190", "sicCode": "45190", "title": "Sale of other motor vehicles", "subValues": [] }] }, { "id": "452", "sicCode": "452", "title": "Maintenance and repair of motor vehicles", "subValues": [{ "id": "45200", "sicCode": "45200", "title": "Maintenance and repair of motor vehicles", "subValues": [] }] }, { "id": "453", "sicCode": "453", "title": "Sale of motor vehicle parts and accessories", "subValues": [{ "id": "45310", "sicCode": "45310", "title": "Wholesale trade of motor vehicle parts and accessories", "subValues": [] }, { "id": "45320", "sicCode": "45320", "title": "Retail trade of motor vehicle parts and accessories", "subValues": [] }] }, { "id": "454", "sicCode": "454", "title": "Sale, maintenance and repair of motorcycles and related parts and accessories", "subValues": [{ "id": "45400", "sicCode": "45400", "title": "Sale, maintenance and repair of motorcycles and related parts and accessories", "subValues": [] }] }] }, { "id": "46", "sicCode": "46", "title": "Wholesale trade, except of motor vehicles and motorcycles", "subValues": [{ "id": "461", "sicCode": "461", "title": "Wholesale on a fee or contract basis", "subValues": [{ "id": "46110", "sicCode": "46110", "title": "Agents involved in the sale of agricultural raw materials, live animals, textile raw materials and semi-finished goods", "subValues": [] }, { "id": "46120", "sicCode": "46120", "title": "Agents involved in the sale of fuels, ores, metals and industrial chemicals", "subValues": [] }, { "id": "46130", "sicCode": "46130", "title": "Agents involved in the sale of timber and building materials", "subValues": [] }, { "id": "46140", "sicCode": "46140", "title": "Agents involved in the sale of machinery, industrial equipment, ships and aircraft", "subValues": [] }, { "id": "46150", "sicCode": "46150", "title": "Agents involved in the sale of furniture, household goods, hardware and ironmongery", "subValues": [] }, { "id": "46160", "sicCode": "46160", "title": "Agents involved in the sale of textiles, clothing, fur, footwear and leather goods", "subValues": [] }, { "id": "46170", "sicCode": "46170", "title": "Agents involved in the sale of food, beverages and tobacco", "subValues": [] }, { "id": "46180", "sicCode": "46180", "title": "Agents specialised in the sale of other particular products", "subValues": [] }, { "id": "46190", "sicCode": "46190", "title": "Agents involved in the sale of a variety of goods", "subValues": [] }] }, { "id": "462", "sicCode": "462", "title": "Wholesale of agricultural raw materials and live animals", "subValues": [{ "id": "46210", "sicCode": "46210", "title": "Wholesale of grain, unmanufactured tobacco, seeds and animal feeds", "subValues": [] }, { "id": "46220", "sicCode": "46220", "title": "Wholesale of flowers and plants", "subValues": [] }, { "id": "46230", "sicCode": "46230", "title": "Wholesale of live animals", "subValues": [] }, { "id": "46240", "sicCode": "46240", "title": "Wholesale of hides, skins and leather", "subValues": [] }] }, { "id": "463", "sicCode": "463", "title": "Wholesale of food, beverages and tobacco", "subValues": [{ "id": "46310", "sicCode": "46310", "title": "Wholesale of fruit and vegetables", "subValues": [] }, { "id": "46320", "sicCode": "46320", "title": "Wholesale of meat and meat products", "subValues": [] }, { "id": "46330", "sicCode": "46330", "title": "Wholesale of dairy products, eggs and edible oils and fats", "subValues": [] }, { "id": "46340", "sicCode": "46340", "title": "Wholesale of beverages", "subValues": [] }, { "id": "46341", "sicCode": "46341", "title": "Wholesale of fruit and vegetable juices, mineral waters and soft drinks", "subValues": [] }, { "id": "46342", "sicCode": "46342", "title": "Wholesale of wine, beer, spirits and other alcoholic beverages", "subValues": [] }, { "id": "46350", "sicCode": "46350", "title": "Wholesale of tobacco products", "subValues": [] }, { "id": "46360", "sicCode": "46360", "title": "Wholesale of sugar and chocolate and sugar confectionery", "subValues": [] }, { "id": "46370", "sicCode": "46370", "title": "Wholesale of coffee, tea, cocoa and spices", "subValues": [] }, { "id": "46380", "sicCode": "46380", "title": "Wholesale of other food, including fish, crustaceans and molluscs", "subValues": [] }, { "id": "46390", "sicCode": "46390", "title": "Non-specialised wholesale of food, beverages and tobacco", "subValues": [] }] }, { "id": "464", "sicCode": "464", "title": "Wholesale of household goods", "subValues": [{ "id": "46410", "sicCode": "46410", "title": "Wholesale of textiles", "subValues": [] }, { "id": "46420", "sicCode": "46420", "title": "Wholesale of clothing and footwear", "subValues": [] }, { "id": "46430", "sicCode": "46430", "title": "Wholesale of electrical household appliances", "subValues": [] }, { "id": "46431", "sicCode": "46431", "title": "Wholesale of gramophone records, audio tapes, compact discs and video tapes and of the equipment on which these are played", "subValues": [] }, { "id": "46439", "sicCode": "46439", "title": "Wholesale of radio and television goods and of electrical household appliances (other than of gramophone records, audio tapes, compact discs and video tapes and the equipment on which these are played) nec", "subValues": [] }, { "id": "46440", "sicCode": "46440", "title": "Wholesale of china and glassware and cleaning materials", "subValues": [] }, { "id": "46450", "sicCode": "46450", "title": "Wholesale of perfume and cosmetics", "subValues": [] }, { "id": "46460", "sicCode": "46460", "title": "Wholesale of pharmaceutical goods", "subValues": [] }, { "id": "46470", "sicCode": "46470", "title": "Wholesale of furniture, carpets and lighting equipment", "subValues": [] }, { "id": "46480", "sicCode": "46480", "title": "Wholesale of watches and jewellery", "subValues": [] }, { "id": "46490", "sicCode": "46490", "title": "Wholesale of other household goods", "subValues": [] }, { "id": "46491", "sicCode": "46491", "title": "Wholesale of musical instruments", "subValues": [] }, { "id": "46499", "sicCode": "46499", "title": "Wholesale of household goods (other than musical instruments) nec", "subValues": [] }] }, { "id": "465", "sicCode": "465", "title": "Wholesale of information and communication equipment", "subValues": [{ "id": "46510", "sicCode": "46510", "title": "Wholesale of computers, computer peripheral equipment and software", "subValues": [] }, { "id": "46520", "sicCode": "46520", "title": "Wholesale of electronic and telecommunications equipment and parts", "subValues": [] }] }, { "id": "466", "sicCode": "466", "title": "Wholesale of other machinery, equipment and supplies", "subValues": [{ "id": "46610", "sicCode": "46610", "title": "Wholesale of agricultural machinery, equipment and supplies", "subValues": [] }, { "id": "46620", "sicCode": "46620", "title": "Wholesale of machine tools", "subValues": [] }, { "id": "46630", "sicCode": "46630", "title": "Wholesale of mining, construction and civil engineering machinery", "subValues": [] }, { "id": "46640", "sicCode": "46640", "title": "Wholesale of machinery for the textile industry and of sewing and knitting machines", "subValues": [] }, { "id": "46650", "sicCode": "46650", "title": "Wholesale of office furniture", "subValues": [] }, { "id": "46660", "sicCode": "46660", "title": "Wholesale of other office machinery and equipment", "subValues": [] }, { "id": "46690", "sicCode": "46690", "title": "Wholesale of other machinery and equipment", "subValues": [] }] }, { "id": "467", "sicCode": "467", "title": "Other specialised wholesale", "subValues": [{ "id": "46710", "sicCode": "46710", "title": "Wholesale of solid, liquid and gaseous fuels and related products", "subValues": [] }, { "id": "46711", "sicCode": "46711", "title": "Wholesale of petroleum and petroleum products", "subValues": [] }, { "id": "46719", "sicCode": "46719", "title": "Wholesale of fuels and related products (other than petroleum and petroleum products)", "subValues": [] }, { "id": "46720", "sicCode": "46720", "title": "Wholesale of metals and metal ores", "subValues": [] }, { "id": "46730", "sicCode": "46730", "title": "Wholesale of wood, construction materials and sanitary equipment", "subValues": [] }, { "id": "46740", "sicCode": "46740", "title": "Wholesale of hardware, plumbing and heating equipment and supplies", "subValues": [] }, { "id": "46750", "sicCode": "46750", "title": "Wholesale of chemical products", "subValues": [] }, { "id": "46760", "sicCode": "46760", "title": "Wholesale of other intermediate products", "subValues": [] }, { "id": "46770", "sicCode": "46770", "title": "Wholesale of waste and scrap", "subValues": [] }] }, { "id": "469", "sicCode": "469", "title": "Non-specialised wholesale trade", "subValues": [{ "id": "46900", "sicCode": "46900", "title": "Non-specialised wholesale trade", "subValues": [] }] }] }, { "id": "47", "sicCode": "47", "title": "Retail trade, except of motor vehicles and motorcycles", "subValues": [{ "id": "471", "sicCode": "471", "title": "Retail sale in non-specialised stores", "subValues": [{ "id": "47110", "sicCode": "47110", "title": "Retail sale in non-specialised stores with food, beverages or tobacco predominating", "subValues": [] }, { "id": "47190", "sicCode": "47190", "title": "Other retail sale in non-specialised stores", "subValues": [] }] }, { "id": "472", "sicCode": "472", "title": "Retail sale of food, beverages and tobacco in specialised stores", "subValues": [{ "id": "47210", "sicCode": "47210", "title": "Retail sale of fruit and vegetables in specialised stores", "subValues": [] }, { "id": "47220", "sicCode": "47220", "title": "Retail sale of meat and meat products in specialised stores", "subValues": [] }, { "id": "47230", "sicCode": "47230", "title": "Retail sale of fish, crustaceans and molluscs in specialised stores", "subValues": [] }, { "id": "47240", "sicCode": "47240", "title": "Retail sale of bread, cakes, flour confectionery and sugar confectionery in specialised stores", "subValues": [] }, { "id": "47250", "sicCode": "47250", "title": "Retail sale of beverages in specialised stores", "subValues": [] }, { "id": "47260", "sicCode": "47260", "title": "Retail sale of tobacco products in specialised stores", "subValues": [] }, { "id": "47290", "sicCode": "47290", "title": "Other retail sale of food in specialised stores", "subValues": [] }] }, { "id": "473", "sicCode": "473", "title": "Retail sale of automotive fuel in specialised stores", "subValues": [{ "id": "47300", "sicCode": "47300", "title": "Retail sale of automotive fuel in specialised stores", "subValues": [] }] }, { "id": "474", "sicCode": "474", "title": "Retail sale of information and communication equipment in specialised stores", "subValues": [{ "id": "47410", "sicCode": "47410", "title": "Retail sale of computers, peripheral units and software in specialised stores", "subValues": [] }, { "id": "47420", "sicCode": "47420", "title": "Retail sale of telecommunications equipment in specialised stores", "subValues": [] }, { "id": "47421", "sicCode": "47421", "title": "Retail sale of mobile telephones in specialised stores", "subValues": [] }, { "id": "47429", "sicCode": "47429", "title": "Retail sale of telecommunications equipment (other than mobile telephones) nec, in specialised stores", "subValues": [] }, { "id": "47430", "sicCode": "47430", "title": "Retail sale of audio and video equipment in specialised stores", "subValues": [] }] }, { "id": "475", "sicCode": "475", "title": "Retail sale of other household equipment in specialised stores", "subValues": [{ "id": "47510", "sicCode": "47510", "title": "Retail sale of textiles in specialised stores", "subValues": [] }, { "id": "47520", "sicCode": "47520", "title": "Retail sale of hardware, paints and glass in specialised stores", "subValues": [] }, { "id": "47530", "sicCode": "47530", "title": "Retail sale of carpets, rugs, wall and floor coverings in specialised stores", "subValues": [] }, { "id": "47540", "sicCode": "47540", "title": "Retail sale of electrical household appliances in specialised stores", "subValues": [] }, { "id": "47590", "sicCode": "47590", "title": "Retail sale of furniture, lighting equipment and other household articles in specialised stores", "subValues": [] }, { "id": "47591", "sicCode": "47591", "title": "Retail sale of musical instruments and scores in specialised stores", "subValues": [] }, { "id": "47599", "sicCode": "47599", "title": "Retail sale of furniture, lighting equipment and other household articles (other than musical instruments) nec, in specialised stores", "subValues": [] }] }, { "id": "476", "sicCode": "476", "title": "Retail sale of cultural and recreation goods in specialised stores", "subValues": [{ "id": "47610", "sicCode": "47610", "title": "Retail sale of books in specialised stores", "subValues": [] }, { "id": "47620", "sicCode": "47620", "title": "Retail sale of newspapers and stationery in specialised stores", "subValues": [] }, { "id": "47630", "sicCode": "47630", "title": "Retail sale of music and video recordings in specialised stores", "subValues": [] }, { "id": "47640", "sicCode": "47640", "title": "Retail sale of sporting equipment in specialised stores", "subValues": [] }, { "id": "47650", "sicCode": "47650", "title": "Retail sale of games and toys in specialised stores", "subValues": [] }] }, { "id": "477", "sicCode": "477", "title": "Retail sale of other goods in specialised stores", "subValues": [{ "id": "47710", "sicCode": "47710", "title": "Retail sale of clothing in specialised stores", "subValues": [] }, { "id": "47720", "sicCode": "47720", "title": "Retail sale of footwear and leather goods in specialised stores", "subValues": [] }, { "id": "47721", "sicCode": "47721", "title": "Retail sale of footwear in specialised stores", "subValues": [] }, { "id": "47722", "sicCode": "47722", "title": "Retail sale of leather goods in specialised stores", "subValues": [] }, { "id": "47730", "sicCode": "47730", "title": "Dispensing chemist in specialised stores", "subValues": [] }, { "id": "47740", "sicCode": "47740", "title": "Retail sale of medical and orthopaedic goods in specialised stores", "subValues": [] }, { "id": "47741", "sicCode": "47741", "title": "Retail sale of hearing aids in specialised stores", "subValues": [] }, { "id": "47749", "sicCode": "47749", "title": "Retail sale of medical and orthopaedic goods (other than hearing aids) nec, in specialised stores", "subValues": [] }, { "id": "47750", "sicCode": "47750", "title": "Retail sale of cosmetic and toilet articles in specialised stores", "subValues": [] }, { "id": "47760", "sicCode": "47760", "title": "Retail sale of flowers, plants, seeds, fertilisers, pet animals and pet food in specialised stores", "subValues": [] }, { "id": "47770", "sicCode": "47770", "title": "Retail sale of watches and jewellery in specialised stores", "subValues": [] }, { "id": "47780", "sicCode": "47780", "title": "Other retail sale of new goods in specialised stores", "subValues": [] }, { "id": "47781", "sicCode": "47781", "title": "Retail sale in commercial art galleries", "subValues": [] }, { "id": "47782", "sicCode": "47782", "title": "Retail sale by opticians", "subValues": [] }, { "id": "47789", "sicCode": "47789", "title": "Other retail sale of new goods in specialised stores (other than by opticians or commercial art galleries), nec", "subValues": [] }, { "id": "47790", "sicCode": "47790", "title": "Retail sale of second-hand goods in stores", "subValues": [] }, { "id": "47791", "sicCode": "47791", "title": "Retail sale of antiques including antique books, in stores", "subValues": [] }, { "id": "47799", "sicCode": "47799", "title": "Retail sale of second-hand goods (other than antiques and antique books) in stores", "subValues": [] }] }, { "id": "478", "sicCode": "478", "title": "Retail sale via stalls and markets", "subValues": [{ "id": "47810", "sicCode": "47810", "title": "Retail sale via stalls and markets of food, beverages and tobacco products", "subValues": [] }, { "id": "47820", "sicCode": "47820", "title": "Retail sale via stalls and markets of textiles, clothing and footwear", "subValues": [] }, { "id": "47890", "sicCode": "47890", "title": "Retail sale via stalls and markets of other goods", "subValues": [] }] }, { "id": "479", "sicCode": "479", "title": "Retail trade not in stores, stalls or markets", "subValues": [{ "id": "47910", "sicCode": "47910", "title": "Retail sale via mail order houses or via Internet", "subValues": [] }, { "id": "47990", "sicCode": "47990", "title": "Other retail sale not in stores, stalls or markets", "subValues": [] }] }] }] }, { "id": " H", "sicCode": " H", "title": "TRANSPORTATION AND STORAGE", "subValues": [{ "id": "49", "sicCode": "49", "title": "Land transport and transport via pipelines", "subValues": [{ "id": "491", "sicCode": "491", "title": "Passenger rail transport, interurban", "subValues": [{ "id": "49100", "sicCode": "49100", "title": "Passenger rail transport, interurban", "subValues": [] }] }, { "id": "492", "sicCode": "492", "title": "Freight rail transport", "subValues": [{ "id": "49200", "sicCode": "49200", "title": "Freight rail transport", "subValues": [] }] }, { "id": "493", "sicCode": "493", "title": "Other passenger land transport", "subValues": [{ "id": "49310", "sicCode": "49310", "title": "Urban and suburban passenger land transport", "subValues": [] }, { "id": "49311", "sicCode": "49311", "title": "Urban, suburban or metropolitan area passenger railway transportation by underground, metro and similar systems", "subValues": [] }, { "id": "49319", "sicCode": "49319", "title": "Urban, suburban or metropolitan area passenger land transport other than railway transportation by underground, metro and similar systems", "subValues": [] }, { "id": "49320", "sicCode": "49320", "title": "Taxi operation", "subValues": [] }, { "id": "49390", "sicCode": "49390", "title": "Other passenger land transport nec", "subValues": [] }] }, { "id": "494", "sicCode": "494", "title": "Freight transport by road and removal services", "subValues": [{ "id": "49410", "sicCode": "49410", "title": "Freight transport by road", "subValues": [] }, { "id": "49420", "sicCode": "49420", "title": "Removal services", "subValues": [] }] }, { "id": "495", "sicCode": "495", "title": "Transport via pipeline", "subValues": [{ "id": "49500", "sicCode": "49500", "title": "Transport via pipeline", "subValues": [] }] }] }, { "id": "50", "sicCode": "50", "title": "Water transport", "subValues": [{ "id": "501", "sicCode": "501", "title": "Sea and coastal passenger water transport", "subValues": [{ "id": "50100", "sicCode": "50100", "title": "Sea and coastal passenger water transport", "subValues": [] }] }, { "id": "502", "sicCode": "502", "title": "Sea and coastal freight water transport", "subValues": [{ "id": "50200", "sicCode": "50200", "title": "Sea and coastal freight water transport", "subValues": [] }] }, { "id": "503", "sicCode": "503", "title": "Inland passenger water transport", "subValues": [{ "id": "50300", "sicCode": "50300", "title": "Inland passenger water transport", "subValues": [] }] }, { "id": "504", "sicCode": "504", "title": "Inland freight water transport", "subValues": [{ "id": "50400", "sicCode": "50400", "title": "Inland freight water transport", "subValues": [] }] }] }, { "id": "51", "sicCode": "51", "title": "Air transport", "subValues": [{ "id": "511", "sicCode": "511", "title": "Passenger air transport", "subValues": [{ "id": "51100", "sicCode": "51100", "title": "Passenger air transport", "subValues": [] }, { "id": "51101", "sicCode": "51101", "title": "Scheduled passenger air transport", "subValues": [] }, { "id": "51102", "sicCode": "51102", "title": "Non-scheduled passenger air transport", "subValues": [] }] }, { "id": "512", "sicCode": "512", "title": "Freight air transport and space transport", "subValues": [{ "id": "51210", "sicCode": "51210", "title": "Freight air transport", "subValues": [] }, { "id": "51220", "sicCode": "51220", "title": "Space transport", "subValues": [] }] }] }, { "id": "52", "sicCode": "52", "title": "Warehousing and support activities for transportation", "subValues": [{ "id": "521", "sicCode": "521", "title": "Warehousing and storage", "subValues": [{ "id": "52100", "sicCode": "52100", "title": "Warehousing and storage", "subValues": [] }, { "id": "52101", "sicCode": "52101", "title": "Operation of warehousing and storage facilities for water transport activities of division 50", "subValues": [] }, { "id": "52102", "sicCode": "52102", "title": "Operation of warehousing and storage facilities for air transport activities of division 51", "subValues": [] }, { "id": "52103", "sicCode": "52103", "title": "Operation of warehousing and storage facilities for land transport activities of division 49", "subValues": [] }] }, { "id": "522", "sicCode": "522", "title": "Support activities for transportation", "subValues": [{ "id": "52210", "sicCode": "52210", "title": "Service activities incidental to land transportation", "subValues": [] }, { "id": "52211", "sicCode": "52211", "title": "Operation of rail freight terminals", "subValues": [] }, { "id": "52212", "sicCode": "52212", "title": "Operation of rail passenger facilities at railway stations", "subValues": [] }, { "id": "52213", "sicCode": "52213", "title": "Operation of bus and coach passenger facilities at bus and coach stations", "subValues": [] }, { "id": "52219", "sicCode": "52219", "title": "Other service activities incidental to land transportation, nec (not including operation of rail freight terminals, passenger facilities at railway stations or passenger facilities at bus and coach stations)", "subValues": [] }, { "id": "52220", "sicCode": "52220", "title": "Service activities incidental to water transportation", "subValues": [] }, { "id": "52230", "sicCode": "52230", "title": "Service activities incidental to air transportation", "subValues": [] }, { "id": "52240", "sicCode": "52240", "title": "Cargo handling", "subValues": [] }, { "id": "52241", "sicCode": "52241", "title": "Cargo handling for water transport activities of division 50", "subValues": [] }, { "id": "52242", "sicCode": "52242", "title": "Cargo handling for air transport activities of division 51", "subValues": [] }, { "id": "52243", "sicCode": "52243", "title": "Cargo handling for land transport activities of division 49", "subValues": [] }, { "id": "52290", "sicCode": "52290", "title": "Other transportation support activities", "subValues": [] }] }] }, { "id": "53", "sicCode": "53", "title": "Postal and courier activities", "subValues": [{ "id": "531", "sicCode": "531", "title": "Postal activities under universal service obligation", "subValues": [{ "id": "53100", "sicCode": "53100", "title": "Postal activities under universal service obligation", "subValues": [] }] }, { "id": "532", "sicCode": "532", "title": "Other postal and courier activities", "subValues": [{ "id": "53200", "sicCode": "53200", "title": "Other postal and courier activities", "subValues": [] }, { "id": "53201", "sicCode": "53201", "title": "Licensed Carriers", "subValues": [] }, { "id": "53202", "sicCode": "53202", "title": "Unlicensed Carriers", "subValues": [] }] }] }] }, { "id": " I", "sicCode": " I", "title": "ACCOMMODATION AND FOOD SERVICE ACTIVITIES", "subValues": [{ "id": "55", "sicCode": "55", "title": "Accommodation", "subValues": [{ "id": "551", "sicCode": "551", "title": "Hotels and similar accommodation", "subValues": [{ "id": "55100", "sicCode": "55100", "title": "Hotels and similar accommodation", "subValues": [] }] }, { "id": "552", "sicCode": "552", "title": "Holiday and other short-stay accommodation", "subValues": [{ "id": "55200", "sicCode": "55200", "title": "Holiday and other short-stay accommodation", "subValues": [] }, { "id": "55201", "sicCode": "55201", "title": "Holiday centres and villages", "subValues": [] }, { "id": "55202", "sicCode": "55202", "title": "Youth hostels", "subValues": [] }, { "id": "55209", "sicCode": "55209", "title": "Other holiday and other short-stay accommodation (not including holiday centres and villages or youth hostels) nec", "subValues": [] }] }, { "id": "553", "sicCode": "553", "title": "Camping grounds, recreational vehicle parks and trailer parks", "subValues": [{ "id": "55300", "sicCode": "55300", "title": "Camping grounds, recreational vehicle parks and trailer parks", "subValues": [] }] }, { "id": "559", "sicCode": "559", "title": "Other accommodation", "subValues": [{ "id": "55900", "sicCode": "55900", "title": "Other accommodation", "subValues": [] }] }] }, { "id": "56", "sicCode": "56", "title": "Food and beverage service activities", "subValues": [{ "id": "561", "sicCode": "561", "title": "Restaurants and mobile food service activities", "subValues": [{ "id": "56100", "sicCode": "56100", "title": "Restaurants and mobile food service activities", "subValues": [] }, { "id": "56101", "sicCode": "56101", "title": "Licensed restaurants", "subValues": [] }, { "id": "56102", "sicCode": "56102", "title": "Unlicensed restaurants and cafes", "subValues": [] }, { "id": "56103", "sicCode": "56103", "title": "Take away food shops and mobile food stands", "subValues": [] }] }, { "id": "562", "sicCode": "562", "title": "Event catering and other food service activities", "subValues": [{ "id": "56210", "sicCode": "56210", "title": "Event catering activities", "subValues": [] }, { "id": "56290", "sicCode": "56290", "title": "Other food service activities", "subValues": [] }] }, { "id": "563", "sicCode": "563", "title": "Beverage serving activities", "subValues": [{ "id": "56300", "sicCode": "56300", "title": "Beverage serving activities", "subValues": [] }, { "id": "56301", "sicCode": "56301", "title": "Licensed clubs", "subValues": [] }, { "id": "56302", "sicCode": "56302", "title": "Public houses and bars", "subValues": [] }] }] }] }, { "id": "J", "sicCode": "J", "title": "INFORMATION AND COMMUNICATION", "subValues": [{ "id": "58", "sicCode": "58", "title": "Publishing activities", "subValues": [{ "id": "581", "sicCode": "581", "title": "Publishing of books, periodicals and other publishing activities", "subValues": [{ "id": "58110", "sicCode": "58110", "title": "Book publishing", "subValues": [] }, { "id": "58120", "sicCode": "58120", "title": "Publishing of directories and mailing lists", "subValues": [] }, { "id": "58130", "sicCode": "58130", "title": "Publishing of newspapers", "subValues": [] }, { "id": "58140", "sicCode": "58140", "title": "Publishing of journals and periodicals", "subValues": [] }, { "id": "58141", "sicCode": "58141", "title": "Publishing of learned journals", "subValues": [] }, { "id": "58142", "sicCode": "58142", "title": "Publishing of consumer, business and professional journals and periodicals", "subValues": [] }, { "id": "58190", "sicCode": "58190", "title": "Other publishing activities", "subValues": [] }] }, { "id": "582", "sicCode": "582", "title": "Software publishing", "subValues": [{ "id": "58210", "sicCode": "58210", "title": "Publishing of computer games", "subValues": [] }, { "id": "58290", "sicCode": "58290", "title": "Other software publishing", "subValues": [] }] }] }, { "id": "59", "sicCode": "59", "title": "Motion picture, video and television programme production, sound recording and music publishing activities", "subValues": [{ "id": "591", "sicCode": "591", "title": "Motion picture, video and television programme activities", "subValues": [{ "id": "59110", "sicCode": "59110", "title": "Motion picture, video and television programme production activities", "subValues": [] }, { "id": "59111", "sicCode": "59111", "title": "Motion picture production activities", "subValues": [] }, { "id": "59112", "sicCode": "59112", "title": "Video production activities", "subValues": [] }, { "id": "59113", "sicCode": "59113", "title": "Television programme production activities", "subValues": [] }, { "id": "59120", "sicCode": "59120", "title": "Motion picture, video and television programme post-production activities", "subValues": [] }, { "id": "59130", "sicCode": "59130", "title": "Motion picture, video and television programme distribution activities", "subValues": [] }, { "id": "59131", "sicCode": "59131", "title": "Motion picture distribution activities", "subValues": [] }, { "id": "59132", "sicCode": "59132", "title": "Video distribution activities", "subValues": [] }, { "id": "59133", "sicCode": "59133", "title": "Television programme distribution activities", "subValues": [] }, { "id": "59140", "sicCode": "59140", "title": "Motion picture projection activities", "subValues": [] }] }, { "id": "592", "sicCode": "592", "title": "Sound recording and music publishing activities", "subValues": [{ "id": "59200", "sicCode": "59200", "title": "Sound recording and music publishing activities", "subValues": [] }] }] }, { "id": "60", "sicCode": "60", "title": "Programming and broadcasting activities", "subValues": [{ "id": "601", "sicCode": "601", "title": "Radio broadcasting", "subValues": [{ "id": "60100", "sicCode": "60100", "title": "Radio broadcasting", "subValues": [] }] }, { "id": "602", "sicCode": "602", "title": "Television programming and broadcasting activities", "subValues": [{ "id": "60200", "sicCode": "60200", "title": "Television programming and broadcasting activities", "subValues": [] }] }] }, { "id": "61", "sicCode": "61", "title": "Telecommunications", "subValues": [{ "id": "611", "sicCode": "611", "title": "Wired telecommunications activities", "subValues": [{ "id": "61100", "sicCode": "61100", "title": "Wired telecommunications activities", "subValues": [] }] }, { "id": "612", "sicCode": "612", "title": "Wireless telecommunications activities", "subValues": [{ "id": "61200", "sicCode": "61200", "title": "Wireless telecommunications activities", "subValues": [] }] }, { "id": "613", "sicCode": "613", "title": "Satellite telecommunications activities", "subValues": [{ "id": "61300", "sicCode": "61300", "title": "Satellite telecommunications activities", "subValues": [] }] }, { "id": "619", "sicCode": "619", "title": "Other telecommunications activities", "subValues": [{ "id": "61900", "sicCode": "61900", "title": "Other telecommunications activities", "subValues": [] }] }] }, { "id": "62", "sicCode": "62", "title": "Computer programming, consultancy and related activities", "subValues": [{ "id": "620", "sicCode": "620", "title": "Computer programming, consultancy and related activities", "subValues": [{ "id": "62010", "sicCode": "62010", "title": "Computer programming activities", "subValues": [] }, { "id": "62011", "sicCode": "62011", "title": "Ready-made interactive leisure and entertainment software development", "subValues": [] }, { "id": "62012", "sicCode": "62012", "title": "Business and domestic software development", "subValues": [] }, { "id": "62020", "sicCode": "62020", "title": "Computer consultancy activities", "subValues": [] }, { "id": "62030", "sicCode": "62030", "title": "Computer facilities management activities", "subValues": [] }, { "id": "62090", "sicCode": "62090", "title": "Other information technology and computer service activities", "subValues": [] }] }] }, { "id": "63", "sicCode": "63", "title": "Information service activities", "subValues": [{ "id": "631", "sicCode": "631", "title": "Data processing, hosting and related activities; web portals", "subValues": [{ "id": "63110", "sicCode": "63110", "title": "Data processing, hosting and related activities", "subValues": [] }, { "id": "63120", "sicCode": "63120", "title": "Web portals", "subValues": [] }] }, { "id": "639", "sicCode": "639", "title": "Other information service activities", "subValues": [{ "id": "63910", "sicCode": "63910", "title": "News agency activities", "subValues": [] }, { "id": "63990", "sicCode": "63990", "title": "Other information service activities nec", "subValues": [] }] }] }] }, { "id": " K", "sicCode": " K", "title": "FINANCIAL AND INSURANCE ACTIVITIES", "subValues": [{ "id": "64", "sicCode": "64", "title": "Financial service activities, except insurance and pension funding", "subValues": [{ "id": "641", "sicCode": "641", "title": "Monetary intermediation", "subValues": [{ "id": "64110", "sicCode": "64110", "title": "Central banking", "subValues": [] }, { "id": "64190", "sicCode": "64190", "title": "Other monetary intermediation", "subValues": [] }, { "id": "64191", "sicCode": "64191", "title": "Banks", "subValues": [] }, { "id": "64192", "sicCode": "64192", "title": "Building societies", "subValues": [] }] }, { "id": "642", "sicCode": "642", "title": "Activities of holding companies", "subValues": [{ "id": "64200", "sicCode": "64200", "title": "Activities of holding companies", "subValues": [] }, { "id": "64201", "sicCode": "64201", "title": "Activities of agricultural holding companies", "subValues": [] }, { "id": "64202", "sicCode": "64202", "title": "Activities of production holding companies", "subValues": [] }, { "id": "64203", "sicCode": "64203", "title": "Activities of construction holding companies", "subValues": [] }, { "id": "64204", "sicCode": "64204", "title": "Activities of distribution holding companies", "subValues": [] }, { "id": "64205", "sicCode": "64205", "title": "Activities of financial services holding companies", "subValues": [] }, { "id": "64209", "sicCode": "64209", "title": "Activities of other holding companies (not including agricultural, production, construction, distribution and financial services holding companies) nec", "subValues": [] }] }, { "id": "643", "sicCode": "643", "title": "Trusts, funds and similar financial entities", "subValues": [{ "id": "64300", "sicCode": "64300", "title": "Trusts, funds and similar financial entities", "subValues": [] }, { "id": "64301", "sicCode": "64301", "title": "Activities of investment trusts", "subValues": [] }, { "id": "64302", "sicCode": "64302", "title": "Activities of unit trusts", "subValues": [] }, { "id": "64303", "sicCode": "64303", "title": "Activities of venture and development capital companies", "subValues": [] }, { "id": "64304", "sicCode": "64304", "title": "Activities of open-ended investment companies", "subValues": [] }, { "id": "64305", "sicCode": "64305", "title": "Activities of property unit trusts", "subValues": [] }, { "id": "64306", "sicCode": "64306", "title": "Activities of real estate investment trusts", "subValues": [] }] }, { "id": "649", "sicCode": "649", "title": "Other financial service activities, except insurance and pension funding", "subValues": [{ "id": "64910", "sicCode": "64910", "title": "Financial leasing", "subValues": [] }, { "id": "64920", "sicCode": "64920", "title": "Other credit granting", "subValues": [] }, { "id": "64921", "sicCode": "64921", "title": "Credit granting by non-deposit taking finance houses and other specialist consumer credit grantors", "subValues": [] }, { "id": "64922", "sicCode": "64922", "title": "Activities of mortgage finance companies", "subValues": [] }, { "id": "64929", "sicCode": "64929", "title": "Other credit granting (not including credit granting by non-deposit taking finance houses and other specialist consumer credit grantors and activities of mortgage finance companies) nec", "subValues": [] }, { "id": "64990", "sicCode": "64990", "title": "Other financial service activities, except insurance and pension funding, nec", "subValues": [] }, { "id": "64991", "sicCode": "64991", "title": "Security dealing on own account", "subValues": [] }, { "id": "64992", "sicCode": "64992", "title": "Factoring", "subValues": [] }, { "id": "64999", "sicCode": "64999", "title": "Other financial service activities, except insurance and pension funding, (not including security dealing on own account and factoring) nec", "subValues": [] }] }] }, { "id": "65", "sicCode": "65", "title": "Insurance, reinsurance and pension funding, except compulsory social security", "subValues": [{ "id": "651", "sicCode": "651", "title": "Insurance", "subValues": [{ "id": "65110", "sicCode": "65110", "title": "Life insurance", "subValues": [] }, { "id": "65120", "sicCode": "65120", "title": "Non-life insurance", "subValues": [] }] }, { "id": "652", "sicCode": "652", "title": "Reinsurance", "subValues": [{ "id": "65200", "sicCode": "65200", "title": "Reinsurance", "subValues": [] }, { "id": "65201", "sicCode": "65201", "title": "Life reinsurance", "subValues": [] }, { "id": "65202", "sicCode": "65202", "title": "Non-life reinsurance", "subValues": [] }] }, { "id": "653", "sicCode": "653", "title": "Pension funding", "subValues": [{ "id": "65300", "sicCode": "65300", "title": "Pension funding", "subValues": [] }] }] }, { "id": "66", "sicCode": "66", "title": "Activities auxiliary to financial services and insurance activities", "subValues": [{ "id": "661", "sicCode": "661", "title": "Activities auxiliary to financial services, except insurance and pension funding", "subValues": [{ "id": "66110", "sicCode": "66110", "title": "Administration of financial markets", "subValues": [] }, { "id": "66120", "sicCode": "66120", "title": "Security and commodity contracts brokerage", "subValues": [] }, { "id": "66190", "sicCode": "66190", "title": "Other activities auxiliary to financial services, except insurance and pension funding", "subValues": [] }] }, { "id": "662", "sicCode": "662", "title": "Activities auxiliary to insurance and pension funding", "subValues": [{ "id": "66210", "sicCode": "66210", "title": "Risk and damage evaluation", "subValues": [] }, { "id": "66220", "sicCode": "66220", "title": "Activities of insurance agents and brokers", "subValues": [] }, { "id": "66290", "sicCode": "66290", "title": "Other activities auxiliary to insurance and pension funding", "subValues": [] }] }, { "id": "663", "sicCode": "663", "title": "Fund management activities", "subValues": [{ "id": "66300", "sicCode": "66300", "title": "Fund management activities", "subValues": [] }] }] }] }, { "id": " L", "sicCode": " L", "title": "REAL ESTATE ACTIVITIES", "subValues": [{ "id": "68", "sicCode": "68", "title": "Real estate activities", "subValues": [{ "id": "681", "sicCode": "681", "title": "Buying and selling of own real estate", "subValues": [{ "id": "68100", "sicCode": "68100", "title": "Buying and selling of own real estate", "subValues": [] }] }, { "id": "682", "sicCode": "682", "title": "Renting and operating of own or leased real estate", "subValues": [{ "id": "68200", "sicCode": "68200", "title": "Renting and operating of own or leased real estate", "subValues": [] }, { "id": "68201", "sicCode": "68201", "title": "Renting and operating of Housing Association real estate", "subValues": [] }, { "id": "68202", "sicCode": "68202", "title": "Letting and operating of conference and exhibition centres", "subValues": [] }, { "id": "68209", "sicCode": "68209", "title": "Letting and operating of own or leased real estate (other than Housing Association real estate and conference and exhibition services) nec", "subValues": [] }] }, { "id": "683", "sicCode": "683", "title": "Real estate activities on a fee or contract basis", "subValues": [{ "id": "68310", "sicCode": "68310", "title": "Real estate agencies", "subValues": [] }, { "id": "68320", "sicCode": "68320", "title": "Management of real estate on a fee or contract basis", "subValues": [] }] }] }] }, { "id": " M", "sicCode": " M", "title": "PROFESSIONAL, SCIENTIFIC AND TECHNICAL ACTIVITIES", "subValues": [{ "id": "69", "sicCode": "69", "title": "Legal and accounting activities", "subValues": [{ "id": "691", "sicCode": "691", "title": "Legal activities", "subValues": [{ "id": "69100", "sicCode": "69100", "title": "Legal activities", "subValues": [] }, { "id": "69101", "sicCode": "69101", "title": "Barristers at law", "subValues": [] }, { "id": "69102", "sicCode": "69102", "title": "Solicitors", "subValues": [] }, { "id": "69109", "sicCode": "69109", "title": "Activities of patent and copyright agents; other legal activities (other than those of barristers and solicitors) nec", "subValues": [] }] }, { "id": "692", "sicCode": "692", "title": "Accounting, bookkeeping and auditing activities; tax consultancy", "subValues": [{ "id": "69200", "sicCode": "69200", "title": "Accounting, bookkeeping and auditing activities; tax consultancy", "subValues": [] }, { "id": "69201", "sicCode": "69201", "title": "Accounting, and auditing activities", "subValues": [] }, { "id": "69202", "sicCode": "69202", "title": "Bookkeeping activities", "subValues": [] }, { "id": "69203", "sicCode": "69203", "title": "Tax consultancy", "subValues": [] }] }] }, { "id": "70", "sicCode": "70", "title": "Activities of head offices; management consultancy activities", "subValues": [{ "id": "701", "sicCode": "701", "title": "Activities of head offices", "subValues": [{ "id": "70100", "sicCode": "70100", "title": "Activities of head offices", "subValues": [] }] }, { "id": "702", "sicCode": "702", "title": "Management consultancy activities", "subValues": [{ "id": "70210", "sicCode": "70210", "title": "Public relations and communication activities", "subValues": [] }, { "id": "70220", "sicCode": "70220", "title": "Business and other management consultancy activities", "subValues": [] }, { "id": "70221", "sicCode": "70221", "title": "Financial management", "subValues": [] }, { "id": "70229", "sicCode": "70229", "title": "Management consultancy activities (other than financial management)", "subValues": [] }] }] }, { "id": "71", "sicCode": "71", "title": "Architectural and engineering activities; technical testing and analysis", "subValues": [{ "id": "711", "sicCode": "711", "title": "Architectural and engineering activities and related technical consultancy", "subValues": [{ "id": "71110", "sicCode": "71110", "title": "Architectural activities", "subValues": [] }, { "id": "71111", "sicCode": "71111", "title": "Architectural activities", "subValues": [] }, { "id": "71112", "sicCode": "71112", "title": "Urban planning and landscape architectural activities", "subValues": [] }, { "id": "71120", "sicCode": "71120", "title": "Engineering activities and related technical consultancy", "subValues": [] }, { "id": "71121", "sicCode": "71121", "title": "Engineering design activities for industrial process and production", "subValues": [] }, { "id": "71122", "sicCode": "71122", "title": "Engineering related scientific and technical consulting activities", "subValues": [] }, { "id": "71129", "sicCode": "71129", "title": "Other engineering activities (not including engineering design for industrial process and production or engineering related scientific and technical consulting activities)", "subValues": [] }] }, { "id": "712", "sicCode": "712", "title": "Technical testing and analysis", "subValues": [{ "id": "71200", "sicCode": "71200", "title": "Technical testing and analysis", "subValues": [] }] }] }, { "id": "72", "sicCode": "72", "title": "Scientific research and development", "subValues": [{ "id": "721", "sicCode": "721", "title": "Research and experimental development on natural sciences and engineering", "subValues": [{ "id": "72110", "sicCode": "72110", "title": "Research and experimental development on biotechnology", "subValues": [] }, { "id": "72190", "sicCode": "72190", "title": "Other research and experimental development on natural sciences and engineering", "subValues": [] }] }, { "id": "722", "sicCode": "722", "title": "Research and experimental development on social sciences and humanities", "subValues": [{ "id": "72200", "sicCode": "72200", "title": "Research and experimental development on social sciences and humanities", "subValues": [] }] }] }, { "id": "73", "sicCode": "73", "title": "Advertising and market research", "subValues": [{ "id": "731", "sicCode": "731", "title": "Advertising", "subValues": [{ "id": "73110", "sicCode": "73110", "title": "Advertising agencies", "subValues": [] }, { "id": "73120", "sicCode": "73120", "title": "Media representation", "subValues": [] }] }, { "id": "732", "sicCode": "732", "title": "Market research and public opinion polling", "subValues": [{ "id": "73200", "sicCode": "73200", "title": "Market research and public opinion polling", "subValues": [] }] }] }, { "id": "74", "sicCode": "74", "title": "Other professional, scientific and technical activities", "subValues": [{ "id": "741", "sicCode": "741", "title": "Specialised design activities", "subValues": [{ "id": "74100", "sicCode": "74100", "title": "Specialised design activities", "subValues": [] }] }, { "id": "742", "sicCode": "742", "title": "Photographic activities", "subValues": [{ "id": "74200", "sicCode": "74200", "title": "Photographic activities", "subValues": [] }, { "id": "74201", "sicCode": "74201", "title": "Portrait photographic activities", "subValues": [] }, { "id": "74202", "sicCode": "74202", "title": "Other specialist photography (not including portrait photography)", "subValues": [] }, { "id": "74203", "sicCode": "74203", "title": "Film processing", "subValues": [] }, { "id": "74209", "sicCode": "74209", "title": "Other photographic activities (not including portrait and other specialist photography and film processing) nec", "subValues": [] }] }, { "id": "743", "sicCode": "743", "title": "Translation and interpretation activities", "subValues": [{ "id": "74300", "sicCode": "74300", "title": "Translation and interpretation activities", "subValues": [] }] }, { "id": "749", "sicCode": "749", "title": "Other professional, scientific and technical activities nec", "subValues": [{ "id": "74900", "sicCode": "74900", "title": "Other professional, scientific and technical activities nec", "subValues": [] }, { "id": "74901", "sicCode": "74901", "title": "Environmental consulting activities", "subValues": [] }, { "id": "74902", "sicCode": "74902", "title": "Quantity surveying activities", "subValues": [] }, { "id": "74909", "sicCode": "74909", "title": "Other professional, scientific and technical activities (not including environmental consultancy or quantity surveying) nec", "subValues": [] }] }] }, { "id": "75", "sicCode": "75", "title": "Veterinary activities", "subValues": [{ "id": "750", "sicCode": "750", "title": "Veterinary activities", "subValues": [{ "id": "75000", "sicCode": "75000", "title": "Veterinary activities", "subValues": [] }] }] }] }, { "id": " N", "sicCode": " N", "title": "ADMINISTRATIVE AND SUPPORT SERVICE ACTIVITIES", "subValues": [{ "id": "77", "sicCode": "77", "title": "Rental and leasing activities", "subValues": [{ "id": "771", "sicCode": "771", "title": "Renting and leasing of motor vehicles", "subValues": [{ "id": "77110", "sicCode": "77110", "title": "Renting and leasing of cars and light motor vehicles", "subValues": [] }, { "id": "77120", "sicCode": "77120", "title": "Renting and leasing of trucks", "subValues": [] }] }, { "id": "772", "sicCode": "772", "title": "Renting and leasing of personal and household goods", "subValues": [{ "id": "77210", "sicCode": "77210", "title": "Renting and leasing of recreational and sports goods", "subValues": [] }, { "id": "77220", "sicCode": "77220", "title": "Renting of video tapes and disks", "subValues": [] }, { "id": "77290", "sicCode": "77290", "title": "Renting and leasing of other personal and household goods", "subValues": [] }, { "id": "77291", "sicCode": "77291", "title": "Renting and leasing of media entertainment equipment", "subValues": [] }, { "id": "77299", "sicCode": "77299", "title": "Renting and leasing of other personal and household goods (other than media entertainment equipment)", "subValues": [] }] }, { "id": "773", "sicCode": "773", "title": "Renting and leasing of other machinery, equipment and tangible goods", "subValues": [{ "id": "77310", "sicCode": "77310", "title": "Renting and leasing of agricultural machinery and equipment", "subValues": [] }, { "id": "77320", "sicCode": "77320", "title": "Renting and leasing of construction and civil engineering machinery and equipment", "subValues": [] }, { "id": "77330", "sicCode": "77330", "title": "Renting and leasing of office machinery and equipment (including computers)", "subValues": [] }, { "id": "77340", "sicCode": "77340", "title": "Renting and leasing of water transport equipment", "subValues": [] }, { "id": "77341", "sicCode": "77341", "title": "Renting and leasing of passenger water transport equipment", "subValues": [] }, { "id": "77342", "sicCode": "77342", "title": "Renting and leasing of freight water transport equipment", "subValues": [] }, { "id": "77350", "sicCode": "77350", "title": "Renting and leasing of air transport equipment", "subValues": [] }, { "id": "77351", "sicCode": "77351", "title": "Renting and leasing of passenger air transport equipment", "subValues": [] }, { "id": "77352", "sicCode": "77352", "title": "Renting and leasing of freight air transport equipment", "subValues": [] }, { "id": "77390", "sicCode": "77390", "title": "Renting and leasing of other machinery, equipment and tangible goods nec", "subValues": [] }] }, { "id": "774", "sicCode": "774", "title": "Leasing of intellectual property and similar products, except copyrighted works", "subValues": [{ "id": "77400", "sicCode": "77400", "title": "Leasing of intellectual property and similar products, except copyrighted works", "subValues": [] }] }] }, { "id": "78", "sicCode": "78", "title": "Employment activities", "subValues": [{ "id": "781", "sicCode": "781", "title": "Activities of employment placement agencies", "subValues": [{ "id": "78100", "sicCode": "78100", "title": "Activities of employment placement agencies", "subValues": [] }, { "id": "78101", "sicCode": "78101", "title": "Motion picture, television and other theatrical casting", "subValues": [] }, { "id": "78109", "sicCode": "78109", "title": "Activities of employment placement agencies (other than motion picture, television and other theatrical casting) nec", "subValues": [] }] }, { "id": "782", "sicCode": "782", "title": "Temporary employment agency activities", "subValues": [{ "id": "78200", "sicCode": "78200", "title": "Temporary employment agency activities", "subValues": [] }] }, { "id": "783", "sicCode": "783", "title": "Other human resources provision", "subValues": [{ "id": "78300", "sicCode": "78300", "title": "Other human resources provision", "subValues": [] }] }] }, { "id": "79", "sicCode": "79", "title": "Travel agency, tour operator and other reservation service and related activities", "subValues": [{ "id": "791", "sicCode": "791", "title": "Travel agency and tour operator activities", "subValues": [{ "id": "79110", "sicCode": "79110", "title": "Travel agency activities", "subValues": [] }, { "id": "79120", "sicCode": "79120", "title": "Tour operator activities", "subValues": [] }] }, { "id": "799", "sicCode": "799", "title": "Other reservation service and related activities", "subValues": [{ "id": "79900", "sicCode": "79900", "title": "Other reservation service and related activities", "subValues": [] }, { "id": "79901", "sicCode": "79901", "title": "Activities of tourist guides", "subValues": [] }, { "id": "79909", "sicCode": "79909", "title": "Other reservation service and related activities (not including activities of tourist guides)", "subValues": [] }] }] }, { "id": "80", "sicCode": "80", "title": "Security and investigation activities", "subValues": [{ "id": "801", "sicCode": "801", "title": "Private security activities", "subValues": [{ "id": "80100", "sicCode": "80100", "title": "Private security activities", "subValues": [] }] }, { "id": "802", "sicCode": "802", "title": "Security systems service activities", "subValues": [{ "id": "80200", "sicCode": "80200", "title": "Security systems service activities", "subValues": [] }] }, { "id": "803", "sicCode": "803", "title": "Investigation activities", "subValues": [{ "id": "80300", "sicCode": "80300", "title": "Investigation activities", "subValues": [] }] }] }, { "id": "81", "sicCode": "81", "title": "Services to buildings and landscape activities", "subValues": [{ "id": "811", "sicCode": "811", "title": "Combined facilities support activities", "subValues": [{ "id": "81100", "sicCode": "81100", "title": "Combined facilities support activities", "subValues": [] }] }, { "id": "812", "sicCode": "812", "title": "Cleaning activities", "subValues": [{ "id": "81210", "sicCode": "81210", "title": "General cleaning of buildings", "subValues": [] }, { "id": "81220", "sicCode": "81220", "title": "Other building and industrial cleaning activities", "subValues": [] }, { "id": "81221", "sicCode": "81221", "title": "Window cleaning services", "subValues": [] }, { "id": "81222", "sicCode": "81222", "title": "Specialised cleaning services", "subValues": [] }, { "id": "81223", "sicCode": "81223", "title": "Furnace and chimney cleaning services", "subValues": [] }, { "id": "81229", "sicCode": "81229", "title": "Building and industrial cleaning activities (other than window cleaning, specialised cleaning and furnace and chimney cleaning services) nec", "subValues": [] }, { "id": "81290", "sicCode": "81290", "title": "Other cleaning activities", "subValues": [] }, { "id": "81291", "sicCode": "81291", "title": "Disinfecting and extermination services", "subValues": [] }, { "id": "81299", "sicCode": "81299", "title": "Cleaning services (other than disinfecting and extermination services) nec", "subValues": [] }] }, { "id": "813", "sicCode": "813", "title": "Landscape service activities", "subValues": [{ "id": "81300", "sicCode": "81300", "title": "Landscape service activities", "subValues": [] }] }] }, { "id": "82", "sicCode": "82", "title": "Office administrative, office support and other business support activities", "subValues": [{ "id": "821", "sicCode": "821", "title": "Office administrative and support activities", "subValues": [{ "id": "82110", "sicCode": "82110", "title": "Combined office administrative service activities", "subValues": [] }, { "id": "82190", "sicCode": "82190", "title": "Photocopying, document preparation and other specialised office support activities", "subValues": [] }] }, { "id": "822", "sicCode": "822", "title": "Activities of call centres", "subValues": [{ "id": "82200", "sicCode": "82200", "title": "Activities of call centres", "subValues": [] }] }, { "id": "823", "sicCode": "823", "title": "Organisation of conventions and trade shows", "subValues": [{ "id": "82300", "sicCode": "82300", "title": "Organisation of conventions and trade shows", "subValues": [] }, { "id": "82301", "sicCode": "82301", "title": "Activities of exhibition and fair organizers", "subValues": [] }, { "id": "82302", "sicCode": "82302", "title": "Activities of conference organizers", "subValues": [] }] }, { "id": "829", "sicCode": "829", "title": "Business support service activities nec", "subValues": [{ "id": "82910", "sicCode": "82910", "title": "Activities of collection agencies and credit bureaus", "subValues": [] }, { "id": "82911", "sicCode": "82911", "title": "Activities of collection agencies", "subValues": [] }, { "id": "82922", "sicCode": "82922", "title": "Activities of credit bureaus", "subValues": [] }, { "id": "82920", "sicCode": "82920", "title": "Packaging activities", "subValues": [] }, { "id": "82990", "sicCode": "82990", "title": "Other business support service activities nec", "subValues": [] }] }] }] }, { "id": " O", "sicCode": " O", "title": "PUBLIC ADMINISTRATION AND DEFENCE; COMPULSORY SOCIAL SECURITY", "subValues": [{ "id": "84", "sicCode": "84", "title": "Public administration and defence; compulsory social security", "subValues": [{ "id": "841", "sicCode": "841", "title": "Administration of the State and the economic and social policy of the community", "subValues": [{ "id": "84110", "sicCode": "84110", "title": "General public administration activities", "subValues": [] }, { "id": "84120", "sicCode": "84120", "title": "Regulation of the activities of providing health care, education, cultural services and other social services, excluding social security", "subValues": [] }, { "id": "84130", "sicCode": "84130", "title": "Regulation of and contribution to more efficient operation of businesses", "subValues": [] }] }, { "id": "842", "sicCode": "842", "title": "Provision of services to the community as a whole", "subValues": [{ "id": "84210", "sicCode": "84210", "title": "Foreign affairs", "subValues": [] }, { "id": "84220", "sicCode": "84220", "title": "Defence activities", "subValues": [] }, { "id": "84230", "sicCode": "84230", "title": "Justice and judicial activities", "subValues": [] }, { "id": "84240", "sicCode": "84240", "title": "Public order and safety activities", "subValues": [] }, { "id": "84250", "sicCode": "84250", "title": "Fire service activities", "subValues": [] }] }, { "id": "843", "sicCode": "843", "title": "Compulsory social security activities", "subValues": [{ "id": "84300", "sicCode": "84300", "title": "Compulsory social security activities", "subValues": [] }] }] }] }, { "id": " P", "sicCode": " P", "title": "EDUCATION", "subValues": [{ "id": "85", "sicCode": "85", "title": "Education", "subValues": [{ "id": "851", "sicCode": "851", "title": "Pre-primary education", "subValues": [{ "id": "85100", "sicCode": "85100", "title": "Pre-primary education", "subValues": [] }] }, { "id": "852", "sicCode": "852", "title": "Primary education", "subValues": [{ "id": "85200", "sicCode": "85200", "title": "Primary education", "subValues": [] }] }, { "id": "853", "sicCode": "853", "title": "Secondary education", "subValues": [{ "id": "85310", "sicCode": "85310", "title": "General secondary education", "subValues": [] }, { "id": "85320", "sicCode": "85320", "title": "Technical and vocational secondary education", "subValues": [] }] }, { "id": "854", "sicCode": "854", "title": "Higher education", "subValues": [{ "id": "85410", "sicCode": "85410", "title": "Post-secondary non-tertiary education", "subValues": [] }, { "id": "85420", "sicCode": "85420", "title": "Tertiary education", "subValues": [] }, { "id": "85421", "sicCode": "85421", "title": "First-degree level higher education", "subValues": [] }, { "id": "85422", "sicCode": "85422", "title": "Post-graduate level higher education", "subValues": [] }] }, { "id": "855", "sicCode": "855", "title": "Other education", "subValues": [{ "id": "85510", "sicCode": "85510", "title": "Sports and recreation education", "subValues": [] }, { "id": "85520", "sicCode": "85520", "title": "Cultural education", "subValues": [] }, { "id": "85530", "sicCode": "85530", "title": "Driving school activities", "subValues": [] }, { "id": "85590", "sicCode": "85590", "title": "Other education nec", "subValues": [] }] }, { "id": "856", "sicCode": "856", "title": "Educational support activities", "subValues": [{ "id": "85600", "sicCode": "85600", "title": "Educational support activities", "subValues": [] }] }] }] }, { "id": " Q", "sicCode": " Q", "title": "HUMAN HEALTH AND SOCIAL WORK ACTIVITIES", "subValues": [{ "id": "86", "sicCode": "86", "title": "Human health activities", "subValues": [{ "id": "861", "sicCode": "861", "title": "Hospital activities", "subValues": [{ "id": "86100", "sicCode": "86100", "title": "Hospital activities", "subValues": [] }, { "id": "86101", "sicCode": "86101", "title": "Hospital activities", "subValues": [] }, { "id": "86102", "sicCode": "86102", "title": "Medical nursing home activities", "subValues": [] }] }, { "id": "862", "sicCode": "862", "title": "Medical and dental practice activities", "subValues": [{ "id": "86210", "sicCode": "86210", "title": "General medical practice activities", "subValues": [] }, { "id": "86220", "sicCode": "86220", "title": "Specialist medical practice activities", "subValues": [] }, { "id": "86230", "sicCode": "86230", "title": "Dental practice activities", "subValues": [] }] }, { "id": "869", "sicCode": "869", "title": "Other human health activities", "subValues": [{ "id": "86900", "sicCode": "86900", "title": "Other human health activities", "subValues": [] }] }] }, { "id": "87", "sicCode": "87", "title": "Residential care activities", "subValues": [{ "id": "871", "sicCode": "871", "title": "Residential nursing care activities", "subValues": [{ "id": "87100", "sicCode": "87100", "title": "Residential nursing care activities", "subValues": [] }] }, { "id": "872", "sicCode": "872", "title": "Residential care activities for learning disabilities, mental health and substance abuse", "subValues": [{ "id": "87200", "sicCode": "87200", "title": "Residential care activities for learning disabilities, mental health and substance abuse", "subValues": [] }] }, { "id": "873", "sicCode": "873", "title": "Residential care activities for the elderly and disabled", "subValues": [{ "id": "87300", "sicCode": "87300", "title": "Residential care activities for the elderly and disabled", "subValues": [] }] }, { "id": "879", "sicCode": "879", "title": "Other residential care activities", "subValues": [{ "id": "87900", "sicCode": "87900", "title": "Other residential care activities", "subValues": [] }] }] }, { "id": "88", "sicCode": "88", "title": "Social work activities without accommodation", "subValues": [{ "id": "881", "sicCode": "881", "title": "Social work activities without accommodation for the elderly and disabled", "subValues": [{ "id": "88100", "sicCode": "88100", "title": "Social work activities without accommodation for the elderly and disabled", "subValues": [] }] }, { "id": "889", "sicCode": "889", "title": "Other social work activities without accommodation", "subValues": [{ "id": "88910", "sicCode": "88910", "title": "Child day-care activities", "subValues": [] }, { "id": "88990", "sicCode": "88990", "title": "Other social work activities without accommodation nec", "subValues": [] }] }] }] }, { "id": " R", "sicCode": " R", "title": "ARTS, ENTERTAINMENT AND RECREATION", "subValues": [{ "id": "90", "sicCode": "90", "title": "Creative, arts and entertainment activities", "subValues": [{ "id": "900", "sicCode": "900", "title": "Creative, arts and entertainment activities", "subValues": [{ "id": "90010", "sicCode": "90010", "title": "Performing arts", "subValues": [] }, { "id": "90020", "sicCode": "90020", "title": "Support activities to performing arts", "subValues": [] }, { "id": "90030", "sicCode": "90030", "title": "Artistic creation", "subValues": [] }, { "id": "90040", "sicCode": "90040", "title": "Operation of arts facilities", "subValues": [] }] }] }, { "id": "91", "sicCode": "91", "title": "Libraries, archives, museums and other cultural activities", "subValues": [{ "id": "910", "sicCode": "910", "title": "Libraries, archives, museums and other cultural activities", "subValues": [{ "id": "91010", "sicCode": "91010", "title": "Library and archive activities", "subValues": [] }, { "id": "91011", "sicCode": "91011", "title": "Library activities", "subValues": [] }, { "id": "91012", "sicCode": "91012", "title": "Archive activities", "subValues": [] }, { "id": "91020", "sicCode": "91020", "title": "Museum activities", "subValues": [] }, { "id": "91030", "sicCode": "91030", "title": "Operation of historical sites and buildings and similar visitor attractions", "subValues": [] }, { "id": "91040", "sicCode": "91040", "title": "Botanical and zoological gardens and nature reserve activities", "subValues": [] }] }] }, { "id": "92", "sicCode": "92", "title": "Gambling and betting activities", "subValues": [{ "id": "920", "sicCode": "920", "title": "Gambling and betting activities", "subValues": [{ "id": "92000", "sicCode": "92000", "title": "Gambling and betting activities", "subValues": [] }] }] }, { "id": "93", "sicCode": "93", "title": "Sports activities and amusement and recreation activities", "subValues": [{ "id": "931", "sicCode": "931", "title": "Sports activities", "subValues": [{ "id": "93110", "sicCode": "93110", "title": "Operation of sports facilities", "subValues": [] }, { "id": "93120", "sicCode": "93120", "title": "Activities of sport clubs", "subValues": [] }, { "id": "93130", "sicCode": "93130", "title": "Fitness facilities", "subValues": [] }, { "id": "93190", "sicCode": "93190", "title": "Other sports activities", "subValues": [] }, { "id": "93191", "sicCode": "93191", "title": "Activities of racehorse owners", "subValues": [] }, { "id": "93199", "sicCode": "93199", "title": "Other sports activities (not including activities of racehorse owners) nec", "subValues": [] }] }, { "id": "932", "sicCode": "932", "title": "Amusement and recreation activities", "subValues": [{ "id": "93210", "sicCode": "93210", "title": "Activities of amusement parks and theme parks", "subValues": [] }, { "id": "93290", "sicCode": "93290", "title": "Other amusement and recreation activities", "subValues": [] }] }] }] }, { "id": " S", "sicCode": " S", "title": "OTHER SERVICE ACTIVITIES", "subValues": [{ "id": "94", "sicCode": "94", "title": "Activities of membership organisations", "subValues": [{ "id": "941", "sicCode": "941", "title": "Activities of business, employers and professional membership organisations", "subValues": [{ "id": "94110", "sicCode": "94110", "title": "Activities of business and employers membership organisations", "subValues": [] }, { "id": "94120", "sicCode": "94120", "title": "Activities of professional membership organisations", "subValues": [] }] }, { "id": "942", "sicCode": "942", "title": "Activities of trade unions", "subValues": [{ "id": "94200", "sicCode": "94200", "title": "Activities of trade unions", "subValues": [] }] }, { "id": "949", "sicCode": "949", "title": "Activities of other membership organisations", "subValues": [{ "id": "94910", "sicCode": "94910", "title": "Activities of religious organisations", "subValues": [] }, { "id": "94920", "sicCode": "94920", "title": "Activities of political organisations", "subValues": [] }, { "id": "94990", "sicCode": "94990", "title": "Activities of other membership organisations nec", "subValues": [] }] }] }, { "id": "95", "sicCode": "95", "title": "Repair of computers and personal and household goods", "subValues": [{ "id": "951", "sicCode": "951", "title": "Repair of computers and communication equipment", "subValues": [{ "id": "95110", "sicCode": "95110", "title": "Repair of computers and peripheral equipment", "subValues": [] }, { "id": "95120", "sicCode": "95120", "title": "Repair of communication equipment", "subValues": [] }] }, { "id": "952", "sicCode": "952", "title": "Repair of personal and household goods", "subValues": [{ "id": "95210", "sicCode": "95210", "title": "Repair of consumer electronics", "subValues": [] }, { "id": "95220", "sicCode": "95220", "title": "Repair of household appliances and home and garden equipment", "subValues": [] }, { "id": "95230", "sicCode": "95230", "title": "Repair of footwear and leather goods", "subValues": [] }, { "id": "95240", "sicCode": "95240", "title": "Repair of furniture and home furnishings", "subValues": [] }, { "id": "95250", "sicCode": "95250", "title": "Repair of watches, clocks and jewellery", "subValues": [] }, { "id": "95290", "sicCode": "95290", "title": "Repair of other personal and household goods", "subValues": [] }] }] }, { "id": "96", "sicCode": "96", "title": "Other personal service activities", "subValues": [{ "id": "960", "sicCode": "960", "title": "Other personal service activities", "subValues": [{ "id": "96010", "sicCode": "96010", "title": "Washing and (dry-)cleaning of textile and fur products", "subValues": [] }, { "id": "96020", "sicCode": "96020", "title": "Hairdressing and other beauty treatment", "subValues": [] }, { "id": "96030", "sicCode": "96030", "title": "Funeral and related activities", "subValues": [] }, { "id": "96040", "sicCode": "96040", "title": "Physical well-being activities", "subValues": [] }, { "id": "96090", "sicCode": "96090", "title": "Other personal service activities nec", "subValues": [] }] }] }] }, { "id": " T", "sicCode": " T", "title": "ACTIVITIES OF HOUSEHOLDS AS EMPLOYERS; UNDIFFERENTIATED GOODS-AND SERVICES-PRODUCING ACTIVITIES OF HOUSEHOLDS FOR OWN USE", "subValues": [{ "id": "97", "sicCode": "97", "title": "Activities of households as employers of domestic personnel", "subValues": [{ "id": "970", "sicCode": "970", "title": "Activities of households as employers of domestic personnel", "subValues": [{ "id": "97000", "sicCode": "97000", "title": "Activities of households as employers of domestic personnel", "subValues": [] }] }] }, { "id": "98", "sicCode": "98", "title": "Undifferentiated goods- and services-producing activities of private households for own use", "subValues": [{ "id": "981", "sicCode": "981", "title": "Undifferentiated goods-producing activities of private households for own use", "subValues": [{ "id": "98100", "sicCode": "98100", "title": "Undifferentiated goods-producing activities of private households for own use", "subValues": [] }] }, { "id": "982", "sicCode": "982", "title": "Undifferentiated service-producing activities of private households for own use", "subValues": [{ "id": "98200", "sicCode": "98200", "title": "Undifferentiated service-producing activities of private households for own use", "subValues": [] }] }] }] }, { "id": " U", "sicCode": " U", "title": "ACTIVITIES OF EXTRATERRITORIAL ORGANISATIONS AND BODIES", "subValues": [{ "id": "99", "sicCode": "99", "title": "Activities of extraterritorial organisations and bodies", "subValues": [{ "id": "990", "sicCode": "990", "title": "Activities of extraterritorial organisations and bodies", "subValues": [{ "id": "99000", "sicCode": "99000", "title": "Activities of extraterritorial organisations and bodies", "subValues": [] }] }] }] }]