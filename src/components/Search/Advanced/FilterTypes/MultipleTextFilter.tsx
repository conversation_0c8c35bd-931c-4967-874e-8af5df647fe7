import "../../../../styling/filters.css";
import React, { useEffect, useState, useRef, useContext } from "react";
import { ParentContext } from "../../../constants/ParentContext";
import { Mixpanel } from "../../../mixpanel/mixpanel";

interface MultipleTextFilterProps {
  filter: any;
  addedFilters: any;
  apply: any;
  hideApply?: any;
  clear: any;
  inIndustry?: any;
}

const MultipleTextFilter: React.FC<MultipleTextFilterProps> = ({
  filter,
  addedFilters,
  apply,
  hideApply,
  clear,
  inIndustry,
}) => {
  // #region CONSTANTS & STATE VARIABLES
  const context = useContext<any>(ParentContext);
  const isBiz = context.isBiz4Biz || context.isHarbour ? false : true;

  const [text, setText] = useState("");
  const [chosenOptions, setChosenOptions] = useState<any>([]);
  const [focused, setFocused] = useState(false);
  const [updateVar, setUpdateVar] = useState(0);

  const gridRef = useRef<any>();

  let user = JSON.parse(localStorage.getItem("user")!)

  // #endregion
  useEffect(() => {
    setChosenOptions([]);

    addedFilters?.forEach((element: any) => {
      if (element.id === filter.id) {
        // setText(element.value.text)
        setChosenOptions(element.value.inputs);
      }
    });
  }, [addedFilters.length]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    gridRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chosenOptions.length]);

  // #region SHOW COMPONENTS
  const showSelected = () => {
    let compArray: any = [];

    chosenOptions.forEach((element: any) => {
      compArray.push(
        <div className="appliedFilterValue spaced" key={element}>
          <span className="text-xs semibold gray-700 alignLeft oneLine">
            {element}
          </span>
          <img
            className="appliedFilterClose"
            src="/assets/x-close.png"
            onClick={() => remove(element)}
            alt="close"
          />
        </div>
      );
    });

    return compArray;
  };

  // #endregion

  // #region WEB REQUESTS

  // #endregion

  // #region BUTTONS CLICKED

  const remove = (name: any) => {
    var index = 0;
    chosenOptions.forEach((element: any) => {
      if (element === name) {
        chosenOptions.splice(index, 1);
      }
      index++;
    });
    setChosenOptions(chosenOptions);
    setUpdateVar(updateVar + 1);

    if (chosenOptions.length === 0) {
      clear();
    } else {
      apply(filter, { inputs: chosenOptions });
    }
  };

  const applyFilter = () => {

    Mixpanel.track("FeatureUsage", {
      $name: user?.name,
      $email: user?.email,
      "Plan": user?.plan,
      "User Type": "Buyer",
      "User ID": user.uid,
      "Date": new Date().toISOString(),
      "Feature": `Applied ${filter.title} Filter`
    });

    apply(filter, { inputs: chosenOptions });
  };

  // #endregion

  // #region OTHER

  // #endregion


  const addTag = () => {

    let tags = text.split(',')

    for (const tag of tags) {
      if (!chosenOptions.includes(tag)) {
        chosenOptions.push(tag);
      }
    }

    setChosenOptions(chosenOptions);

    if (inIndustry) {
      apply(filter, { inputs: chosenOptions });
    }
    setText("");
  };

  const isDisabled = () => {
    if (chosenOptions.length === 0) {
      return true;
    } else {
      return false;
    }
  };

  return (
    <div className="textFilter padding3">
      <div
        className={`textInput ${focused ? "focus" : ""
          } search text-md regular gray-900`}
      >
        <div className="searchMultipleGrid" style={{ width: "100%" }}>
          {showSelected()}

          <input
            ref={gridRef}
            className="textInput inner"
            type="text"
            placeholder={filter.placeholder}
            value={text}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            onChange={(e) => setText(e.target.value)}
            onKeyPress={(event) => {
              if (event.key === "Enter") {
                addTag();
              }
            }}
          />
        </div>
      </div>

      {!inIndustry && (
        <button
          className="secondaryButton applyFilterBtn"
          disabled={isDisabled()}
          onClick={applyFilter}
        >
          <img
            className="secondaryButtonIcon"
            src={
              isDisabled()
                ? "/assets/grey-plus.png"
                : isBiz
                  ? "/assets/red-plus.png"
                  : "/assets/blue-plus.png"
            }
            alt="plus"
          />
          <span
            className={`text-xs semibold ${isDisabled() ? "gray-300" : "primary-700"
              }`}
          >
            Apply filter
          </span>
        </button>
      )}
    </div>
  );
}

export default MultipleTextFilter;