import "../../../../styling/search.css";
import React, { useEffect, useState } from "react";
import SICFilter from "../../Advanced/FilterTypes/SICFilter";
import AIIcon from "components/constants/AiIcon";
import MultipleTextFilter from "components/Search/Advanced/FilterTypes/MultipleTextFilter";
import { Mixpanel } from "components/mixpanel/mixpanel";
import { auth } from "index";
import { Badge } from "components/shadcn/ui/badge";

interface IndustrySearchProps {
  appliedFilters: any;
  applyFilter: any;
  remove: any;
  done: any;
  setIncludeInParent?: (value: boolean) => void;
}

const IndustrySearch: React.FC<IndustrySearchProps> = ({
  appliedFilters,
  applyFilter,
  remove,
  done,
  setIncludeInParent,
}) => {
  // #region CONSTANTS & STATE VARIABLES
  const [searchType, setSearchType] = useState("sic");
  const [include, setInclude] = useState(() => {
    const savedValue = localStorage.getItem("includeInIndustrySearch");
    return savedValue !== null ? JSON.parse(savedValue) : true;
  });

  // #endregion

  // #region SHOW COMPONENTS

  // #endregion

  // #region WEB REQUESTS

  // #endregion

  // #region BUTTONS CLICKED

  // #endregion

  // #region OTHER

  // #endregion

  const handleToggle = () => {
    const newValue = !include;
    setInclude(newValue);
    localStorage.setItem("includeInIndustrySearch", JSON.stringify(newValue));

    if (typeof setIncludeInParent === "function") {
      setIncludeInParent(newValue);
    }

  };

  const clear = () => {
    remove(filter);
    remove(filter2);
    remove(filter3);
    localStorage.setItem("includeInIndustrySearch", JSON.stringify("true"));
    setInclude(true);
    if (typeof setIncludeInParent === "function") {
      setIncludeInParent(true);
    }
  };

  let filter = {
    id: "2C",
    title: "SIC Industry name",
    type: "multiple",
    subType: "sic",
    placeholder: "Enter SIC Code",
  };

  let filter2: any = {
    id: "2C2",
    title: "ISIC Industry name",
    type: "multiple",
    subType: "sic",
    placeholder: "Enter ISIC Code",
  };

  let filter3: any = {
    id: "2I",
    title: "KeyWord",
    type: "multiple",
    placeholder: "Search by company keywords",
    subType: "text",
  };

  useEffect(() => {
    Mixpanel.track("Feature Usage", {
      $name: auth.currentUser?.displayName,
      $email: auth.currentUser?.email,
      "User Type": "Buyer",
      "User ID": auth.currentUser?.uid,
      "Date": new Date().toISOString(),
      "Selected Filter": "Industry",
      "Include in Industry Search": include,
      "Selected Filter Type": searchType === "sic" ? "SIC and ISIC Code" : "AI",
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchType]);

  useEffect(() => {
    if (typeof setIncludeInParent === "function" && appliedFilters) {
      const savedValue = localStorage.getItem("includeInIndustrySearch");
      if (savedValue !== null) {
        setInclude(JSON.parse(savedValue));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="searchBubble industry !overflow-y-visible">
      <div className="searchBubbleTitleRow">
        {/* <span className="text-sm medium gray-700">Industry & Keywords</span> */}
        <span className="text-sm medium gray-700">Industry</span>
        <span className="text-sm medium primary-700 pointer" onClick={clear}>
          Clear
        </span>
      </div>

      <div className="row gap20 ai-centre">
        <div className="radioRow">
          <input
            className="radio input-sm"
            type="radio"
            checked={searchType === "sic"}
            onChange={() => setSearchType("sic")}
          />
          <span className="text-sm medium gray-700">{"SIC & ISIC Code"}</span>

        </div>
        <div className="radioRow">
          <input
            className="radio input-sm"
            type="radio"
            checked={searchType === "ai"}
            onChange={() => setSearchType("ai")}
          />
          <AIIcon />
          <span
            className="text-sm medium"
            style={{
              background: `var(--bizcrunch-gradient)`,
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            {"AI Company Search"}
          </span>
        </div>
      </div>

      {searchType === "sic" &&
        <div className="row gap20 ai-centre mt-2">
          <Badge
            variant={include ? "default" : "outline"}
            className={`cursor-pointer min-h-[30px] ${include ? "bg-primary-700" : ""}`}
            onClick={handleToggle}
          >
            Includes
          </Badge>
          <Badge
            variant={!include ? "secondary" : "outline"}
            className={`cursor-pointer  min-h-[30px] ${!include ? "bg-primary-700 text-white" : ""}`}
            onClick={handleToggle}
          >
            Doesn’t include
          </Badge>
        </div>
      }

      {searchType === "sic" && (
        <SICFilter
          filter={filter}
          addedFilters={appliedFilters}
          apply={applyFilter}
          clear={() => remove(filter)}
          useParentInclude={true}
        />
      )}

      {searchType === "sic" && (
        <SICFilter
          filter={filter2}
          addedFilters={appliedFilters}
          apply={applyFilter}
          clear={() => remove(filter2)}
          isic={true}
          useParentInclude={true}
        />
      )}

      {searchType === "ai" && (
        <>
          <MultipleTextFilter
            filter={filter3}
            addedFilters={appliedFilters}
            apply={applyFilter}
            clear={() => remove(filter3)}
            inIndustry={true}
          />
          <div className="text-sm font-normal text-gray-600 text-left">
            Our AI company search pinpoints the exact businesses you need, scouring company websites to find precisely the businesses that matter to you.
          </div>
        </>

      )}

      <div className="loginForgotRow margin24">
        <button onClick={done} className="text-sm medium gray-700">
          Done
        </button>
      </div>
    </div>
  );
}
export default IndustrySearch; 