import '../../../../styling/search.css';
import React from 'react';

interface TutorialBubbleProps {
    step: string;
    next: () => void;
    close?: () => void;
}

const TutorialBubble: React.FC<TutorialBubbleProps> = ({ step, next }) => {
    const getHeader = () => {
        switch (step) {
            case 'location': return 'Add Location';
            case 'industry': return 'Add Industry';
            case 'revenue': return 'Define Revenue';
            case 'saf': return 'Add smart acquire filters';
            case 'more': return 'Advanced filters';
            case 'resultRow': return 'See company details';
            case 'savetocollection': return 'Save to collection';
            case 'collectionlist': return 'Collections';
            case 'campaign': return 'Campaigns';
            default: return '';
        }
    };

    const getContent = () => {
        switch (step) {
            case 'location': return 'Select a target location for your company search, or set a search radius from a set location.\n Tip: Radius searches tend to get more results!';
            case 'industry': return 'Select an industry sector or SIC code to pinpoint the ideal company.';
            case 'revenue': return 'Specify your desired revenue range to discover companies that perfectly match your ideal profile.';
            case 'saf': return 'Enhance your search by applying intelligent acquisition filters for pinpointing for the ideal company. You only get to pick one smart filter at a time on the free trial - so choose wisely! ';
            case 'more': return 'Customise your search using advanced filters. Get granular with debt ratios, incorporation dates, contact detail requirements and more!';
            case 'resultRow': return 'View company details by hovering over a company name and clicking “see more”.';
            case 'savetocollection': return 'Save your search results as a Collection to easily revisit, track and export high-potential companies later.';
            case 'collectionlist': return 'This is where you can view all the collections you have added. You can use these collections to start a campaign';
            case 'campaign': return 'Create outreach campaigns using your saved Collections, send them to potential sellers at scale, and track responses here. ';
            default: return '';
        }
    };

    const getImage = () => {
        switch (step) {
            case 'location': return '/assets/tbImg1.png';
            case 'industry': return '/assets/tbImg2.png';
            case 'revenue': return '/assets/tbImg3.png';
            case 'saf': return '/assets/tbImg4.png';
            case 'more': return '/assets/tbImg5.png';
            case 'resultRow': return '/assets/tbImg6.png';
            case 'savetocollection': return '/assets/savetocollection.png';
            case 'collectionlist': return '/assets/collectionlist.png';
            case 'campaign': return '/assets/campaign.png';
            default: return '';
        }
    };

    return (
        <div className={`tutorialBubble ${step}`}>
            <img className={`tbImg ${step === "more" ? "more" : ""}`} src={getImage()} alt='tutorialImg' />
            <div className='column ai-start gap8 paddingSides8'>
                <span className='text-md semibold'>{getHeader()}</span>
                <span className='text-xs medium alignLeft'>{getContent()}</span>
            </div>
            <div className='width100 row ai-centre jc-between paddingSides16 paddingBottom8'>
                <button className='tbButton' onClick={next}>
                    <span className='text-sm semibold'>{step === "more" || step === "campaign" ? "Finish" : "Next"}</span>
                </button>
            </div>
        </div>
    );
};

export default TutorialBubble;