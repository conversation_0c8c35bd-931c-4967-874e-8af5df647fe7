
import '../../../../styling/search.css'
import React, { useEffect, useState } from 'react';

import Tooltip from '../../../constants/Tooltip';
import { Mixpanel } from '../../../mixpanel/mixpanel';
import { auth } from '../../../..';

interface SmartAcquireSearchProps {
    appliedFilters: any;
    applyFilter: any;
    removeFilter: any;
    done: any;
}

const SmartAcquireSearch: React.FC<SmartAcquireSearchProps> = ({ appliedFilters, applyFilter, removeFilter, done }) => {


    // #region CONSTANTS & STATE VARIABLES

    const [lowDebt, setLowDebt] = useState(false)
    const [profitable, setProfitable] = useState(false)
    const [established, setEstablished] = useState(false)
    const [retiringOwner, setRetiringOwner] = useState(false)
    const [simpleOwnership, setSimpleOwnership] = useState(false)

    const [valueChosen, setValueChosen] = useState(false)
    const [anyButtonPressed, setAnyButtonPressed] = useState(false)
    const isDisabled = appliedFilters.some((f: { id: string }) => f.id === "2D");

    const primary600 = getComputedStyle(document.body).getPropertyValue('--primary-600');

    // #endregion


    // #region SHOW COMPONENTS

    // #endregion


    // #region WEB REQUESTS

    // #endregion


    // #region BUTTONS CLICKED

    // #endregion


    // #region OTHER

    // #endregion

    let filter = { id: "1A", title: "Smart acquire filters", type: "multiple", values: ["Leverageable", "Profitable", "Established", "Retiring Owner", "Simple onwership"] }


    useEffect(() => {

        appliedFilters.forEach((element: any) => {
            if (element.id === filter.id) {
                element.value.forEach((value: any) => {
                    if (value === "Leverageable") { setLowDebt(true) }
                    if (value === "Profitable") { setProfitable(true) }
                    if (value === "Established") { setEstablished(true) }
                    if (value === "Retiring Owner") { setRetiringOwner(true) }
                    if (value === "Simple ownership") { setSimpleOwnership(true) }
                    setValueChosen(true)
                });
            }
        });

    }, []) // eslint-disable-line react-hooks/exhaustive-deps


    useEffect(() => {

        let array = []

        if (lowDebt) { array.push("Leverageable") }
        if (profitable) { array.push("Profitable") }
        if (established) { array.push("Established") }
        if (retiringOwner) { array.push("Retiring Owner") }
        if (simpleOwnership) { array.push("Simple ownership") }

        if (array.length > 0) {
            applyFilter(filter, array)
        }
        else if (valueChosen) {
            removeFilter(filter, array)
        }


    }, [lowDebt, profitable, established, retiringOwner, simpleOwnership]) // eslint-disable-line react-hooks/exhaustive-deps




    const clear = () => {
        removeFilter(filter, [])
        removeFilter(filter, [])
        removeFilter(filter, [])
        removeFilter(filter, [])
        removeFilter(filter, [])

        setLowDebt(false)
        setProfitable(false)
        setEstablished(false)
        setRetiringOwner(false)
        setSimpleOwnership(false)
    }

    useEffect(() => {
        if (anyButtonPressed) {
            Mixpanel.track("FeatureUsage", {
                $name: auth.currentUser?.displayName,
                $email: auth.currentUser?.email,
                //"Plan": auth.currentUser?.plan,
                "User Type": "Buyer",
                "User ID": auth.currentUser?.uid,
                "Date": new Date().toISOString(),
                "Feature": "Applied Smart Filters"
            });
        }

    }, [anyButtonPressed])



    return (
        <div className="searchBubble smartAcquire">

            <div className='searchBubbleTitleRow'>
                <div className='searchBubbleTitleSec'>
                    <span className='text-sm semibold gray-700'>Smart Acquire Filter</span>
                    <span className='text-sm regular gray-600'>Select acquire filters</span>
                </div>
                <span className='text-sm medium primary-700 pointer' onClick={clear}>Clear</span>
            </div>



            <div className='safGrid'>

                <div className={`smartAcquireFilter ${lowDebt ? "selected" : ""}`} onClick={() => { setLowDebt(!lowDebt); setAnyButtonPressed(true) }}>

                    <div className='safLeft'>
                        {lowDebt && <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M2.5 6H9.5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>}
                        {!lowDebt && <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 2.5V9.5M2.5 6H9.5" stroke={primary600} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>}
                        <span className={`safTitle text-xs semibold ${lowDebt ? "selected" : ""}`}>Leverageable</span>
                        <Tooltip icon={lowDebt ? 'dark' : 'normal'} text={"Has available borrowing capacity against its existing assets. Low debt & high proportion of fixed assets"} />
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <g clipPath="url(#clip0_1076_11670)">
                            <path d="M11.333 2.22587C13.3257 3.37856 14.6663 5.53304 14.6663 8.00065C14.6663 11.6826 11.6816 14.6673 7.99967 14.6673C4.31778 14.6673 1.33301 11.6826 1.33301 8.00065C1.33301 5.53304 2.67367 3.37856 4.66634 2.22587M5.33301 8.00065L7.99967 10.6673M7.99967 10.6673L10.6663 8.00065M7.99967 10.6673V1.33398" stroke={lowDebt ? "white" : "black"} strokeWidth="1.4" strokeLinecap="round" strokeLinejoin="round" />
                        </g>
                        <defs>
                            <clipPath id="clip0_1076_11670">
                                <rect width="16" height="16" fill={lowDebt ? "white" : "black"} />
                            </clipPath>
                        </defs>
                    </svg>

                </div>

                <div className={`smartAcquireFilter ${retiringOwner ? "selected" : ""}`} onClick={() => { setRetiringOwner(!retiringOwner); setAnyButtonPressed(true) }}>

                    <div className='safLeft'>
                        {retiringOwner && <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M2.5 6H9.5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>}
                        {!retiringOwner && <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 2.5V9.5M2.5 6H9.5" stroke={primary600} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>}
                        <span className={`safTitle text-xs semibold ${retiringOwner ? "selected" : ""}`}>Retiring Owner</span>
                        <Tooltip icon={retiringOwner ? 'dark' : 'normal'} text={"All majority shareholders are coming up to retirement age"} />
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M7.99312 8.25622V14.8723C8.06484 13.4733 7.99312 8.25622 7.99312 8.25622ZM7.99312 8.25622L4.10256 11.5807C4.08833 11.5807 4.01276 11.5186 3.93463 11.4426C3.51532 11.0347 3.31088 10.6958 3.15252 10.1458C3.09172 9.93465 3.09691 9.35421 3.16202 9.08325C3.23326 8.78667 3.28315 8.64832 3.42603 8.35073C3.55571 8.08063 3.77284 7.73556 3.93118 7.54788C4.07791 7.37398 4.1622 7.28159 4.26027 7.18713C4.32395 7.12581 4.3693 7.06894 4.36107 7.06074C4.35287 7.05255 3.81825 7.04585 3.17306 7.04585H2C2.00152 6.62199 2.06388 6.18362 2.2474 5.79711C2.34728 5.58673 2.55575 5.28903 2.71124 5.13467C2.92795 4.91958 3.1955 4.7614 3.46823 4.62917C3.83788 4.44999 4.42679 4.31929 4.9963 4.29004L5.29821 4.27453L5.05596 4.02259C4.29867 3.23508 4.0653 2.99157 3.96228 2.88136L3.84773 2.75885L3.92684 2.68714C4.25027 2.39398 4.65546 2.20659 5.0683 2.08188C5.12878 2.06361 5.28715 2.03557 5.42022 2.01958C5.8432 1.96881 6.1693 2.02237 6.56952 2.11592C6.82809 2.17637 7.011 2.30681 7.2348 2.42353C7.34621 2.48159 7.62461 2.70907 7.79516 2.88138C7.89165 2.97889 7.98073 3.05859 7.99312 3.05853C8.00548 3.05844 8.11954 2.95711 8.24656 2.83334C8.37358 2.70954 8.56296 2.55173 8.66737 2.48264C9.03272 2.2409 9.4602 2.08515 9.89618 2.02501C10.3344 1.96457 10.7652 2.02193 11.1829 2.16587C11.3428 2.221 12.1397 2.53988 12.1397 2.7629C12.1397 2.76893 12.1026 2.81184 12.0572 2.85829C12.0118 2.90471 11.7916 3.13342 11.5678 3.36652C11.344 3.59962 11.055 3.89927 10.9257 4.03243L10.6904 4.27453L10.9903 4.28974C11.5767 4.31951 12.1238 4.43895 12.4974 4.61877C12.8056 4.76717 13.0954 4.94664 13.335 5.19413C13.5962 5.46399 13.7904 5.82829 13.9076 6.26813C13.9473 6.41703 14.0155 6.99618 13.9968 7.02618C13.9899 7.03739 13.4812 7.04585 12.8129 7.04585C12.1684 7.04585 11.636 7.05097 11.6297 7.05722C11.6234 7.06346 11.6875 7.13988 11.772 7.227C12.1646 7.63155 12.4214 8.01666 12.6741 8.57964C12.7522 8.7537 12.8448 9.12865 12.8698 9.37176C12.916 9.82231 12.8484 10.2471 12.6716 10.6168C12.6138 10.7377 12.4577 10.9999 12.4153 11.0475C12.4028 11.0615 12.3581 11.1157 12.3159 11.168C12.1931 11.3201 11.923 11.5807 11.888 11.5807L7.99312 8.25622Z" stroke={retiringOwner ? "white" : "black"} strokeWidth="1.5" strokeLinejoin="round" />
                    </svg>
                </div>


                <div className={`smartAcquireFilter ${profitable ? "selected" : ""}`} onClick={() => { setProfitable(!profitable); setAnyButtonPressed(true) }}>

                    <div className='safLeft'>
                        {profitable && <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M2.5 6H9.5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>}
                        {!profitable && <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 2.5V9.5M2.5 6H9.5" stroke={primary600} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>}
                        <span className={`safTitle text-xs semibold ${profitable ? "selected" : ""}`}>Profitable</span>
                        <Tooltip icon={profitable ? 'dark' : 'normal'} text={"Reasonable net profit margins filed or inferred through retained earnings"} />

                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M3 10.5C4.8 7.92857 5.43509 6.49472 6.68182 6.83333C7.60227 7.08333 7.68048 8.28676 8.72727 8.5C10.1299 8.78571 10.65 6.64286 12 4.5" stroke={profitable ? "white" : "black"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                </div>

                <div className={`smartAcquireFilter ${simpleOwnership ? "selected" : ""}`} onClick={() => { setSimpleOwnership(!simpleOwnership); setAnyButtonPressed(true) }}>

                    <div className='safLeft'>
                        {simpleOwnership && <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M2.5 6H9.5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>}
                        {!simpleOwnership && <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 2.5V9.5M2.5 6H9.5" stroke={primary600} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>}
                        <span className={`safTitle text-xs semibold ${simpleOwnership ? "selected" : ""}`}>Simple Ownership</span>
                        <Tooltip icon={simpleOwnership ? 'dark' : 'normal'} text={"Small number of PSCs and single layer group structure"} />

                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8.25 9.8125H5.4375C4.56527 9.8125 4.12916 9.8125 3.77429 9.92015C2.97528 10.1625 2.35003 10.7878 2.10765 11.5868C2 11.9417 2 12.3778 2 13.25M10.75 11.375L12 12.625L14.5 10.125M9.8125 4.8125C9.8125 6.3658 8.5533 7.625 7 7.625C5.4467 7.625 4.1875 6.3658 4.1875 4.8125C4.1875 3.2592 5.4467 2 7 2C8.5533 2 9.8125 3.2592 9.8125 4.8125Z" stroke={simpleOwnership ? "white" : "black"} strokeWidth="1.4" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>

                </div>

                <div
                    className={`smartAcquireFilter ${established ? "selected" : ""} ${isDisabled ? "cursor-not-allowed" : ""}`}
                    onClick={() => {
                        if (isDisabled) return;
                        setEstablished(!established);
                        setAnyButtonPressed(true);
                    }}
                >
                    <div className='safLeft'>
                        {established ? (
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                <path d="M2.5 6H9.5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                        ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                <path d="M6 2.5V9.5M2.5 6H9.5" stroke={primary600} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                        )}

                        <span className={`safTitle text-xs semibold ${established ? "selected" : ""}`}>Established</span>
                        <Tooltip
                            icon={established ? 'dark' : 'normal'}
                            text={!isDisabled ? "Private limited company, incorporated at least 15 years ago" : "Disabled due to an active Incorporation Date filter"}
                        />
                    </div>

                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path
                            d="M4 5.28571H9.5M9.5 5.28571L7.5 7.57143M9.5 5.28571L7.5 3M4 11H12M12 11L10 13.2857M12 11L10 8.71429"
                            stroke={established ? "white" : "black"}
                            strokeWidth="1.4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                    </svg>

                </div>
            </div>
            <div className='loginForgotRow margin24'><button onClick={done} className='text-sm medium gray-700'>Done</button></div>
        </div>
    )

}

export default SmartAcquireSearch;