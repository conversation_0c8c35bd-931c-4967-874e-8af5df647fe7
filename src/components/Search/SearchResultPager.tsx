
import '../../styling/search.css'
import React, { useEffect, useState } from 'react';

interface SearchResultPagerProps {
    currentPage: any,
    pages: any,
    change: any
}

const SearchResultPager: React.FC<SearchResultPagerProps> = ({ currentPage, pages, change }) => {

    // #region CONSTANTS & STATE VARIABLES
    const [page, setPage] = useState(currentPage)

    // #endregion

    useEffect(() => {
        setPage(currentPage)
    }, [currentPage]);

    // #region SHOW COMPONENTS

    const showPageNumbers = () => {

        let compArray: any = []
        var index = 1
        while ((index <= pages && pages < 8) || (index <= pages && index <= 3)) {
            let thisIndex = index

            compArray.push(<div className={`pagerItem ${page === thisIndex ? "selected" : ""}`} onClick={() => change(thisIndex)}>
                {thisIndex}
            </div>)
            index++
        }

        if (pages > 8) {
            compArray.push(<div className={`pagerItem ${page > 3 && page < pages - 2 ? "selected" : ""} noPointer`}>
                ...
            </div>)

            var index2 = pages - 2
            while (index2 <= pages) {
                let thisIndex = index2

                compArray.push(<div className={`pagerItem ${page === thisIndex ? "selected" : ""}`} onClick={() => change(thisIndex)}>
                    {thisIndex}
                </div>)
                index2++
            }
        }

        return compArray
    }

    // #endregion

    // #region WEB REQUESTS

    // #endregion


    // #region BUTTONS CLICKED
    const previous = () => {
        if (currentPage !== 1) {
            change(currentPage - 1)
        }
    }
    const next = () => {
        if (currentPage !== pages) {
            change(currentPage + 1)
        }
    }
    // #endregion


    // #region OTHER

    // #endregion





    return (
        <div className="width100 row jc-between ai-centre">

            <div className={`row jc-between ai-centre gap8 ${currentPage === 1 ? "" : "pointer"}`}>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M15.8327 9.99935H4.16602M4.16602 9.99935L9.99935 15.8327M4.16602 9.99935L9.99935 4.16602" stroke={currentPage === 1 ? "#EAECF0" : "#475467"} strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <span className={`text-sm semibold ${currentPage === 1 ? "gray-200" : "gray-600"}`} onClick={previous}>Previous</span>
            </div>

            <div className='row'>
                {showPageNumbers()}
            </div>

            <div className={`row jc-between ai-centre gap8 ${currentPage === pages ? "" : "pointer"}`} onClick={next}>
                <span className={`text-sm semibold ${currentPage === pages ? "gray-200" : "gray-600"}`}>Next</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M4.16602 9.99935H15.8327M15.8327 9.99935L9.99935 4.16602M15.8327 9.99935L9.99935 15.8327" stroke={currentPage === pages ? "#EAECF0" : "#475467"} strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
            </div>
        </div>
    )

}

export default SearchResultPager;