import { Dict } from "mixpanel-browser";

export enum MixpanelEventName {
  pageView = "Pageview",
  saveCampaignName = "User clicks Save on Campaign name",
  userAddsCollectionToCampaign = "User adds collection to campaign",
  userSetupCustomization = "User sets customization",
  userSavesCustomization = "User saves customization",
  userSavesEmailMessage = "User Saves email message",
  userClicksSaveAndProceedOnCampaign = `User clicks "Save & Proceed" on campaign flow`,
  userClicksStartCampaign = "User Clicks start campaign",
  userClicksBack = `User clicks "Back" on campaign flow `,
  userLeavesCampaign = "User clicks exit from campaign flow ",
  userSelectsGetStarted = `User selects "Get Started"`,
  preferToSkipTrial = "User Clicks Prefer to skip the trial? Subscribe now",
  userSelects7DayFreeTrial = `User selects "Start 7 day free trial"`,
  clicksMonthlyBilling = `User clicks "Monthly billing"`,
  clicksAnnualBilling = `User clicks "Annual billing"`,
  clicksUnlockFullQouta = `User clicks "Unlock full quota"`,
  clicksUnlickFullQuotaFromSaveCollection = `User clicks "Unlock full quota from save collection"`,
  clickUpgradePlan = `User clicks "Upgrade subscription"`,
  clicksCancelSubscription = `User clicks "Cancel subscription"`,
  clicksEndTrialNow = `User clicks "End trial now"`,
  clicksOnTopUpQouta = `User clicks "Top up quota"`,
  searchCampaignName = "User enters campaign in search",
  selectsCampaign = "User selects campaign",
  selectsSaveToCollection = "User Clicks Save to collection",
  userCreatesNewCollection = "User creates new collection",
  userGetsExistingCollectionError = "User gets collection already exists error",
  userUpdatesExistingCollection = "User adds companies existing collection",
  topUpSuccessFull = "User top up for collection success",
  topUpSuccessFullCampaign = "User top up for collection success",
  topUpError = "An error occcured while topup",
  userClicksNewCampaign = "User clicks new campaign",
  userClicksOnDownloadCollection = `User clicks "Download collection"`,
  userClicksSignIn = `User clicks "Sign in"`,
  signInSuccess = `User signs in successfully`,
  signUpSucess = `User signs up successfully`,
  userClicksForgetYourPassword = `User clicks "Forget your password"`,
  userClicksPrivacyPolicyLink = `User clicks "Privacy policy" link`,
  campaignCreationSuccess = `User creates campaign successfully`,
  errorOccuredWhileCreatingCampaign = `An error occured while creating campaign`,
  openedAccountModal = "User Opens account modal",
  snapshotUpdated = "Subscription and qoutas refreshed and updated",
  userClickedOnGetEarlyAccess = `User clicks "Get early access"`,
  addingQuotaInProgressModalShownToUser = `Adding quota in progress modal shown to user`,
  userGotErrorWhileSavingLetterMessage = `User got error while saving letter message`,
  userSavesLetterMessage = `User saves letter message`,
  userClicksWatchTour = `User clicks "Watch the tour"`,
  userGetsStepError = `User gets step error in campaign flow`,
  userClicksOnAddNewDeal = `User clicks on "Add new deal"`,
  userClicksOnDeal = `User clicks on deal`,
  userDealCreatedSuccess = `User creates deal successfully`,
  userDealUpdatedSuccess = `User updates deal successfully`,
  userDealError = `An error occured `,
  userClickedViewCollection = `User clicks "View collection"`,
  userDeletedCollection = `User deleted collection`,
  userGotErrorDeletingCollection = `User got error while deleting collection`,
  userDeletesCompaniesFromCollection = `User deletes companies from collection`,
}

export type CustomEventProps = {
  mixpanelProps: Dict;
  id: string | number;
  eventName: MixpanelEventName;
  propertyName?: string;
};
