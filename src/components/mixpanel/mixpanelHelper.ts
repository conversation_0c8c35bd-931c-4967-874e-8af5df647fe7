export const getCookie = (name: string): string | null => {
  const cookieString = document.cookie;
  const cookies = cookieString.split(";").map((cookie) => cookie.trim());
  const targetCookie = cookies.find((cookie) => cookie.startsWith(`${name}=`));

  if (targetCookie) {
    return targetCookie.split("=")[1];
  }

  // Fallback method
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(";").shift() || null;
  }

  return null;
};

export const getMixpanelDistinctId = (): string => {
  const token = process.env.REACT_APP_MIXPANEL_TOKEN;
  const mixpanelCookieName = `mp_${token}_mixpanel`;
  const mixpanelCookie = getCookie(mixpanelCookieName);

  if (mixpanelCookie) {
    try {
      const cookieData = JSON.parse(decodeURIComponent(mixpanelCookie));

      // First try to get the non-device distinct_id
      if (
        cookieData.distinct_id &&
        !cookieData.distinct_id.startsWith("$device:")
      ) {
        return cookieData.distinct_id;
      }

      // If only device ID is available, return it without the '$device:' prefix
      if (
        cookieData.distinct_id &&
        cookieData.distinct_id.startsWith("$device:")
      ) {
        return cookieData.distinct_id.replace("$device:", "");
      }

      // Fallback to device_id if distinct_id is not available
      if (cookieData.$device_id) {
        return cookieData.$device_id;
      }

      return "";
    } catch (error) {
      console.error("Error parsing Mixpanel cookie:", error);
      return "";
    }
  }

  return "";
};
