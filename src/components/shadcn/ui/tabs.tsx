import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "components/lib/utils";

const Tabs = TabsPrimitive.Root;

const TabsList = React.forwardRef<
    React.ElementRef<typeof TabsPrimitive.List>,
    React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> & {
        isHarbour?: boolean;
    }
>(({ className, isHarbour = false, ...props }, ref) => (
    <TabsPrimitive.List
        ref={ref}
        className={cn(
            "inline-flex h-9 items-center justify-start rounded-lg p-1",
            isHarbour ? "bg-[#3DAEDF]" : "bg-[#F9FAFB]", // Apply Harbour Club background or default background
            className
        )}
        {...props}
    />
));
TabsList.displayName = TabsPrimitive.List.displayName;

const TabsTrigger = React.forwardRef<
    React.ElementRef<typeof TabsPrimitive.Trigger>,
    React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & {
        isHarbour?: boolean;
    }
>(({ className, isHarbour = false, ...props }, ref) => (
    <TabsPrimitive.Trigger
        ref={ref}
        className={cn(
            "rounded-[6px] h-[36px] inline-flex items-center justify-center whitespace-nowrap px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",
            isHarbour
                ? "data-[state=active]:bg-[#B9E6FE] data-[state=active]:text-[#182059]" // Harbour Club colors
                : "data-[state=active]:bg-[#FFF1F3] data-[state=active]:text-primary-700", // Default colors
            className
        )}
        {...props}
    />
));
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = React.forwardRef<
    React.ElementRef<typeof TabsPrimitive.Content>,
    React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content> & {
        isHarbour?: boolean;
    }
>(({ className, isHarbour = false, ...props }, ref) => (
    <TabsPrimitive.Content
        ref={ref}
        className={cn(
            "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            isHarbour
                ? "focus-visible:text-[#182059]"
                : "focus-visible:text-primary-700", // Apply text color based on Harbour Club
            className
        )}
        {...props}
    />
));
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsList, TabsTrigger, TabsContent };
