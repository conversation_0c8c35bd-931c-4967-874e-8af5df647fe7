import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "components/lib/utils";
import { AlertCircle, CheckCircle, Info, XCircle } from "lucide-react";
import SvgCloseIcon from "components/common/iconComponents/closeIcon";

const alertVariants = cva(
  "relative w-full rounded-lg border p-4 flex items-start gap-3 mb-2 h-[fit-content]",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground border-border",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive bg-destructive/10",
        success:
          "border-green-600/50 text-green-700 bg-green-50 dark:border-green-500",
        warning:
          "border-yellow-600/50 text-yellow-700 bg-yellow-50 dark:border-yellow-500",
        info: "p-[16px] gap-[16px] rounded-tl-[12px] border-t-[1px] border-[#FEC84B] opacity-100 bg-[#FFFCF5]",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

interface AlertProps
  extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof alertVariants> {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  showCloseIcon?: boolean;
  onClose?: () => void;
}

const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  (
    {
      className,
      variant,
      title,
      description,
      icon,
      showCloseIcon,
      onClose,
      children,
      ...props
    },
    ref
  ) => {
    const getDefaultIcon = () => {
      if (!icon) {
        switch (variant) {
          case "success":
            return <CheckCircle className="h-5 w-5 text-green-600" />;
          case "destructive":
            return <XCircle className="h-5 w-5 text-destructive" />;
          case "warning":
            return <AlertCircle className="h-5 w-5 text-yellow-600" />;
          case "info":
            return <Info className="h-5 w-5 text-blue-600" />;
          default:
            return null;
        }
      }
      return icon;
    };

    return (
      <div
        ref={ref}
        role="alert"
        className={cn(alertVariants({ variant }), className)}
        {...props}
      >
        {showCloseIcon && onClose && (
          <button
            onClick={onClose}
            className="absolute top-2 right-2 transition-all"
            aria-label="Close"
          >
            <SvgCloseIcon
              className="h-5 w-5"
              color={
                variant === "default"
                  ? "#4B5563"
                  : variant === "destructive"
                    ? "#EF4444"
                    : variant === "success"
                      ? "#059669"
                      : variant === "warning"
                        ? "#D97706"
                        : variant === "info"
                          ? "#2563EB"
                          : "#667085"
              }
            />
          </button>
        )}

        <div className="flex-1 gap-2 flex flex-col">
          <div className="flex flex-row gap-2 items-center justify-center">
            {getDefaultIcon()}
            {title && <AlertTitle>{title}</AlertTitle>}
          </div>
          {description && <AlertDescription>{description}</AlertDescription>}
          {children}
        </div>
      </div>
    );
  }
);
Alert.displayName = "Alert";

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-semibold leading-none tracking-tight", className)}
    {...props}
  >
    {children}
  </h5>
));
AlertTitle.displayName = "AlertTitle";

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
));
AlertDescription.displayName = "AlertDescription";

export { Alert, AlertTitle, AlertDescription };
