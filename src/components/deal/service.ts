import axiosWithToken from "axiosWithToken";
import {
  CreateDealDTO,
  DealDocummentsReponseDTO,
  DealNotesReponseDTO,
  DealResponseDTO,
  DealStageDTO,
  StageDTO,
} from "./types";

const baseURL2 = process.env.REACT_APP_BASEURL2;

export const getDeals = async (): Promise<StageDTO[]> => {
  try {
    const response = await axiosWithToken.get<StageDTO[]>(
      `${baseURL2}api/deals`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching data", error);
    throw error;
  }
};

export const getDealStages = async (): Promise<DealStageDTO[]> => {
  try {
    const response = await axiosWithToken.get<DealStageDTO[]>(
      `${baseURL2}api/deals/stages`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching data", error);
    throw error;
  }
};

export const createDeal = async (
  deal: CreateDealDTO
): Promise<DealResponseDTO> => {
  try {
    const response = await axiosWithToken.post<DealResponseDTO>(
      `${baseURL2}api/deals`,
      deal
    );

    return response.data;
  } catch (error) {
    console.error("Error creating deal", error);
    throw error;
  }
};

export const updateDeal = async (
  dealId: string,
  deal: CreateDealDTO
): Promise<DealResponseDTO> => {
  try {
    const response = await axiosWithToken.patch<DealResponseDTO>(
      `${baseURL2}api/deals/${dealId}/update`,
      deal
    );

    return response.data;
  } catch (error) {
    console.error("Error creating deal", error);
    throw error;
  }
};

export const moveDeal = async (dealId: string, dealStageId: string) => {
  try {
    const response = await axiosWithToken.put(
      `${baseURL2}api/deals/${dealId}/move`,
      { dealStageId }
    );
    return response.data;
  } catch (error) {
    console.error("Error moving deal", error);
    throw error;
  }
};

export const positionDeal = async (dealId: string, position: number) => {
  try {
    const response = await axiosWithToken.put(
      `${baseURL2}api/deals/${dealId}/position`,
      { position }
    );
    return response.data;
  } catch (error) {
    console.error("Error positioning deal", error);
    throw error;
  }
};

export const getDealById = async (dealId: string) => {
  try {
    const response = await axiosWithToken.get(`${baseURL2}api/deals/${dealId}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching deal by ID", error);
    throw error;
  }
};

export const getAllDealDocuments = async (dealId: string) => {
  try {
    const response = await axiosWithToken.get<DealDocummentsReponseDTO[]>(
      `${baseURL2}api/deals/${dealId}/documents`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching deal documents", error);
    throw error;
  }
};

export const uploadDealDocument = async (dealId: string, file: File) => {
  try {
    const formData = new FormData();
    formData.append("file", file);

    const response = await axiosWithToken.post(
      `${baseURL2}api/deals/${dealId}/documents`,
      formData
    );

    return response.data;
  } catch (error) {
    console.error("Error uploading deal document", error);
    throw error;
  }
};

export const saveNotes = async (dealId: string, note: string) => {
  try {
    const response = await axiosWithToken.post(
      `${baseURL2}api/deals/${dealId}/notes`,
      { note: note }
    );
    return response.data;
  } catch (error) {
    console.error("Error positioning deal", error);
    throw error;
  }
};

export const deleteDocument = async (dealId: string, documentId: string) => {
  try {
    const response = await axiosWithToken.delete(
      `${baseURL2}api/deals/${dealId}/documents/${documentId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting document", error);
    throw error;
  }
};

export const getPreSignedUrlForDealDocument = async (
  dealId: string,
  documentId: string
) => {
  try {
    const response = await axiosWithToken.get(
      `${baseURL2}api/deals/${dealId}/documents/${documentId}/signed-url`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching pre-signed URL", error);
    throw error;
  }
};

export const getAllDealNotes = async (dealId: string) => {
  try {
    const response = await axiosWithToken.get<DealNotesReponseDTO[]>(
      `${baseURL2}api/deals/${dealId}/notes`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching deal notes", error);
    throw error;
  }
};

export const deleteNote = async (dealId: string, noteId: string) => {
  try {
    const response = await axiosWithToken.delete(
      `${baseURL2}api/deals/${dealId}/notes/${noteId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting note", error);
    throw error;
  }
};

export const updateNote = async (
  dealId: string,
  noteId: string,
  note: string
) => {
  try {
    const response = await axiosWithToken.patch(
      `${baseURL2}api/deals/${dealId}/notes/${noteId}`,
      { note: note }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating note", error);
    throw error;
  }
};

export const getAllDealOffers = async (dealId: string) => {
  try {
    const response = await axiosWithToken.get(
      `${baseURL2}api/deals/${dealId}/offers`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching deal offers", error);
    throw error;
  }
};

export const createDealOffer = async (
  dealId: string,
  payload: {
    offer: number;
    status: string;
  }
) => {
  try {
    const response = await axiosWithToken.post(
      `${baseURL2}api/deals/${dealId}/offers`,
      { offer: payload.offer, status: payload.status }
    );
    return response.data;
  } catch (error) {
    console.error("Error creating deal offer", error);
    throw error;
  }
};

export const updateDealOffer = async (
  dealId: string,
  offerId: string,
  offer: number,
  status: string
) => {
  try {
    const response = await axiosWithToken.patch(
      `${baseURL2}api/deals/${dealId}/offers/${offerId}`,
      { offer: offer, status: status }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating deal offer", error);
    throw error;
  }
};

export const deleteDealOffer = async (dealId: string, offerId: string) => {
  try {
    const response = await axiosWithToken.delete(
      `${baseURL2}api/deals/${dealId}/offers/${offerId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting deal offer", error);
    throw error;
  }
};

export const callNotifyAutomation = async (dealId: string) => {
  try {
    const response = await axiosWithToken.post(
      `${baseURL2}api/deals/${dealId}/notify-automation`
    );
    return response.data;
  } catch (error) {
    console.error("Error calling notify automation", error);
    throw error;
  }
};
