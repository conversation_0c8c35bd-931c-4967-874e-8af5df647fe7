import { DndContext, closestCorners, DragOverlay } from "@dnd-kit/core";
import { ArrowBigLeft, ArrowBigRight, CirclePlus, RefreshCw } from "lucide-react";
import { useDealTracker } from "./useDealTracker";
import { But<PERSON> } from "components/shadcn/ui/button";
import { Column } from "./column";
import { Task } from "./taskItem";
import { Alert, AlertDescription, AlertTitle } from "components/shadcn/ui/alert";
import Loader from "components/common/loader";
import { TaskDialog } from "./task-dialog/taskDialog";
import { useEffect, useRef, useState } from "react";
import confetti from "canvas-confetti";

export const DealTracker = () => {
    const {
        columns,
        activeId,
        dialogOpen,
        formData,
        isLoading,
        error,
        dealSuccess,
        dealError,
        isUpdatingDealRef,
        dealErrorMessage,
        totalDeals,
        setDialogOpen,
        setFormData,
        addTask,
        handleDragStart,
        handleDragEnd,
        handleTaskClick,
        handleSaveTask,
        getActiveTask,
        refreshData,
        handleNewDeal,
    } = useDealTracker();

    const activeTask = getActiveTask();
    const columnEntries = Object.entries(columns);

    const scrollRef = useRef<HTMLDivElement>(null);
    const [isAtStart, setIsAtStart] = useState(true);
    const [isAtEnd, setIsAtEnd] = useState(false);

    const updateScrollButtons = () => {
        const el = scrollRef.current;
        if (!el) return;

        setIsAtStart(el.scrollLeft <= 0);
        setIsAtEnd(el.scrollLeft + el.clientWidth >= el.scrollWidth - 5); // allow small margin
    };

    const scrollLeft = () => {
        scrollRef.current?.scrollBy({ left: -300, behavior: "smooth" });
    };

    const scrollRight = () => {
        scrollRef.current?.scrollBy({ left: 300, behavior: "smooth" });
    };

    useEffect(() => {
        const el = scrollRef.current;
        if (!el) return;

        updateScrollButtons();
        el.addEventListener("scroll", updateScrollButtons);

        return () => {
            el.removeEventListener("scroll", updateScrollButtons);
        };
    }, [columns]);


    useEffect(() => {
        if (!isUpdatingDealRef.current && totalDeals === 1) {
            const duration = 5000;
            const interval = 250;
            const end = Date.now() + duration;

            const randomInRange = (min: number, max: number) => {
                return Math.random() * (max - min) + min;
            };

            const intervalId = setInterval(() => {
                if (Date.now() > end) {
                    clearInterval(intervalId);
                    return;
                }

                confetti({
                    particleCount: 20,
                    angle: randomInRange(0, 360),
                    spread: 360,
                    origin: {
                        x: Math.random(),
                        y: Math.random(),
                    },
                    shapes: ["circle", "square", "star", "triangle", "heart", "diamond"] as any,
                    gravity: 0.8,
                    colors: ["#ff0000", "#800080", "#8A2BE2", "#0000FF"],
                    scalar: 1.4,
                });
            }, interval);

            return () => clearInterval(intervalId);
        }
    }, [totalDeals, isUpdatingDealRef]);

    if (error) {
        return (
            <div className="fullScreen">
                <div className="container">
                    <Alert variant="destructive" className="my-8">
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                    <div className="flex justify-center">
                        <Button onClick={refreshData} variant="outline">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Try Again
                        </Button>
                    </div>
                </div>
            </div>
        );
    }



    return (
        <div className="fullScreen">
            <div className="container">
                <div className="savedFiltersScreen pb-0">
                    <div className="flex flex-col gap-5 justify-between w-full items-center">
                        <div className="mx-auto w-full">
                            <div className="sfTitles mb-8 text-left flex flex-row justify-between">
                                <div>
                                    <h1 className="text-3xl font-bold">Deal Tracker</h1>
                                    <p className="text-md regular gray-600">
                                        Manage and track your deals through different stages
                                    </p>
                                </div>
                                {isLoading && <Loader />}
                                <Button variant="primary" size="sm" onClick={handleNewDeal}>
                                    <CirclePlus className="h-4 w-4 mr-2" />
                                    Add New Deal
                                </Button>
                            </div>
                        </div>
                    </div>

                    {dealSuccess && (
                        <Alert
                            variant="success"
                            title={`Deal ${isUpdatingDealRef.current ? "updated" : "created"} successfully!`}
                        />
                    )}
                    {dealError && (
                        <Alert
                            variant="destructive"
                            title="An Error occurred, Please try again later"
                            description={dealErrorMessage ?? ""}
                        />
                    )}

                    <DndContext
                        collisionDetection={closestCorners}
                        onDragStart={handleDragStart}
                        onDragEnd={handleDragEnd}
                    >
                        <div className="relative w-full flex items-center h-[65vh]">
                            <button
                                onClick={scrollLeft}
                                className={`p-2 rounded-full shadow bg-gray-200 flex items-center justify-center transition-colors ${isAtStart ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-100"
                                    }`} aria-label="Scroll Left"
                                disabled={isAtStart}
                            >
                                <ArrowBigLeft className="w-6 h-6 text-primary-700" />
                            </button>

                            <div
                                className="overflow-x-auto w-full min-h-full h-full mx-4"
                                ref={scrollRef}
                            >
                                <div className="flex gap-6 h-full min-h-full px-2 pb-8 pt-2 w-max">
                                    {columnEntries.map(([columnId, column]) => (
                                        <Column
                                            key={columnId}
                                            columnId={columnId}
                                            column={column}
                                            columnsPerPage={4}
                                            onTaskClick={handleTaskClick}
                                            setFormData={setFormData}
                                            setDialogOpen={setDialogOpen}
                                            addTask={(columnId, task) => addTask(false, columnId, task)}
                                        />
                                    ))}
                                </div>
                            </div>

                            <button
                                onClick={scrollRight}
                                className={`p-2 rounded-full shadow bg-gray-200 flex items-center justify-center transition-colors ${isAtEnd ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-100"
                                    }`} aria-label="Scroll Right"
                                disabled={isAtEnd}
                            >
                                <ArrowBigRight className="w-6 h-6 text-primary-700" />
                            </button>
                        </div>

                        <DragOverlay>
                            {activeId && activeTask && (
                                <div className="rotate-3 scale-105">
                                    <Task
                                        task={activeTask}
                                        onClick={handleTaskClick}
                                        isDragging={true}
                                    />
                                </div>
                            )}
                        </DragOverlay>
                    </DndContext>

                    <TaskDialog
                        open={dialogOpen}
                        onOpenChange={setDialogOpen}
                        formData={formData}
                        setFormData={setFormData}
                        onSave={(isEditMode, data) => handleSaveTask(isEditMode, data)}
                        columns={columns}
                        isLoading={isLoading}
                    />
                </div>
            </div>
        </div>
    );
};

export default DealTracker;
