import type React from "react";

import { useState } from "react";

export const useDragDetection = () => {
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  const handlePointerDown = (e: React.PointerEvent) => {
    setStartPosition({ x: e.clientX, y: e.clientY });
    setIsDragging(false);
  };

  const handlePointerMove = (e: React.PointerEvent) => {
    const moveThreshold = 5;
    if (
      Math.abs(e.clientX - startPosition.x) > moveThreshold ||
      Math.abs(e.clientY - startPosition.y) > moveThreshold
    ) {
      setIsDragging(true);
    }
  };

  const isClick = () => !isDragging;

  return {
    handlePointerDown,
    handlePointerMove,
    isClick,
  };
};
