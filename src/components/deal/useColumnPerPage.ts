import { useEffect, useState } from "react";

export const useColumnsPerPage = () => {
  const [columnsPerPage, setColumnsPerPage] = useState(3);

  useEffect(() => {
    const update = () => {
      const width = window.innerWidth;
      if (width >= 1280) setColumnsPerPage(4);
      else if (width >= 768) setColumnsPerPage(3);
      else setColumnsPerPage(1);
    };

    update();
    window.addEventListener("resize", update);
    return () => window.removeEventListener("resize", update);
  }, []);

  return columnsPerPage;
};
