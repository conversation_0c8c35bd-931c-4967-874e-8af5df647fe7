import type React from "react";

export interface DealTask {
  id: string;
  dealStageId: string;
  companyName: string;
  companyNumber: string;
  dealStage: DealStage;
  virtualDataRoomUrl?: string;
  website?: string;
  contactName?: string;
  email?: string;
  phoneNumber?: string;
  country?: string;
  isImportant?: boolean;
  currency?: string;
  askingPrice: number;
  spaDraftOn?: string;
  estimatedValuation: number;
  isOfferAccepted?: boolean;
  isHotAgreed?: boolean;
  dueDiligenceBeganOn?: string;
  dueDiligenceConclusionOn?: string;
  isSpaAgreed?: boolean;
  isSignedByBuyer?: boolean;
  isSignedBySeller?: boolean;
  finalValuationAgreed?: number;
  finalValuationAgreedOn?: string;
  accountStatus?: string;
  completionStatus?: string;
  iban?: string;
  isDualAuthorized?: boolean;
  yourOffer?: number;
  headline?: string;
  latestSpaUrl?: string;
  totalDocuments?: number;
  notes?: Note[];
  documents?: Documents[];
  note?: string;
  offerStatus?: OFFER_STATUS;
}

export interface Column {
  title: string;
  name: string;
  position: number;
  deals: DealTask[];
}

export interface Columns {
  [key: string]: Column;
}

export interface TaskProps {
  task: DealTask;
  isDragging?: boolean;
  onClick: (task: DealTask) => void;
}

export interface DroppableProps {
  id: string;
  children: React.ReactNode;
}

export interface ColumnProps {
  columnId: string;
  column: Column;
  addTask: (columnId: string, task: DealTask) => void;
  onTaskClick: (task: any) => void;
  setFormData: (data: any) => void;
  setDialogOpen: (open: boolean) => void;
}

export interface DealDTO {
  id: string;
  dealStageId: string;
  companyName: string;
  companyNumber: string;
  currency: string;
  notes: string;
  isImportant: number;
  position: number;
  createdAt: string;
  updatedAt: string;
  country: string;
  website: string;
  contactName: string;
  email: string;
  phoneNumber: string;
}

export interface StageDTO {
  id: string;
  name: string;
  position: number;
  deals: DealDTO[];
}

export interface DealStageDTO {
  id: string;
  name: string;
  position: number;
}
export interface CreateDealDTO {
  dealStageId: string;
  companyName: string;
  companyNumber: string;
  website: string;
  contactName: string;
  email: string;
  phoneNumber: string;
  country: string;
  isImportant: boolean;
  currency: string;
  askingPrice: number;
  estimatedValuation: number;
  isOfferAccepted: boolean;
  isHotAgreed: boolean;
  dueDiligenceBeganOn: string; // ISO date string
  dueDiligenceConclusionOn: string; // ISO date string
  spaDraftOn: string; // ISO date string
  isSpaAgreed: boolean;
  isSignedByBuyer: boolean;
  isSignedBySeller: boolean;
  latestSpaUrl: string;
  finalValuationAgreed: number;
  finalValuationAgreedOn: string; // ISO date string
  accountStatus: string;
  completionStatus: string;
  iban: string;
  isDualAuthorized: boolean;
  note?: string;
  offerStatus?: OFFER_STATUS;
  virtualDataRoomUrl?: string;
}
export interface StageComponentProps {
  formData: DealTask;
  setFormData: (data: DealTask) => void;
  isEditing?: boolean;
  showError?: any;
  errors?: any;
  handleBlur?: any;
}

type Note = {
  id: string;
  note: string;
  createdBy: string;
};

type Documents = {
  id: string;
  path: string;
  uploadedBy: string;
};

export type DealStage = {
  createdAt: string;
  description: null;
  id: string;
  isActive: boolean;
  name: string;
  position: number;
  updatedAt: string;
};

export interface DealResponseDTO {
  id: string;
  dealStageId: string;
  companyName: string;
  companyNumber: string;
  isImportant: boolean;
  position: number;
  createdAt: string;
  updatedAt: string;
}

export interface DealDocummentsReponseDTO {
  id: string;
  fileName: string;
  uploadedBy: string;
  createdAt: string;
}

export interface DealNotesReponseDTO {
  id: string;
  note: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export enum OFFER_STATUS {
  PENDING = "Pending",
  ACCEPTED = "Accepted",
  REJECTED = "Rejected",
}

export enum CURRENCY {
  GBP = "GBP",
  USD = "USD",
  EUR = "EUR",
}

export const defaultDealTask: DealTask = {
  id: "",
  dealStageId: "",
  companyName: "",
  companyNumber: "",
  website: undefined,
  dealStage: null as unknown as DealStage,
  contactName: undefined,
  email: undefined,
  phoneNumber: undefined,
  country: "United Kingdom",
  isImportant: undefined,
  currency: undefined,
  askingPrice: 0,
  estimatedValuation: 0,
  isOfferAccepted: undefined,
  isHotAgreed: undefined,
  dueDiligenceBeganOn: undefined,
  dueDiligenceConclusionOn: undefined,
  spaDraftOn: undefined,
  isSpaAgreed: undefined,
  isSignedByBuyer: undefined,
  isSignedBySeller: undefined,
  finalValuationAgreed: 0,
  finalValuationAgreedOn: undefined,
  accountStatus: undefined,
  completionStatus: undefined,
  iban: undefined,
  isDualAuthorized: undefined,
  offerStatus: undefined,
  note: undefined,
};
