import type React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Grip, Building, ArrowRight, Star } from "lucide-react";
import { motion } from "framer-motion";
import { TaskProps } from "./types";
import { useDragDetection } from "./useDragDetection";

export const Task: React.FC<TaskProps> = ({ task, isDragging, onClick }) => {
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: task.id });
    const { handlePointerDown, handlePointerMove, isClick } = useDragDetection();

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    const handlePointerUp = () => {
        if (isClick()) {
            onClick(task);
        }
    };

    return (
        <motion.div
            ref={setNodeRef}
            style={style}
            className={`group relative rounded-xl border bg-white border-slate-200 p-3 shadow-sm transition-all duration-200
                hover:shadow-md hover:border-purple-200 ${isDragging ? "z-50 scale-105 shadow-lg rotate-2" : ""}
                min-w-[200px] max-w-[300px] md:min-w-[250px] md:max-w-[350px]`}
            onPointerDown={handlePointerDown}
            onPointerMove={handlePointerMove}
            onPointerUp={handlePointerUp}
            {...attributes}
            {...listeners}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            whileHover={{ y: -2 }}
        >
            <div className="absolute left-2 2 p-1 rounded-md hover:bg-slate-100 cursor-grab active:cursor-grabbing z-10">
                <Grip className="h-4 w-4 text-slate-400 group-hover:text-slate-600" />
            </div>

            <div className="flex flex-col ml-6">

                <div className="flex items-start justify-between gap-2 mb-2">
                    <div className="flex items-start gap-2 flex-grow min-w-0">
                        <Building className="h-4 w-4 text-slate-800 shrink-0 mt-0.5" />
                        <span className=" text-left text-sm text-slate-800 font-medium break-words flex-grow min-w-0">
                            {task.companyName}
                        </span>
                    </div>

                    {Boolean(task.isImportant) && (
                        <div className="flex-shrink-0 ml-2 mt-0.5">
                            <Star className="h-4 w-4 text-amber-500" fill="#f59e0b" />
                        </div>
                    )}
                </div>

                <div className="flex justify-end border-t border-dashed border-slate-100 pt-2 mt-2">
                    <button
                        className="text-xs flex items-center text-slate-800 hover:text-purple-800 font-medium"
                        onClick={(e) => {
                            e.stopPropagation();
                            onClick(task);
                        }}
                    >
                        Details <ArrowRight className="h-3 w-3 ml-1" />
                    </button>
                </div>
            </div>
        </motion.div>
    );
};