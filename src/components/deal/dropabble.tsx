import React from "react";
import { useDroppable } from "@dnd-kit/core";

interface DroppableProps {
    id: string; // Unique column ID
    children: React.ReactNode; // The column contents
}

export const Droppable: React.FC<DroppableProps> = ({ id, children }) => {
    const { setNodeRef } = useDroppable({ id }); // Register this as a drop zone

    return (
        <div ref={setNodeRef} style={{ minHeight: "200px", padding: "10px" }}>
            {children}
        </div>
    );
};
