import type React from "react"
import { AccordionItem, AccordionTrigger, AccordionContent } from "components/shadcn/ui/accordion"
import { Label } from "components/shadcn/ui/label"
import { Input } from "components/shadcn/ui/input"
import { Checkbox } from "components/shadcn/ui/checkbox"
import { StageComponentProps } from "../types"

export const InterestSection: React.FC<StageComponentProps> = ({ formData, setFormData }) => {
    return (
        <AccordionItem value="interest">
            <AccordionTrigger className="[&[data-state=open]]:bg-gray-200">Interest</AccordionTrigger>
            <AccordionContent className="ml-3 space-y-6">
                <div className="flex items-center space-x-2">
                    <Checkbox
                        id="important"
                        checked={!!formData.isImportant}
                        onCheckedChange={(checked) => setFormData({ ...formData, isImportant: !!checked })}
                    />
                    <Label htmlFor="important" className="flex items-center gap-2 text-slate-700">
                        Mark As Important?
                    </Label>
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="headline" className="text-slate-700">
                        Headline (formerly Card Title / Action Note)
                    </Label>
                    <Input
                        id="headline"
                        value={formData.headline || ""}
                        onChange={(e) => setFormData({ ...formData, headline: e.target.value })}
                        placeholder="Enter headline"
                    />
                </div>
            </AccordionContent>
        </AccordionItem>
    )
}
