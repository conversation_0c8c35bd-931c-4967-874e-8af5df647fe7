import { useState, useEffect, useCallback } from "react";
import { useSelector } from "react-redux";
import { DealTask, DealStageDTO } from "../types";
import { selectDealStages } from "../dealTrackerSlice";
import {
  isFormValid,
  TaskFormErrors,
  validateTaskForm,
} from "./taskValidation";
import { cleanFormData } from "./taskUtils";
import lang from "lang";

interface UseTaskFormProps {
  initialFormData: DealTask;
  onSave: (isEditMode: boolean, data: DealTask) => Promise<void>;
  onClose: () => void;
  isOpen: boolean;
}

export const useTaskForm = ({
  initialFormData,
  onSave,
  onClose,
  isOpen,
}: UseTaskFormProps) => {
  const { dealTracker: dealTrackerCopy } = lang;

  const stages = useSelector(selectDealStages);
  const [formData, setFormData] = useState<DealTask>(initialFormData);
  const [errors, setErrors] = useState<TaskFormErrors>({});
  const [touched, setTouched] = useState<{
    [key in keyof TaskFormErrors]?: boolean;
  }>({});
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const getAllStages = stages?.map((stage: DealStageDTO) => ({
    name: stage.name,
    dealStageId: stage.id,
  }));

  const validate = useCallback(() => {
    const newErrors = validateTaskForm(formData);
    setErrors(newErrors);
    return isFormValid(newErrors);
  }, [formData]);

  useEffect(() => {
    setFormData(initialFormData);
    if (
      isOpen &&
      initialFormData.id &&
      initialFormData.dealStage &&
      !initialFormData.dealStageId
    ) {
      setFormData((prev) => ({
        ...prev,
        dealStageId: initialFormData.dealStage?.id || "",
      }));
    }
  }, [isOpen, initialFormData]);

  useEffect(() => {
    if (isOpen) {
      setErrors({});
      setTouched({});
      setSubmitted(false);
      validate();
    }
  }, [isOpen, validate]);

  useEffect(() => {
    validate();
  }, [formData, validate]);

  const handleChange = useCallback((field: keyof DealTask, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  }, []);

  const handleBlur = useCallback((fieldName: keyof TaskFormErrors) => {
    setTouched((prev) => ({ ...prev, [fieldName]: true }));
  }, []);

  const handlePhoneNumberChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      let newValue = inputValue;

      if (inputValue.length > 0 && !inputValue.startsWith("+")) {
        if (
          (!formData.phoneNumber || formData.phoneNumber === "") &&
          inputValue.match(/^[0-9]/)
        ) {
          newValue = "+44" + inputValue;
        } else if (
          formData.phoneNumber &&
          !formData.phoneNumber.startsWith("+") &&
          inputValue.startsWith("+")
        ) {
          newValue = inputValue;
        }
      } else if (inputValue === "+") {
        newValue = "+";
      } else if (
        inputValue.startsWith("+") &&
        formData.phoneNumber &&
        formData.phoneNumber.startsWith("+44")
      ) {
        if (!inputValue.startsWith("+44")) {
          newValue = inputValue;
        }
      } else if (inputValue === "") {
        newValue = "";
      }

      handleChange("phoneNumber", newValue);
    },
    [formData.phoneNumber, handleChange]
  );

  const handleSubmit = async () => {
    setSubmitted(true);
    const isValid = validate();
    if (isValid) {
      setIsLoading(true);
      const dataToSave = cleanFormData(formData);
      try {
        await onSave(isEditMode, dataToSave);
        onClose();
      } catch (error) {
        console.error("Error saving deal:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const showError = (fieldName: keyof TaskFormErrors) => {
    return (touched[fieldName] || submitted) && errors[fieldName];
  };

  const isEditMode = !!formData.id;
  const formIsValid = isFormValid(errors);

  return {
    formData,
    errors,
    touched,
    submitted,
    isLoading,
    getAllStages,
    isEditMode,
    formIsValid,
    dealTrackerCopy,
    setFormData,
    handleChange,
    handleBlur,
    handlePhoneNumberChange,
    handleSubmit,
    showError,
  };
};
