import { useState } from "react";
import { useDispatch } from "react-redux";

import {
  createDealOffer,
  deleteDealOffer,
  getAllDealOffers,
  updateDealOffer,
} from "components/deal/service";
import { setDealOffers } from "components/deal/dealTrackerSlice";

export const useOfferSection = (dealId: string) => {
  const [loadingMap, setLoadingMap] = useState<{ [key: string]: boolean }>({});
  const dispatch = useDispatch();

  const setOfferLoading = (id: string, isLoading: boolean) => {
    setLoadingMap((prev) => ({ ...prev, [id]: isLoading }));
  };

  const refreshOffers = async () => {
    try {
      const offers = await getAllDealOffers(dealId);
      dispatch(setDealOffers(offers));
    } catch (error) {
      console.error("Error fetching deal offers:", error);
    }
  };

  const handleCreateOffer = async (payload: {
    offer: number;
    status: string;
  }) => {
    setOfferLoading("new", true);
    try {
      await createDealOffer(dealId, payload);
      await refreshOffers();
    } catch (error) {
      console.error("Error creating offer:", error);
    } finally {
      setOfferLoading("new", false);
    }
  };

  const handleUpdateOffer = async (
    id: string,
    payload: { offer: number; status: string }
  ) => {
    setOfferLoading(id, true);
    try {
      await updateDealOffer(dealId, id, payload.offer, payload.status);
      await refreshOffers();
    } catch (error) {
      console.error("Error updating offer:", error);
    } finally {
      setOfferLoading(id, false);
    }
  };

  const handleDeleteOffer = async (id: string) => {
    setOfferLoading(id, true);
    try {
      await deleteDealOffer(dealId, id);
      await refreshOffers();
    } catch (error) {
      console.error("Error deleting offer:", error);
    } finally {
      setOfferLoading(id, false);
    }
  };

  return {
    handleCreateOffer,
    handleUpdateOffer,
    handleDeleteOffer,
    loadingMap,
  };
};
