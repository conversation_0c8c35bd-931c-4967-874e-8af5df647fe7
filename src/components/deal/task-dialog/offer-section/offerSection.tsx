import type React from "react";
import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>cil, Trash2, Loader2 } from "lucide-react";
import {
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from "components/shadcn/ui/accordion";
import { Label } from "components/shadcn/ui/label";
import { Input } from "components/shadcn/ui/input";
import { Button } from "components/shadcn/ui/button";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "components/shadcn/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "components/shadcn/ui/select";
import { useOfferSection } from "./useOfferSection";
import { useSelector } from "react-redux";
import { selectDealOffers } from "components/deal/dealTrackerSlice";
import { OFFER_STATUS, StageComponentProps } from "components/deal/types";
import { Alert } from "components/shadcn/ui/alert";
import { formatNumber } from "helpers";

export const OfferSection: React.FC<StageComponentProps> = ({
    formData,
    setFormData,
    isEditing,
}) => {
    const getAllOffers = useSelector(selectDealOffers);
    const [newOfferStatus, setNewOfferStatus] = useState<string | undefined>();
    const [editOfferId, setEditOfferId] = useState<string | null>(null);
    const [editOfferValue, setEditOfferValue] = useState("");
    const [editStatus, setEditStatus] = useState("");
    const [displayValue, setDisplayValue] = useState("");

    const [alertMessage, setAlertMessage] = useState<{
        type: "success" | "destructive";
        message: string;
    } | null>(null);

    const { handleCreateOffer, handleUpdateOffer, handleDeleteOffer, loadingMap } =
        useOfferSection(formData.id);

    useEffect(() => {
        if (alertMessage) {
            const timer = setTimeout(() => setAlertMessage(null), 3000);
            return () => clearTimeout(timer);
        }
    }, [alertMessage]);

    const handleSaveOffer = () => {
        if (!formData.yourOffer || isNaN(Number(formData.yourOffer))) return;
        handleCreateOffer({
            offer: Number(formData.yourOffer),
            status: newOfferStatus || "Pending",
        });
    };

    const handleEdit = (id: string, offer: number, status: string) => {
        setEditOfferId(id);
        setEditOfferValue(String(offer));
        setEditStatus(status);
    };

    const handleUpdate = () => {
        if (!editOfferId || isNaN(Number(editOfferValue))) return;
        handleUpdateOffer(editOfferId, {
            offer: Number(editOfferValue),
            status: editStatus,
        });
        setEditOfferId(null);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const rawValue = e.target.value.replace(/,/g, ""); // remove commas
        const floatVal = parseFloat(rawValue);

        if (!isNaN(floatVal)) {
            setFormData({
                ...formData,
                yourOffer: floatVal,
            });
            setDisplayValue(formatNumber(rawValue));
        } else {
            setFormData({
                ...formData,
                yourOffer: 0,
            });
            setDisplayValue("");
        }
    };

    // const handleSendNonBindingLoI = async () => {
    //     try {
    //         await callNotifyAutomation(formData.id);
    //         setAlertMessage({
    //             type: "success",
    //             message: "Non-Binding Letter of Intent sent successfully!",
    //         });
    //     } catch (error) {
    //         console.error("Error sending LoI:", error);
    //         setAlertMessage({
    //             type: "destructive",
    //             message: "Failed to send Non-Binding Letter of Intent. Please try again.",
    //         });
    //     }
    // };

    return (
        <AccordionItem value="offer">
            <AccordionTrigger className="[&[data-state=open]]:bg-gray-200">
                Offer
            </AccordionTrigger>
            <AccordionContent className="ml-3 space-y-6">
                {alertMessage && (
                    <Alert variant={alertMessage.type} title={alertMessage.message} />
                )}
                <div className="text-sm font-normal">Manage your record of Offer History for this Deal here
                </div>
                <div className="flex flex-col gap-6 md:flex-row md:items-end">
                    <div className="flex flex-col gap-2 w-full md:w-1/3">
                        <Label htmlFor="yourOffer" className="text-slate-700">
                            Your Offer
                        </Label>
                        <div className="flex flex-row items-center gap-2">
                            <div className="text-sm font-semibold  text-gray-500">{formData.currency}</div>

                            <Input
                                id="yourOffer"
                                type="text"
                                inputMode="decimal"
                                value={displayValue}
                                onChange={handleChange}
                                placeholder="Enter your offer"
                            />
                        </div>
                    </div>

                    <div className="flex flex-col gap-2 w-full md:w-1/3">
                        <Label className="text-slate-700">Status</Label>
                        <Select
                            value={newOfferStatus}
                            onValueChange={(value) => {
                                setNewOfferStatus(value);
                                setFormData({
                                    ...formData,
                                    offerStatus: value as OFFER_STATUS,
                                });
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent className="z-9999">
                                <SelectItem value="Pending">Pending</SelectItem>
                                <SelectItem value="Accepted">Accepted</SelectItem>
                                <SelectItem value="Rejected">Rejected</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {isEditing && (
                        <div className="w-full md:w-1/3 flex items-end">
                            <Button
                                className="w-full"
                                variant="primary"
                                onClick={handleSaveOffer}
                                disabled={loadingMap["new"]}
                            >
                                {loadingMap["new"] ? (
                                    <Loader2 className="animate-spin w-4 h-4 mr-2" />
                                ) : (
                                    <Save className="h-4 w-4 mr-2" />
                                )}
                                Save Offer
                            </Button>
                        </div>
                    )}
                    {/* <Button
                        className="w-[50%]"
                        variant="outline"
                        onClick={handleSendNonBindingLoI}
                        disabled={loading}
                    >
                        {loading ? (
                            <Loader2 className="animate-spin w-4 h-4 mr-2" />
                        ) : (
                            <Send className="h-4 w-4 mr-2" />
                        )}
                        Send Non-Binding Letter of Intent
                    </Button> */}
                </div>

                {!isEditing && (
                    <div className="text-sm text-muted-foreground mt-4">
                        Your offer details will be automatically saved once you create a deal
                    </div>
                )}


                {/* <div className="flex items-center gap-6">
                    <Label className="text-slate-700">Offer Accepted?</Label>
                    <RadioGroup
                        value={
                            formData?.isOfferAccepted !== undefined
                                ? String(formData.isOfferAccepted)
                                : ""
                        }
                        onValueChange={(value) =>
                            setFormData({ ...formData, isOfferAccepted: value === "true" })
                        }
                        className="flex flex-row gap-6"
                    >
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="true" id="accepted-yes" />
                            <Label htmlFor="accepted-yes">Yes</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="false" id="accepted-no" />
                            <Label htmlFor="accepted-no">No</Label>
                        </div>
                    </RadioGroup>
                </div> */}

                <div className="grid gap-4">
                    <Label className="text-slate-700">Offer History</Label>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Offer Date</TableHead>
                                <TableHead>Offer Value</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody className="">
                            {getAllOffers?.length ? (
                                getAllOffers.map((offer) => (
                                    <TableRow key={offer.id}>
                                        <TableCell>
                                            {new Date(offer.createdAt).toLocaleDateString("en-GB")}
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-row gap-2 items-center">

                                                <div className="text-sm font-semibold  text-gray-500">
                                                    {formData.currency}
                                                </div>
                                                {editOfferId === offer.id ? (

                                                    <Input
                                                        value={editOfferValue}
                                                        onChange={(e) => setEditOfferValue(e.target.value)}
                                                    />
                                                ) : (
                                                    `${offer.offer.toLocaleString()}`
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            {editOfferId === offer.id ? (
                                                <Select
                                                    value={editStatus}
                                                    onValueChange={setEditStatus}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent className="z-9999">
                                                        <SelectItem value="Pending">Pending</SelectItem>
                                                        <SelectItem value="Accepted">Accepted</SelectItem>
                                                        <SelectItem value="Rejected">Rejected</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            ) : (
                                                offer.status
                                            )}
                                        </TableCell>
                                        <TableCell className="flex gap-2">
                                            {editOfferId === offer.id ? (
                                                <Button
                                                    variant="primary"
                                                    size="sm"
                                                    onClick={handleUpdate}
                                                    disabled={loadingMap[editOfferId || ""]}
                                                >
                                                    {loadingMap[editOfferId || ""] ? (
                                                        <Loader2 className="animate-spin w-4 h-4" />
                                                    ) : (
                                                        "Save"
                                                    )}
                                                </Button>
                                            ) : (
                                                <Button
                                                    size="icon"
                                                    variant="ghost"
                                                    onClick={() =>
                                                        handleEdit(offer.id, offer.offer, offer.status)
                                                    }
                                                >
                                                    <Pencil className="h-4 w-4" />
                                                </Button>
                                            )}
                                            <Button
                                                size="icon"
                                                variant="ghost"
                                                onClick={() => handleDeleteOffer(offer.id)}
                                                disabled={loadingMap[offer.id]}
                                            >
                                                {loadingMap[offer.id] ? (
                                                    <Loader2 className="animate-spin w-4 h-4" />
                                                ) : (
                                                    <Trash2 className="h-4 w-4 text-red-500" />
                                                )}
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell className="text-muted-foreground">
                                        No offers yet
                                    </TableCell>
                                    <TableCell />
                                    <TableCell />
                                    <TableCell />
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
            </AccordionContent>
        </AccordionItem >
    );
};
