import type React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Trash2, EyeI<PERSON> } from "lucide-react";
import {
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from "components/shadcn/ui/accordion";
import { Label } from "components/shadcn/ui/label";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "components/shadcn/ui/table";
import { DealDocummentsReponseDTO, StageComponentProps } from "../types";
import DealFileUpload from "components/common/file-upload/fileUpload";
import { useDispatch, useSelector } from "react-redux";
import {
    selectDealDocumentDetails,
    setDealDocumentsDetails,
    selectFileDetails,
    setFileDetails,
} from "../dealTrackerSlice";
import { deleteDocument, getAllDealDocuments, getPreSignedUrlForDealDocument, uploadDealDocument } from "../service";
import { Button } from "components/shadcn/ui/button";
import { useEffect, useState } from "react";
import { Alert } from "components/shadcn/ui/alert";

export const DealDocumentsSection: React.FC<StageComponentProps> = ({
    formData,
    setFormData,
    isEditing,
}) => {
    const dealDocuments = useSelector(selectDealDocumentDetails);
    const selectedFileForUpload = useSelector(selectFileDetails);
    const dispatch = useDispatch();

    const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
    const [uploadErrorMessage, setUploadErrorMessage] = useState<string>("");

    useEffect(() => {
        setUploadStatus("idle");
        setUploadErrorMessage("");
    }, [formData.id]);

    const handleDownload = async (doc: DealDocummentsReponseDTO) => {
        try {
            const docUrl = await getPreSignedUrlForDealDocument(formData.id, doc.id);
            if (docUrl?.url) {
                window.open(docUrl.url, "_blank");
            } else {
                console.error("Pre-signed URL not returned.");
            }
        } catch (error) {
            console.error("Error downloading document:", error);
        }
    };

    const handleDelete = async (doc: DealDocummentsReponseDTO) => {
        try {
            await deleteDocument(formData.id, doc.id);
            const updatedDealDocuments = await getAllDealDocuments(formData.id);
            dispatch(setDealDocumentsDetails(updatedDealDocuments));
        } catch (error) {
            console.error("Error deleting document:", error);
        }
    };

    const handleUploadDocument = async () => {
        if (!selectedFileForUpload) {
            setUploadErrorMessage("Please select a file to upload.");
            setUploadStatus("error");
            return;
        }

        if (!formData.id) {
            setUploadErrorMessage("Cannot upload document without a deal ID. Please save the deal first.");
            setUploadStatus("error");
            return;
        }

        setUploadStatus("uploading");
        setUploadErrorMessage("");

        try {
            await uploadDealDocument(formData.id, selectedFileForUpload);
            setUploadStatus("success");
            dispatch(setFileDetails(null));
            const updatedDealDocuments = await getAllDealDocuments(formData.id);
            dispatch(setDealDocumentsDetails(updatedDealDocuments));
        } catch (err) {
            const errorMsg =
                err instanceof Error ? err.message : "Upload failed. Please try again.";
            setUploadErrorMessage(errorMsg);
            setUploadStatus("error");
        }
    };

    useEffect(() => {
        if (uploadStatus === "success" || uploadStatus === "error") {
            const timer = setTimeout(() => setUploadStatus("idle"), 3000);
            return () => clearTimeout(timer);
        }
    }, [uploadStatus]);

    return (
        <AccordionItem value="deal-documents">
            <AccordionTrigger className="[&[data-state=open]]:bg-gray-200">
                Deal Documents
            </AccordionTrigger>
            <AccordionContent className="ml-3 space-y-6">
                <div className="text-sm font-normal">
                    Upload and Manage key Deal documents here
                </div>
                <div className="grid gap-2">
                    <Label className="flex items-center gap-2 text-slate-700">
                        <FileText className="h-4 w-4 text-blue-600" /> Total Documents for
                        Deal: {dealDocuments?.length || 0}
                    </Label>
                </div>
                <div className="grid gap-2">
                    <Label className="text-slate-700">Documents</Label>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Document Name</TableHead>
                                <TableHead>Date Added</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {dealDocuments && dealDocuments.length > 0 ? (
                                dealDocuments.map((doc) => (
                                    <TableRow key={doc.id}>
                                        <TableCell>{doc.fileName}</TableCell>
                                        <TableCell>{new Date(doc.createdAt).toLocaleDateString()}</TableCell>
                                        <TableCell>
                                            <div className="flex gap-4 items-center">
                                                <Button
                                                    onClick={() => handleDownload(doc)}
                                                    aria-label="View document"
                                                    variant={"ghost"}
                                                >
                                                    <EyeIcon className="h-4 w-4 text-green-600" />
                                                </Button>
                                                <Button
                                                    onClick={() => handleDelete(doc)}
                                                    aria-label="Delete document"
                                                    disabled={!isEditing}
                                                    variant={"ghost"}
                                                >
                                                    <Trash2 className="h-4 w-4 text-red-600" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell className="text-muted-foreground" colSpan={3}>
                                        No documents uploaded yet
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
                {uploadStatus === "success" && (
                    <Alert
                        variant={"success"}
                        title={"File uploaded successfully!"}
                    />
                )}
                {uploadStatus === "error" && uploadErrorMessage && (
                    <Alert
                        variant={"destructive"}
                        title={uploadErrorMessage}
                    />
                )}

                <DealFileUpload
                    isUploading={uploadStatus === "uploading"}
                    isEditing={isEditing}
                />



                {isEditing && (
                    <div className="w-full flex justify-center">
                        <Button
                            onClick={handleUploadDocument}
                            disabled={!selectedFileForUpload || uploadStatus === "uploading"}
                            className="w-[50%]"
                            variant="primary"
                        >
                            {uploadStatus === "uploading" ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                                    Uploading...
                                </>
                            ) : (
                                "Upload Document"
                            )}
                        </Button>
                    </div>
                )}
            </AccordionContent>
        </AccordionItem >
    );
};
