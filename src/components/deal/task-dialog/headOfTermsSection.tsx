import React from "react";
import {
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "components/shadcn/ui/accordion";
import { Label } from "components/shadcn/ui/label";
import { RadioGroup, RadioGroupItem } from "components/shadcn/ui/radio-group";
import { StageComponentProps } from "../types";

export const HeadsOfTermsSection: React.FC<StageComponentProps> = ({ formData, setFormData }) => {
  // const [alertMessage, setAlertMessage] = useState<{
  //   type: "success" | "destructive";
  //   message: string;
  // } | null>(null);

  // const handleSendHot = async () => {
  //   try {
  //     const response = await callNotifyAutomation(formData.id);
  //     setAlertMessage({ type: "success", message: "Heads of Terms sent successfully!" });
  //     return response.data;
  //   } catch (error) {
  //     console.error("Error sending HoT", error);
  //     setAlertMessage({
  //       type: "destructive",
  //       message: "Failed to send Heads of Terms. Please try again.",
  //     });
  //   }
  // };

  // useEffect(() => {
  //   if (alertMessage) {
  //     const timer = setTimeout(() => setAlertMessage(null), 3000);
  //     return () => clearTimeout(timer);
  //   }
  // }, [alertMessage]);

  return (
    <AccordionItem value="heads-of-terms">
      <AccordionTrigger className="[&[data-state=open]]:bg-gray-200">
        Heads of Terms
      </AccordionTrigger>
      <AccordionContent className="ml-3 flex flex-col gap-6">
        {/* {alertMessage && (
          <Alert variant={alertMessage.type} title={alertMessage.message} />
        )} */}
        <div className="text-sm font-normal">
          Your HoTs may take several drafts to agree.  Upload agreed version via the Documents section.
        </div>

        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-6">
            <Label className="text-slate-700 whitespace-nowrap">HoT Agreed?</Label>
            <RadioGroup
              value={String(formData.isHotAgreed)}
              onValueChange={(value) =>
                setFormData({ ...formData, isHotAgreed: value === "true" })
              }
              className="flex flex-row gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="hot-yes" />
                <Label htmlFor="hot-yes">Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="hot-no" />
                <Label htmlFor="hot-no">No</Label>
              </div>
            </RadioGroup>
          </div>
        </div>

        {/* <div className="flex w-full justify-center pt-2">
          <Button className="w-[40%]" variant="primary" onClick={handleSendHot}>
            <Send className="h-4 w-4 mr-2" />
            Create and Send Heads of Terms
          </Button>
        </div> */}
      </AccordionContent>
    </AccordionItem>
  );
};
