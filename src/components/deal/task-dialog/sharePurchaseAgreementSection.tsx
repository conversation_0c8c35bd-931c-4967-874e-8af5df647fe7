import React from "react";
import {
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from "components/shadcn/ui/accordion";
import { Label } from "components/shadcn/ui/label";
import { Input } from "components/shadcn/ui/input";
import { RadioGroup, RadioGroupItem } from "components/shadcn/ui/radio-group";
import { StageComponentProps } from "../types";

export const SharePurchaseAgreementSection: React.FC<StageComponentProps> = ({ formData, setFormData, errors, showError, handleBlur }) => {
    // const [alertMessage, setAlertMessage] = useState<{
    //     type: "success" | "destructive";
    //     message: string;
    // } | null>(null);

    // const handleSendSPA = async () => {
    //     try {
    //         const response = await callNotifyAutomation(formData.id);
    //         setAlertMessage({
    //             type: "success",
    //             message: "Share Purchase Agreement sent successfully!",
    //         });
    //         return response.data;
    //     } catch (error) {
    //         console.error("Error sending SPA", error);
    //         setAlertMessage({
    //             type: "destructive",
    //             message: "Failed to send Share Purchase Agreement. Please try again.",
    //         });
    //     }
    // };

    // useEffect(() => {
    //     if (alertMessage) {
    //         const timer = setTimeout(() => setAlertMessage(null), 3000);
    //         return () => clearTimeout(timer);
    //     }
    // }, [alertMessage]);

    return (
        <AccordionItem value="spa">
            <AccordionTrigger className="[&[data-state=open]]:bg-gray-200">
                Share Purchase Agreement
            </AccordionTrigger>
            <AccordionContent className="ml-3 space-y-6">
                {/* {alertMessage && (
                    <Alert variant={alertMessage.type} title={alertMessage.message} />
                )} */}
                <div className="text-sm font-normal">
                    Link to the SPA here, to track it as it evolves.
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="grid gap-2">
                        <div className="flex flex-col gap-2">
                            <Label htmlFor="spaLink" className="text-slate-700">
                                Link to Latest SPA
                            </Label>
                            <Input
                                id="spaLink"
                                type="url"
                                value={formData.latestSpaUrl || ""}
                                onChange={(e) => setFormData({ ...formData, latestSpaUrl: e.target.value })}
                                onBlur={() => handleBlur?.("latestSpaUrl")}

                                placeholder="Enter SPA link"
                            />
                            {showError?.("latestSpaUrl") && (
                                <p className="text-red-500 text-sm mt-1">
                                    {errors?.latestSpaUrl}
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="spaDraftDate" className="text-slate-700">
                            Date of Draft
                        </Label>
                        <Input
                            id="spaDraftDate"
                            type="date"
                            value={formData.spaDraftOn?.slice(0, 10) || ""}
                            onChange={(e) => setFormData({ ...formData, spaDraftOn: e.target.value })}
                        />
                    </div>

                    <div className="grid gap-2">
                        <Label className="text-slate-700">Agreed?</Label>
                        <RadioGroup
                            value={formData.isSpaAgreed ? "yes" : "no"}
                            onValueChange={(value) =>
                                setFormData({ ...formData, isSpaAgreed: value === "yes" })
                            }
                            className="flex flex-row gap-5"
                        >
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="yes" id="spa-agreed-yes" />
                                <Label htmlFor="spa-agreed-yes">Yes</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="no" id="spa-agreed-no" />
                                <Label htmlFor="spa-agreed-no">No</Label>
                            </div>
                        </RadioGroup>
                    </div>

                    <div className="grid gap-2">
                        <Label className="text-slate-700">Signed by Seller?</Label>
                        <RadioGroup
                            value={formData.isSignedBySeller ? "yes" : "no"}
                            onValueChange={(value) =>
                                setFormData({ ...formData, isSignedBySeller: value === "yes" })
                            }
                            className="flex flex-row gap-5"
                        >
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="yes" id="seller-signed-yes" />
                                <Label htmlFor="seller-signed-yes">Yes</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="no" id="seller-signed-no" />
                                <Label htmlFor="seller-signed-no">No</Label>
                            </div>
                        </RadioGroup>
                    </div>

                    <div className="grid gap-2 md:col-span-2">
                        <Label className="text-slate-700">Signed by Buyer?</Label>
                        <RadioGroup
                            value={formData.isSignedByBuyer ? "yes" : "no"}
                            onValueChange={(value) =>
                                setFormData({ ...formData, isSignedByBuyer: value === "yes" })
                            }
                            className="flex flex-row gap-5"
                        >
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="yes" id="buyer-signed-yes" />
                                <Label htmlFor="buyer-signed-yes">Yes</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="no" id="buyer-signed-no" />
                                <Label htmlFor="buyer-signed-no">No</Label>
                            </div>
                        </RadioGroup>
                    </div>
                </div>

                {/* <div className="flex w-full justify-center">
                    <Button className="w-[50%]" variant="primary" onClick={handleSendSPA}>
                        <Send className="h-4 w-4 mr-2" />
                        Create and Send SPA
                    </Button>
                </div> */}
            </AccordionContent>
        </AccordionItem>
    );
};
