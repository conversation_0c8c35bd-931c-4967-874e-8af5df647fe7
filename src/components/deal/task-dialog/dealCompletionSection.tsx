import React, { useEffect, useState } from "react";
import {
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from "components/shadcn/ui/accordion";
import { Label } from "components/shadcn/ui/label";
import { Input } from "components/shadcn/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "components/shadcn/ui/select";
import { Checkbox } from "components/shadcn/ui/checkbox";
import { StageComponentProps } from "../types";
import { formatNumber } from "helpers";
// import { Alert } from "components/shadcn/ui/alert";

// type AlertState = {
//     type: "success" | "error";
//     message: string;
// } | null;

export const DealCompletionSection: React.FC<StageComponentProps> = ({
    formData,
    setFormData,
}) => {
    const [finalValuationDisplay, setFinalValuationDisplay] = useState("");

    useEffect(() => {
        if (formData.finalValuationAgreed) {
            setFinalValuationDisplay(formatNumber(formData.finalValuationAgreed));
        } else {
            setFinalValuationDisplay("");
        }
    }, [formData.finalValuationAgreed]);

    const handleFinalValuationChange = (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const rawValue = e.target.value.replace(/,/g, "");
        const floatVal = parseFloat(rawValue);

        if (!isNaN(floatVal)) {
            setFormData({
                ...formData,
                finalValuationAgreed: floatVal,
            });
            setFinalValuationDisplay(formatNumber(rawValue));
        } else {
            setFormData({
                ...formData,
                finalValuationAgreed: 0,
            });
            setFinalValuationDisplay("");
        }
    };
    // const [alert, setAlert] = useState<AlertState>(null);

    // useEffect(() => {
    //     if (alert) {
    //         const timer = setTimeout(() => setAlert(null), 3000);
    //         return () => clearTimeout(timer);
    //     }
    // }, [alert]);

    // const setUpWireTransfer = async () => {
    //     try {
    //         const response = await callNotifyAutomation(formData.id);
    //         setAlert({ type: "success", message: "Wire-transfer account setup successfully!" });
    //         return response.data;
    //     } catch (error) {
    //         console.error("Error calling notify automation", error);
    //         setAlert({
    //             type: "error",
    //             message: "Failed to set up wire-transfer account. Please try again.",
    //         });
    //     }
    // };

    return (
        <AccordionItem value="deal-completion">
            <AccordionTrigger className="[&[data-state=open]]:bg-gray-200">
                Deal Completion
            </AccordionTrigger>
            <AccordionContent className="ml-3 space-y-4">
                {/* {alert && (
                    <Alert
                        variant={alert.type === "success" ? "success" : "destructive"}
                        title={alert.message}
                    />
                )} */}
                <div className="text-sm font-normal">
                    Congratulations on getting your Deal over the line. Mark it as
                    Complete here.
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="grid gap-2">
                        <Label
                            htmlFor="finalValuation"
                            className="flex items-center gap-2 text-slate-700"
                        >
                            Final Valuation Agreed
                        </Label>
                        <div className="flex flex-row items-center gap-2">
                            <div className="text-sm font-semibold  text-gray-500">
                                {formData.currency}
                            </div>

                            <Input
                                id="finalValuation"
                                type="text"
                                inputMode="decimal"
                                value={finalValuationDisplay}
                                onChange={handleFinalValuationChange}
                                placeholder="Enter final valuation"
                            />
                        </div>
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="finalValuationDate" className="text-slate-700">
                            Final Valuation Agreed On
                        </Label>
                        <Input
                            id="finalValuationDate"
                            type="date"
                            value={formData.finalValuationAgreedOn?.slice(0, 10) || ""}
                            onChange={(e) =>
                                setFormData({
                                    ...formData,
                                    finalValuationAgreedOn: e.target.value,
                                })
                            }
                        />
                    </div>

                    <div className="grid gap-2">
                        <Label className="text-slate-700">Account Status</Label>
                        <Select
                            value={formData.accountStatus || ""}
                            onValueChange={(value) =>
                                setFormData({ ...formData, accountStatus: value })
                            }
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent className="z-9999">
                                <SelectItem value="Not Started">Not Started</SelectItem>
                                <SelectItem value="Pending">Pending</SelectItem>
                                <SelectItem value="Open">Open</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="grid gap-2 ">
                        <Label className="text-slate-700">Completion Status</Label>
                        <Select
                            value={formData.completionStatus || ""}
                            onValueChange={(value) =>
                                setFormData({ ...formData, completionStatus: value })
                            }
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent className="z-9999">
                                <SelectItem value="Pending">Pending</SelectItem>
                                <SelectItem value="Completed">Completed</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="iban" className="text-slate-700">
                            IBAN (account reference number for wire-transfer)
                        </Label>
                        <Input
                            id="iban"
                            value={formData.iban || ""}
                            onChange={(e) =>
                                setFormData({ ...formData, iban: e.target.value })
                            }
                            placeholder="Enter IBAN"
                        />
                    </div>

                    <div className="gap-2 flex flex-row items-center">
                        <Checkbox
                            id="dualAuth"
                            checked={formData.isDualAuthorized || false}
                            onCheckedChange={(checked) =>
                                setFormData({ ...formData, isDualAuthorized: !!checked })
                            }
                        />
                        <Label htmlFor="dualAuth" className="text-slate-700">
                            Dual-Authorised?
                        </Label>
                    </div>
                </div>

                {/* 
                <div className="flex justify-center">
                    <Button className="w-[50%]" variant="primary" onClick={setUpWireTransfer}>
                        <Send className="h-4 w-4 mr-2" />
                        Setup Wire-Transfer Account
                    </Button>
                </div> */}
            </AccordionContent>
        </AccordionItem>
    );
};
