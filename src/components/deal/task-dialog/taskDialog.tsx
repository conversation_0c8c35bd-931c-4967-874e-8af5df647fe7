// src/components/TaskDialog.tsx

import type React from "react";
import {
    X,
    Building,
    Hash,
    Phone,
    Mail,
    User,
    Globe,
    MapPin,
    BriefcaseBusiness,
    WalletIcon,
} from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogFooter,
} from "components/shadcn/ui/dialog";
import { Label } from "components/shadcn/ui/label";
import { Input } from "components/shadcn/ui/input";
import { Button } from "components/shadcn/ui/button";
import { Accordion } from "components/shadcn/ui/accordion";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "components/shadcn/ui/select";
import { Checkbox } from "components/shadcn/ui/checkbox";
import Loader from "components/common/loader";

import { CURRENCY, DealTask } from "../types";
import { ResearchSection } from "./researchSection";
import { OfferSection } from "./offer-section/offerSection";
import { HeadsOfTermsSection } from "./headOfTermsSection";
import { DueDiligenceSection } from "./dueDilligenceSection";
import { SharePurchaseAgreementSection } from "./sharePurchaseAgreementSection";
import { DealCompletionSection } from "./dealCompletionSection";
import { DealDocumentsSection } from "./dealDocumentSection";
import { NotesSection } from "./notes-section/notesSection";
import { useEffect } from "react";
import { useTaskForm } from "./useTaskForm";

interface TaskDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    formData: DealTask;
    setFormData: (data: DealTask) => void;
    onSave: (isEditMode: boolean, data: DealTask) => Promise<void>;
    columns: any;
    isLoading?: boolean;
}

export const TaskDialog: React.FC<TaskDialogProps> = ({
    open,
    onOpenChange,
    formData: initialFormData,
    setFormData: setExternalFormData,
    onSave,
    isLoading: propIsLoading,
}) => {
    const {
        formData,
        dealTrackerCopy,
        errors,
        getAllStages,
        isEditMode,
        formIsValid,
        setFormData,
        handleChange,
        handleBlur,
        handlePhoneNumberChange,
        handleSubmit,
        showError,
        isLoading: hookIsLoading,
    } = useTaskForm({
        initialFormData,
        onSave,
        onClose: () => onOpenChange(false),
        isOpen: open,
    });

    useEffect(() => {
        setExternalFormData(formData);
    }, [formData, setExternalFormData]);

    return (
        <Dialog
            aria-describedby="Task dialogue"
            open={open}
            onOpenChange={onOpenChange}
        >
            {(propIsLoading || hookIsLoading) && <Loader />}
            <DialogContent className="pr-0 sm:max-w-[70vw] z-[9999] max-h-[90vh] rounded-2xl border-none bg-gradient-to-br from-white to-slate-50 shadow-xl flex flex-col">
                <div className="flex flex-row items-center justify-between gap-4 mb-3 px-4 pt-4">
                    <DialogTitle className="text-xl font-bold text-slate-800 flex flex-row gap-3 items-center">
                        <BriefcaseBusiness className="h-5 w-5 text-primary-600" />
                        {isEditMode ? formData.companyName : "New Deal"}
                    </DialogTitle>
                    <button
                        onClick={() => onOpenChange(false)}
                        className="rounded-full p-1 hover:bg-slate-100 transition-colors"
                    >
                        <X className="h-5 w-5 text-slate-500" />
                    </button>
                </div>
                <div
                    className="overflow-auto px-4 pb-4"
                    style={{ maxHeight: "calc(90vh - 120px)" }}
                >
                    <div className="grid gap-5 py-4">
                        <div className="grid gap-4">
                            <Label
                                htmlFor="companyName"
                                className="flex items-center gap-2 text-slate-700"
                            >
                                <Building className="h-4 w-4 text-blue-500" /> Company Name{" "}
                                <span className="text-primary-600">*</span>
                            </Label>
                            <div className="flex flex-row w-full gap-5">
                                <div className="flex flex-col w-[65%]">
                                    <Input
                                        id="companyName"
                                        value={formData.companyName}
                                        onChange={(e) =>
                                            handleChange("companyName", e.target.value)
                                        }
                                        onBlur={() => handleBlur("companyName")}
                                        className={`border-slate-200  ${showError("companyName") ? "border-red-500" : ""
                                            }`}
                                        placeholder="Enter company name"
                                        required
                                    />
                                    {showError("companyName") && (
                                        <p className="text-red-500 text-sm mt-1">
                                            {errors.companyName}
                                        </p>
                                    )}
                                </div>
                                <div className="flex flex-row items-center gap-2 mt-2">
                                    <Checkbox
                                        id="important"
                                        checked={!!formData.isImportant}
                                        onCheckedChange={(checked) =>
                                            handleChange("isImportant", !!checked)
                                        }
                                    />
                                    <Label
                                        htmlFor="important"
                                        className="flex items-center gap-2 text-slate-700"
                                    >
                                        Mark As Important
                                    </Label>
                                </div>
                            </div>
                        </div>

                        <div className="w-[full] p-3">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <div className="space-y-2">
                                    <Label
                                        htmlFor="country"
                                        className="flex items-center gap-2 text-slate-700"
                                    >
                                        <MapPin className="h-4 w-4 text-red-500" /> Country
                                    </Label>
                                    <Input
                                        id="country"
                                        value={formData.country || "United Kingdom"}
                                        onChange={(e) => handleChange("country", e.target.value)}
                                        className="border-slate-200"
                                        placeholder="Enter country"
                                        disabled
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label
                                        htmlFor="companyNumber"
                                        className="flex items-center gap-2 text-slate-700"
                                    >
                                        <Hash className="h-4 w-4 text-green-500" /> Company ID
                                        Number
                                        <span className="text-primary-600">*</span>
                                    </Label>
                                    <Input
                                        id="companyNumber"
                                        value={formData.companyNumber}
                                        onChange={(e) =>
                                            handleChange("companyNumber", e.target.value)
                                        }
                                        onBlur={() => handleBlur("companyNumber")}
                                        className={`border-slate-200 ${showError("companyNumber") ? "border-red-500" : ""
                                            }`}
                                        placeholder="Enter company number"
                                        required
                                    />
                                    {showError("companyNumber") && (
                                        <p className="text-red-500 text-sm mt-1">
                                            {errors.companyNumber}
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label
                                        htmlFor="website"
                                        className="flex items-center gap-2 text-slate-700"
                                    >
                                        <Globe className="h-4 w-4 text-blue-400" /> Website
                                    </Label>
                                    <Input
                                        id="website"
                                        value={formData.website || ""}
                                        onChange={(e) => handleChange("website", e.target.value)}
                                        onBlur={() => handleBlur("website")}
                                        className={`border-slate-200 ${showError("website") ? "border-red-500" : ""
                                            }`}
                                        placeholder="Enter website URL (e.g., example.com)"
                                    />
                                    {showError("website") && (
                                        <p className="text-red-500 text-sm mt-1">
                                            {errors.website}
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label
                                        htmlFor="contactName"
                                        className="flex items-center gap-2 text-slate-700"
                                    >
                                        <User className="h-4 w-4 text-amber-500" /> Contact Name
                                    </Label>
                                    <Input
                                        id="contactName"
                                        value={formData.contactName || ""}
                                        onChange={(e) =>
                                            handleChange("contactName", e.target.value)
                                        }
                                        className="border-slate-200"
                                        placeholder="Enter contact name"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label
                                        htmlFor="emailAddress"
                                        className="flex items-center gap-2 text-slate-700"
                                    >
                                        <Mail className="h-4 w-4 text-violet-500" /> Email Address
                                    </Label>
                                    <Input
                                        id="emailAddress"
                                        type="email"
                                        value={formData.email || ""}
                                        onChange={(e) => handleChange("email", e.target.value)}
                                        onBlur={() => handleBlur("email")}
                                        className={`border-slate-200 ${showError("email") ? "border-red-500" : ""
                                            }`}
                                        placeholder="Enter email address"
                                    />
                                    {showError("email") && (
                                        <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label
                                        htmlFor="phoneNumber"
                                        className="flex items-center gap-2 text-slate-700"
                                    >
                                        <Phone className="h-4 w-4 text-emerald-500" /> Phone Number
                                    </Label>
                                    <Input
                                        id="phoneNumber"
                                        type="tel"
                                        value={formData.phoneNumber || ""}
                                        onChange={handlePhoneNumberChange}
                                        onBlur={() => handleBlur("phoneNumber")}
                                        className={`border-slate-200 ${showError("phoneNumber") ? "border-red-500" : ""
                                            }`}
                                        placeholder="e.g., +447911123456"
                                    />
                                    {showError("phoneNumber") && (
                                        <p className="text-red-500 text-sm mt-1">
                                            {errors.phoneNumber}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 p-3">
                            <div className="grid gap-2">
                                <Label
                                    htmlFor="dealStage"
                                    className="flex items-center gap-2 text-slate-700"
                                >
                                    <Hash className="h-4 w-4 text-purple-500" />
                                    Select Deal Stage
                                    <span className="text-primary-600">*</span>
                                </Label>

                                <Select
                                    value={formData?.dealStageId || ""}
                                    onValueChange={(value) => {
                                        handleChange("dealStageId", value);
                                        handleBlur("dealStageId");
                                    }}
                                    onOpenChange={(isOpen) => {
                                        if (!isOpen) handleBlur("dealStageId");
                                    }}
                                >
                                    <SelectTrigger
                                        className={showError("dealStageId") ? "border-red-500" : ""}
                                    >
                                        <SelectValue placeholder="Select deal stage" />
                                    </SelectTrigger>
                                    <SelectContent className="z-9999">
                                        {getAllStages?.map((stage: any) => (
                                            <SelectItem
                                                key={stage.dealStageId}
                                                value={stage.dealStageId}
                                            >
                                                {stage.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>

                                <p
                                    className={`text-red-500 text-sm mt-1 min-h-[20px] transition-all duration-300 ${showError("dealStageId") ? "opacity-100" : "opacity-0"
                                        }`}
                                >
                                    {showError("dealStageId") ? errors.dealStageId : ""}
                                </p>
                            </div>

                            <div className="grid gap-2">
                                <Label
                                    htmlFor="dealCurrency"
                                    className="text-slate-700 flex flex-row gap-2"
                                >
                                    <WalletIcon className="h-4 w-4 text-green-500" />
                                    Deal Currency
                                    <span className="text-primary-600">*</span>
                                </Label>

                                <Select
                                    value={formData.currency || ""}
                                    onValueChange={(value: string) => {
                                        handleChange("currency", value);
                                        handleBlur("currency");
                                    }}
                                    onOpenChange={(isOpen) => {
                                        if (!isOpen) handleBlur("currency");
                                    }}
                                    disabled={Object.values(CURRENCY).includes(
                                        formData.currency as CURRENCY
                                    )}
                                >
                                    <SelectTrigger
                                        aria-label="Select currency"
                                        className={showError("currency") ? "border-red-500" : ""}
                                    >
                                        <SelectValue placeholder="Select currency" />
                                    </SelectTrigger>
                                    <SelectContent className="z-9999">
                                        {dealTrackerCopy?.currencies?.map((currency: string) => (
                                            <SelectItem key={currency} value={currency}>
                                                {currency}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>

                                <p
                                    className={`text-red-500 text-sm mt-1 min-h-[20px] transition-all duration-300 ${showError("currency") ? "opacity-100" : "opacity-0"
                                        }`}
                                >
                                    {showError("currency") ? errors.currency : ""}
                                </p>
                            </div>
                        </div>

                        <Accordion
                            type="single"
                            collapsible
                            className="w-full space-y-4 mt-0"
                        >
                            <DealDocumentsSection
                                formData={formData}
                                setFormData={setFormData}
                                isEditing={isEditMode}
                            />

                            <NotesSection
                                formData={formData}
                                setFormData={setFormData}
                                isEditing={isEditMode}
                            />

                            <ResearchSection formData={formData} setFormData={setFormData} />
                            <OfferSection
                                formData={formData}
                                setFormData={setFormData}
                                isEditing={isEditMode}
                            />
                            <HeadsOfTermsSection
                                formData={formData}
                                setFormData={setFormData}
                            />
                            <DueDiligenceSection
                                formData={formData}
                                setFormData={setFormData}
                                errors={errors}
                                showError={showError}
                                handleBlur={() => handleBlur("virtualDataRoomUrl")}
                            />
                            <SharePurchaseAgreementSection
                                formData={formData}
                                setFormData={setFormData}
                                errors={errors}
                                showError={showError}
                                handleBlur={() => handleBlur("latestSpaUrl")}
                            />
                            <DealCompletionSection
                                formData={formData}
                                setFormData={setFormData}
                            />
                        </Accordion>
                    </div>
                </div>

                <DialogFooter className="gap-2 sm:gap-0 px-4 pb-4 border-t border-slate-100 mt-auto">
                    <Button
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        className="border-slate-200 hover:bg-slate-100 hover:text-slate-900"
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleSubmit}
                        disabled={!formIsValid || propIsLoading || hookIsLoading}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                        {isEditMode ? "Update Deal" : "Create Deal"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
