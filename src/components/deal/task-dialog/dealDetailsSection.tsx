import type React from "react"
import { Hash, Globe, User, Mail, MapPin, Phone } from "lucide-react"
import { AccordionItem, AccordionTrigger, AccordionContent } from "components/shadcn/ui/accordion"
import { Label } from "components/shadcn/ui/label"
import { Input } from "components/shadcn/ui/input"
import { StageComponentProps } from "../types"

export const DealDetailsSection: React.FC<StageComponentProps> = ({ formData, setFormData }) => {
    return (
        <AccordionItem value="deal-details">
            <AccordionTrigger>Deal Details</AccordionTrigger>
            <AccordionContent className="ml-3">
                <div className="grid gap-4 py-2">
                    <Label htmlFor="country" className="flex items-center gap-2 text-slate-700">
                        <MapPin className="h-4 w-4 text-red-500" /> Country
                    </Label>
                    <Input
                        id="country"
                        value={formData.country}
                        onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                        className="border-slate-200"
                        placeholder="Enter country"
                    />
                </div>
                <div className="grid gap-4 py-2">
                    <Label htmlFor="companyNumber" className="flex items-center gap-2 text-slate-700">
                        <Hash className="h-4 w-4 text-green-500" /> Company ID Number
                    </Label>
                    <Input
                        id="companyNumber"
                        value={formData.companyNumber}
                        onChange={(e) => setFormData({ ...formData, companyNumber: e.target.value })}
                        className="border-slate-200"
                        placeholder="Enter company number"
                    />
                </div>
                <div className="grid gap-4 py-2">
                    <Label htmlFor="website" className="flex items-center gap-2 text-slate-700">
                        <Globe className="h-4 w-4 text-blue-400" /> Website
                    </Label>
                    <Input
                        id="website"
                        value={formData.website || ""}
                        onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                        className="border-slate-200"
                        placeholder="Enter website URL"
                    />
                </div>
                <div className="grid gap-4 py-2">
                    <Label htmlFor="contactName" className="flex items-center gap-2 text-slate-700">
                        <User className="h-4 w-4 text-amber-500" /> Contact Name
                    </Label>
                    <Input
                        id="contactName"
                        value={formData.contactName || ""}
                        onChange={(e) => setFormData({ ...formData, contactName: e.target.value })}
                        className="border-slate-200"
                        placeholder="Enter contact name"
                    />
                </div>
                <div className="grid gap-4 py-2">
                    <Label htmlFor="emailAddress" className="flex items-center gap-2 text-slate-700">
                        <Mail className="h-4 w-4 text-violet-500" /> Email Address
                    </Label>
                    <Input
                        id="emailAddress"
                        type="email"
                        value={formData.email || ""}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        className="border-slate-200"
                        placeholder="Enter email address"
                    />
                </div>
                <div className="grid gap-4 py-2">
                    <Label htmlFor="phoneNumber" className="flex items-center gap-2 text-slate-700">
                        <Phone className="h-4 w-4 text-emerald-500" /> Phone Number
                    </Label>
                    <Input
                        id="phoneNumber"
                        type="tel"
                        value={formData.phoneNumber || ""}
                        onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
                        className="border-slate-200"
                        placeholder="Enter phone number"
                    />
                </div>
            </AccordionContent>
        </AccordionItem>
    )
}
