import React from "react";
import {
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from "components/shadcn/ui/accordion";
import { Label } from "components/shadcn/ui/label";
import { Input } from "components/shadcn/ui/input";
import { StageComponentProps } from "../types";
import { Button } from "components/shadcn/ui/button";
import { Send } from "lucide-react";

export const DueDiligenceSection: React.FC<StageComponentProps> = ({
    formData,
    handleBlur,
    setFormData,
    errors,
    showError,
}) => {
    const handleVirtualDataRoomUrlClick = () => {
        const url = formData?.virtualDataRoomUrl?.startsWith("http")
            ? formData.virtualDataRoomUrl
            : `https://${formData.virtualDataRoomUrl}`;
        window.open(url, "_blank");
    }
    return (
        <AccordionItem value="due-diligence">
            <AccordionTrigger className="[&[data-state=open]]:bg-gray-200">
                Due Diligence
            </AccordionTrigger>
            <AccordionContent className="ml-3 space-y-6">
                <div className="text-sm font-normal">
                    Add a link to the Virtual Data Room for this Deal here.
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="grid gap-2">
                        <Label htmlFor="ddBeganOn" className="text-slate-700">
                            Due Diligence Began On
                        </Label>
                        <Input
                            id="ddBeganOn"
                            type="date"
                            value={formData.dueDiligenceBeganOn?.slice(0, 10) || ""}
                            onChange={(e) =>
                                setFormData({
                                    ...formData,
                                    dueDiligenceBeganOn: e.target.value,
                                })
                            }
                        />
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="ddTargetDate" className="text-slate-700">
                            Target Due Diligence Conclusion Date
                        </Label>
                        <Input
                            id="ddTargetDate"
                            type="date"
                            value={formData.dueDiligenceConclusionOn?.slice(0, 10) || ""}
                            onChange={(e) =>
                                setFormData({
                                    ...formData,
                                    dueDiligenceConclusionOn: e.target.value,
                                })
                            }
                        />
                    </div>
                    <div className="grid gap-2">
                        <div className="flex flex-col gap-2">
                            <Label htmlFor="linktovdr" className="text-slate-700">
                                Link to Virtual data room
                            </Label>
                            <Input
                                id="vdrlink"
                                type="url"
                                value={formData.virtualDataRoomUrl || ""}
                                onChange={(e) =>
                                    setFormData({
                                        ...formData,
                                        virtualDataRoomUrl: e.target.value,
                                    })
                                }
                                onBlur={() => handleBlur?.("virtualDataRoomUrl")}
                                placeholder="Enter link to virtual data room"
                            />
                            {showError?.("virtualDataRoomUrl") && (
                                <p className="text-red-500 text-sm mt-1">
                                    {errors?.virtualDataRoomUrl}
                                </p>
                            )}
                        </div>
                    </div>
                    {formData.virtualDataRoomUrl &&
                        <div className="flex w-full items-end justify-start">
                            <Button
                                className="w-[60%]"
                                variant="primary"
                                onClick={() => handleVirtualDataRoomUrlClick()}
                            >
                                <Send className="h-4 w-4 mr-2" />
                                Open virtual data room
                            </Button>
                        </div>
                    }
                </div>
            </AccordionContent>
        </AccordionItem>
    );
};
