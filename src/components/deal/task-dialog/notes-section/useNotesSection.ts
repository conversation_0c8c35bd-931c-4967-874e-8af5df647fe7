import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteNote,
  getAllDealNotes,
  saveNotes,
  updateNote,
} from "../../service";
import { selectDealNotes, setDealNotes } from "../../dealTrackerSlice";

export const CHAR_LIMIT = 1000;

export const useDealNotes = (dealId: string) => {
  const dispatch = useDispatch();
  const notes = useSelector(selectDealNotes);

  const [isLoading, setIsLoading] = useState(false);
  const [newNote, setNewNote] = useState("");
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState("");
  const [noteDeleted, setNoteDeleted] = useState(false);
  const refreshNotes = async () => {
    const updatedNotes = await getAllDealNotes(dealId);
    dispatch(setDealNotes(updatedNotes));
  };

  const addNote = async () => {
    if (!newNote.trim()) return;
    setIsLoading(true);
    try {
      await saveNotes(dealId, newNote);
      await refreshNotes();
      setNewNote("");
    } catch (error) {
      console.error("Error adding note:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const deleteNoteById = async (noteId: string) => {
    setIsLoading(true);
    try {
      await deleteNote(dealId, noteId);
      await refreshNotes();
      setNoteDeleted(true);
      setTimeout(() => setNoteDeleted(false), 5000);
    } catch (error) {
      console.error("Error deleting note:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const editNote = async () => {
    if (!editingText.trim() || !editingNoteId) return;
    setIsLoading(true);
    try {
      await updateNote(dealId, editingNoteId, editingText);
      await refreshNotes();
      setEditingNoteId(null);
      setEditingText("");
    } catch (error) {
      console.error("Error editing note:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    notes,
    isLoading,
    newNote,
    noteDeleted,
    setNewNote,
    addNote,
    deleteNoteById,
    editingNoteId,
    setEditingNoteId,
    editingText,
    setEditingText,
    editNote,
    refreshNotes,
  };
};
