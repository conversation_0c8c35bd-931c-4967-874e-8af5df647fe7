import { DealTask } from "../types";

export interface TaskFormErrors {
  companyName?: string;
  companyNumber?: string;
  dealStageId?: string;
  website?: string;
  email?: string;
  phoneNumber?: string;
  virtualDataRoomUrl?: string;
  latestSpaUrl?: string;
  currency?: string;
}

export const validateTaskForm = (formData: DealTask): TaskFormErrors => {
  let errors: TaskFormErrors = {};

  if (!formData.companyName.trim()) {
    errors.companyName = "Company Name is required.";
  }
  if (!formData.companyNumber.trim()) {
    errors.companyNumber = "Company ID Number is required.";
  }
  if (!formData.dealStageId) {
    errors.dealStageId = "Deal Stage is required.";
  }
  if (!formData.currency) {
    errors.currency = "Currency is required.";
  }

  const websiteRegex =
    /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/.*)?$/;
  if (formData.website && !websiteRegex.test(formData.website)) {
    errors.website =
      "Please enter a valid website URL (e.g., example.com, www.example.com, https://example.com).";
  }

  if (
    formData.virtualDataRoomUrl &&
    !websiteRegex.test(formData.virtualDataRoomUrl)
  ) {
    errors.virtualDataRoomUrl =
      "Please enter a valid website URL (e.g., example.com, www.example.com, https://example.com).";
  }
  if (formData.latestSpaUrl && !websiteRegex.test(formData.latestSpaUrl)) {
    errors.latestSpaUrl =
      "Please enter a valid website URL (e.g., example.com, www.example.com, https://example.com).";
  }

  if (
    formData.email &&
    !/^[^\s@]+@[^\s@]+\.[^\s@]{2,3}$/.test(formData.email)
  ) {
    errors.email = "Please enter a valid email address.";
  }

  if (
    formData.phoneNumber &&
    !/^\+?[0-9\s-()]{7,20}$/.test(formData.phoneNumber)
  ) {
    errors.phoneNumber =
      "Please enter a valid phone number (7-20 digits, optional country code).";
  }

  return errors;
};

export const isFormValid = (errors: TaskFormErrors): boolean => {
  return Object.values(errors).every((error) => !error);
};
