import type React from "react";
import {
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from "components/shadcn/ui/accordion";
import { Label } from "components/shadcn/ui/label";
import { Input } from "components/shadcn/ui/input";
import { StageComponentProps } from "../types";
import { useState, useEffect } from "react";
import { formatNumber } from "helpers";

export const ResearchSection: React.FC<StageComponentProps> = ({
    formData,
    setFormData,
}) => {
    const [askingDisplay, setAskingDisplay] = useState("");
    const [valuationDisplay, setValuationDisplay] = useState("");

    useEffect(() => {
        if (formData.askingPrice) {
            setAskingDisplay(formatNumber(formData.askingPrice));
        } else {
            setAskingDisplay("");
        }

        if (formData.estimatedValuation) {
            setValuationDisplay(formatNumber(formData.estimatedValuation));
        } else {
            setValuationDisplay("");
        }
    }, [formData.askingPrice, formData.estimatedValuation]);

    const handleAskingPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const rawValue = e.target.value.replace(/,/g, "");
        const floatVal = parseFloat(rawValue);

        if (!isNaN(floatVal)) {
            setFormData({
                ...formData,
                askingPrice: floatVal,
            });
            setAskingDisplay(formatNumber(rawValue));
        } else {
            setFormData({
                ...formData,
                askingPrice: 0,
            });
            setAskingDisplay("");
        }
    };

    const handleValuationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const rawValue = e.target.value.replace(/,/g, "");
        const floatVal = parseFloat(rawValue);

        if (!isNaN(floatVal)) {
            setFormData({
                ...formData,
                estimatedValuation: floatVal,
            });
            setValuationDisplay(formatNumber(rawValue));
        } else {
            setFormData({
                ...formData,
                estimatedValuation: 0,
            });
            setValuationDisplay("");
        }
    };

    return (
        <AccordionItem value="research">
            <AccordionTrigger className="[&[data-state=open]]:bg-gray-200">
                Research
            </AccordionTrigger>
            <AccordionContent className="ml-3 space-y-6">
                <div className="text-sm font-normal">
                    Review the Candidate and Upload Reports via the Documents section
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">


                    <div className="grid gap-2">
                        <Label htmlFor="askingPrice" className="text-slate-700">
                            Asking Price
                        </Label>
                        <div className="flex flex-row items-center gap-2">
                            <div className="text-sm font-semibold  text-gray-500">{formData.currency}</div>
                            <Input
                                id="askingPrice"
                                type="text"
                                inputMode="decimal"
                                value={askingDisplay}
                                onChange={handleAskingPriceChange}
                                placeholder="Enter asking price"
                            />
                        </div>
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="estimatedValuation" className="text-slate-700">
                            Estimated Valuation
                        </Label>
                        <div className="flex flex-row items-center gap-2">
                            <div className="text-sm font-semibold  text-gray-500">{formData.currency}</div>
                            <Input
                                id="estimatedValuation"
                                type="text"
                                inputMode="decimal"
                                value={valuationDisplay}
                                onChange={handleValuationChange}
                                placeholder="Enter valuation"
                            />
                        </div>
                    </div>
                </div>

                {/* <DealFileUpload dealId={formData.id} /> */}
            </AccordionContent>
        </AccordionItem>
    );
};
