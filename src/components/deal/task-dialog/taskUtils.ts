import { DealTask } from "../types";

export const cleanFormData = (data: DealTask): DealTask => {
  const cleanedData: DealTask = { ...data };

  const optionalKeys: Array<keyof DealTask> = [
    "country",
    "website",
    "contactName",
    "email",
    "phoneNumber",
    "virtualDataRoomUrl",
    "latestSpaUrl",
  ];

  for (const key of optionalKeys) {
    const value = cleanedData[key];
    if (typeof value === "string" && value.trim() === "") {
      (cleanedData[key] as any) = null;
    } else if (typeof value === "number" && value === 0) {
      (cleanedData[key] as any) = 0;
    }
  }
  return cleanedData;
};
