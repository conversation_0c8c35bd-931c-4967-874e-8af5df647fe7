import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { OFFER_STATUS } from "./types";

export interface GetDeal {
  id: string;
  dealStageId: string;
  companyName: string;
  companyNumber: string;
  currency: string;
  notes: string;
  isImportant: number;
  position: number;
  createdAt: string;
  updatedAt: string;
}

export interface Stage {
  id: string;
  name: string;
  position: number;
  deals: GetDeal[];
  dealStage: DealStage;
}

export interface DealStage {
  id: string;
  name: string;
  position: number;
}

export interface DealDocuments {
  id: string;
  fileName: string;
  uploadedBy: string;
  createdAt: string;
}

export interface DealNotes {
  id: string;
  note: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface DealOffer {
  id: string;
  offer: number;
  status: OFFER_STATUS;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}
interface DealTrackerState {
  stages: Stage[];
  dealStages: DealStage[];
  fileDetails: any;
  dealDcoumentsDetails: DealDocuments[];
  dealNotes: DealNotes[];
  dealOffer: DealOffer[];
}

const initialState: DealTrackerState = {
  stages: [],
  dealStages: [],
  fileDetails: null,
  dealDcoumentsDetails: [],
  dealNotes: [],
  dealOffer: [],
};

export const dealTrackerSlice = createSlice({
  name: "dealTracker",
  initialState,
  reducers: {
    setAllDeals: (state, action: PayloadAction<Stage[]>) => {
      state.stages = action.payload;
    },
    setDealStages: (state, action: PayloadAction<DealStage[]>) => {
      state.dealStages = action.payload;
    },
    setFileDetails: (state, action: PayloadAction<any>) => {
      state.fileDetails = action.payload;
    },
    setDealDocumentsDetails: (
      state,
      action: PayloadAction<DealDocuments[]>
    ) => {
      state.dealDcoumentsDetails = action.payload;
    },
    setDealNotes: (state, action: PayloadAction<DealNotes[]>) => {
      state.dealNotes = action.payload;
    },
    setDealOffers: (state, action: PayloadAction<DealOffer[]>) => {
      state.dealOffer = action.payload;
    },
  },
});

export const {
  setAllDeals,
  setDealStages,
  setFileDetails,
  setDealDocumentsDetails,
  setDealNotes,
  setDealOffers,
} = dealTrackerSlice.actions;

export default dealTrackerSlice.reducer;

export const selectAllDeals = (state: { dealTracker: DealTrackerState }) =>
  state.dealTracker.stages;

export const selectDealStages = (state: { dealTracker: DealTrackerState }) =>
  state.dealTracker.dealStages;

export const selectFileDetails = (state: { dealTracker: DealTrackerState }) =>
  state.dealTracker.fileDetails;

export const selectDealDocumentDetails = (state: {
  dealTracker: DealTrackerState;
}) => state.dealTracker.dealDcoumentsDetails;

export const selectDealNotes = (state: { dealTracker: DealTrackerState }) =>
  state.dealTracker.dealNotes;

export const selectDealOffers = (state: { dealTracker: DealTrackerState }) =>
  state.dealTracker.dealOffer;
