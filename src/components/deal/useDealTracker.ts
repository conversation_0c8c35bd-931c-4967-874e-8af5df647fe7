import { useState, useEffect, useCallback, useRef } from "react";
import { DragEndEvent, DragStartEvent } from "@dnd-kit/core";
import { DealTask, Columns, CreateDealDTO, defaultDealTask } from "./types";
import {
  getDealById,
  createDeal,
  getDealStages,
  moveDeal,
  positionDeal,
  getDeals,
  uploadDealDocument,
  getAllDealDocuments,
  getAllDealNotes,
  getAllDealOffers,
  updateDeal,
  saveNotes,
  createDealOffer,
} from "./service";
import {
  selectFileDetails,
  setDealDocumentsDetails,
  setDealNotes,
  setDealOffers,
  setDealStages,
  setFileDetails,
} from "./dealTrackerSlice";
import { useDispatch, useSelector } from "react-redux";
import { mixpanelCustomEvent } from "components/mixpanel/eventTriggers";
import { MixpanelEventName } from "components/mixpanel/types";

export const useDealTracker = () => {
  const uploadedFileDetails = useSelector(selectFileDetails);
  const dispatch = useDispatch();

  const [columns, setColumns] = useState<Columns>({});
  const [activeId, setActiveId] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState<DealTask>(defaultDealTask);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dealSuccess, setDealSuccess] = useState(false);
  const [dealError, setDealError] = useState(false);
  const [dealErrorMessage, setDealErrorMessage] = useState<string | null>(null);
  const [totalDeals, setTotalDeals] = useState(0);
  const isUpdatingDealRef = useRef(false);
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const mixpanelProps = {
    $name: `${user?.name}`,
    $email: user?.email,
  };
  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const [stages, dealStages] = await Promise.all([
        getDeals(),
        getDealStages(),
      ]);

      if (dealStages.length) {
        dispatch(setDealStages(dealStages));
      }
      const columnsData: Columns = {};
      stages.forEach((stage: any) => {
        columnsData[stage.id] = {
          title: stage.name,
          name: stage.name.toLowerCase().replace(/\s+/g, "-"),
          position: stage.position,
          deals: stage.deals,
        };
      });

      setColumns(columnsData);
      setError(null);
    } catch (err) {
      console.error("Error fetching deal data:", err);
      setError("Failed to load deal data. Please try again later.");
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleNewDeal = useCallback(() => {
    isUpdatingDealRef.current = false;
    dispatch(setFileDetails(null));
    dispatch(setDealDocumentsDetails([]));
    dispatch(setDealNotes([]));
    dispatch(setDealOffers([]));
    setFormData(defaultDealTask);
    setActiveId(null);
    setDialogOpen(true);
    mixpanelCustomEvent({
      mixpanelProps: {
        ...mixpanelProps,
      },
      id: user?.uid?.toString(),
      eventName: MixpanelEventName.userClicksOnAddNewDeal,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, setFormData, setDialogOpen, setActiveId]);

  const addTask = useCallback(
    async (isEditMode: boolean, columnId: string, task: DealTask) => {
      isUpdatingDealRef.current = isEditMode;
      const payload = {
        companyName: task.companyName,
        companyNumber: task.companyNumber,
        country: task.country,
        website: task.website,
        contactName: task.contactName,
        email: task.email,
        phoneNumber: task.phoneNumber,

        currency: task.currency,
        isImportant: Boolean(task.isImportant),
        dealStageId: columnId,
        askingPrice: task.askingPrice,
        estimatedValuation: task.estimatedValuation,
        //isOfferAccepted: task.isOfferAccepted,
        isHotAgreed: task.isHotAgreed,
        dueDiligenceBeganOn: task.dueDiligenceBeganOn,
        dueDiligenceConclusionOn: task.dueDiligenceConclusionOn,
        spaDraftOn: task.spaDraftOn,
        isSpaAgreed: task.isSpaAgreed,
        isSignedByBuyer: task.isSignedByBuyer,
        isSignedBySeller: task.isSignedBySeller,
        finalValuationAgreed: task.finalValuationAgreed,
        finalValuationAgreedOn: task.finalValuationAgreedOn,
        accountStatus: task.accountStatus,
        completionStatus: task.completionStatus,
        iban: task.iban,
        isDualAuthorized: task.isDualAuthorized,
        latestSpaUrl: task.latestSpaUrl,
        virtualDataRoomUrl: task.virtualDataRoomUrl,
      } as CreateDealDTO;

      try {
        setIsLoading(true);

        let response;
        if (isEditMode) {
          response = await updateDeal(formData.id, payload);
        } else {
          response = await createDeal(payload);
        }

        if (response && response.id && uploadedFileDetails && !isEditMode) {
          console.log(
            "Attempting to upload document for new deal:",
            response.id,
            uploadedFileDetails.name
          );
          await uploadDealDocument(response.id, uploadedFileDetails);
          dispatch(setFileDetails(null));
          dispatch(setDealDocumentsDetails([]));
        }
        if (task.note && !isEditMode) {
          await saveNotes(response.id, task.note);
        }
        if (task.yourOffer && task.offerStatus && !isEditMode) {
          await createDealOffer(response.id, {
            offer: Number(task.yourOffer),
            status: task.offerStatus,
          });
        }
        const stages = await getDeals();
        const columnsData: any = {};

        stages.forEach((stage: any) => {
          columnsData[stage.id] = {
            title: stage.name,
            name: stage.name.toLowerCase().replace(/\s+/g, "-"),
            position: stage.position,
            deals: stage.deals?.map((deal: any) => ({
              ...deal,
              isImportant: Number(deal.isImportant),
            })),
          };
        });
        const totalDeals = Object.values(columnsData).flatMap(
          (stage: any) => stage.deals
        ).length;
        setTotalDeals(totalDeals);
        setColumns(columnsData);
        setDealSuccess(true);
        setTimeout(() => setDealSuccess(false), 5000);

        setDialogOpen(false);
        mixpanelCustomEvent({
          mixpanelProps: {
            ...mixpanelProps,
            dealId: response.id,
          },
          id: user?.uid?.toString(),
          eventName: isEditMode
            ? MixpanelEventName.userDealUpdatedSuccess
            : MixpanelEventName.userDealCreatedSuccess,
        });
      } catch (error) {
        console.error(
          "Error while creating/updating deal or uploading document:",
          error
        );
        setDealError(true);
        setDealErrorMessage(
          error instanceof Error
            ? (error as any)?.response?.data?.message
            : "Unknown error"
        );
        setTimeout(() => setDealError(false), 5000);
        mixpanelCustomEvent({
          mixpanelProps: {
            ...mixpanelProps,
            error:
              error instanceof Error
                ? (error as any)?.response?.data?.message
                : "Unknown error",
          },
          id: user?.uid?.toString(),
          eventName: MixpanelEventName.userDealError,
        });
      } finally {
        setIsLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      dispatch,
      setIsLoading,
      updateDeal,
      createDeal,
      uploadDealDocument,
      getDeals,
      setColumns,
      setDealSuccess,
      setDealError,
      setDialogOpen,
      uploadedFileDetails,
      formData.id,
    ]
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragEnd = useCallback(
    async (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over) return;

      const activeId = active.id as string;
      const overId = over.id as string;

      let sourceColumnId = "";
      let targetColumnId = "";
      let taskIndex = -1;
      let overTaskIndex = -1;

      // Find source column
      Object.entries(columns).forEach(([columnId, column]) => {
        const taskIdx = column?.deals?.findIndex(
          (task) => task.id === activeId
        );
        if (taskIdx !== -1) {
          sourceColumnId = columnId;
          taskIndex = taskIdx;
        }
      });

      // Find target column (where the task is dropped over)
      Object.entries(columns).forEach(([columnId, column]) => {
        const overIdx = column?.deals?.findIndex((task) => task.id === overId);
        if (overIdx !== -1) {
          targetColumnId = columnId;
          overTaskIndex = overIdx;
        }
      });

      // If dropped over a column (not a task), treat overId as columnId
      if (columns[overId]) {
        targetColumnId = overId;
        overTaskIndex = columns[overId].deals.length;
      }

      if (taskIndex === -1 || !targetColumnId) return;

      // Moving to different column
      if (sourceColumnId !== targetColumnId) {
        try {
          setIsLoading(true);
          await moveDeal(activeId, targetColumnId);
          const stages = await getDeals();
          const columnsData: Columns = {};
          stages.forEach((stage: any) => {
            columnsData[stage.id] = {
              title: stage.name,
              name: stage.name.toLowerCase().replace(/\s+/g, "-"),
              position: stage.position,
              deals: stage?.deals?.map((deal: any) => ({
                ...deal,
                isImportant: Number(deal.isImportant),
              })),
            };
          });
          setColumns(columnsData);
          setIsLoading(false);
        } catch (error) {
          console.error("Error moving deal to a different column:", error);
        }
      } else if (taskIndex !== overTaskIndex) {
        // Reordering within the same column
        try {
          setIsLoading(true);
          await positionDeal(activeId, overTaskIndex + 1);
          const stages = await getDeals();
          const columnsData: Columns = {};
          stages.forEach((stage: any) => {
            columnsData[stage.id] = {
              title: stage.name,
              name: stage.name.toLowerCase().replace(/\s+/g, "-"),
              position: stage.position,
              deals: stage?.deals?.map((deal: any) => ({
                ...deal,
                isImportant: Number(deal.isImportant),
              })),
            };
          });
          setColumns(columnsData);
          setIsLoading(false);
        } catch (error) {
          console.error("Error updating deal position:", error);
        }
      }

      setActiveId(null);
    },
    [columns]
  );

  const handleTaskClick = useCallback(
    (task: DealTask) => {
      const loadDealDetails = async (dealId: string) => {
        setIsLoading(true);
        try {
          const [dealDetails, dealDocuments, dealNotes, dealOffers] =
            await Promise.all([
              getDealById(dealId),
              getAllDealDocuments(dealId),
              getAllDealNotes(dealId),
              getAllDealOffers(dealId),
            ]);

          setFormData(dealDetails);
          dispatch(setDealDocumentsDetails(dealDocuments));
          dispatch(setDealNotes(dealNotes));
          dispatch(setDealOffers(dealOffers));
          setError(null);
          mixpanelCustomEvent({
            mixpanelProps: {
              ...mixpanelProps,
              dealId: dealDetails.id,
            },
            id: user?.uid?.toString(),
            eventName: MixpanelEventName.userClicksOnDeal,
          });
        } catch (error) {
          console.error("Error fetching deal details:", error);
          setError("Failed to load deal details. Please try again later.");
        } finally {
          setIsLoading(false);
        }
      };

      loadDealDetails(task.id);
      setDialogOpen(true);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch]
  );

  const handleSaveTask = useCallback(
    async (isEditMode: boolean, taskData: DealTask) => {
      const newTask = {
        ...taskData,
      };
      addTask(isEditMode, taskData.dealStageId, newTask);

      setDialogOpen(false);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [columns, addTask]
  );

  const getActiveTask = useCallback(() => {
    if (!activeId) return null;

    for (const columnId in columns) {
      const task = columns[columnId]?.deals?.find((t) => t.id === activeId);
      if (task) return task;
    }

    return null;
  }, [activeId, columns]);

  return {
    columns,
    activeId,
    dialogOpen,
    formData,
    isLoading,
    error,
    dealSuccess,
    dealError,
    isUpdatingDealRef,
    dealErrorMessage,
    totalDeals,
    setDialogOpen,
    setFormData,
    addTask,
    handleDragStart,
    handleDragEnd,
    handleTaskClick,
    handleSaveTask,
    getActiveTask,
    refreshData: fetchData,
    handleNewDeal,
  };
};
