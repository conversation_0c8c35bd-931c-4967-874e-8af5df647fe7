import React from "react";
import {
    useReactTable,
    getCoreRowModel,
    flexRender,
    ColumnDef,
} from "@tanstack/react-table";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "components/shadcn/ui/table";
import Skeleton from "react-loading-skeleton";
import Moment from "moment";
import SvgCheckGreenIcon from "components/common/iconComponents/checkGreenIcon";
import { getCurrencySymbol } from "helpers";

type Invoice = {
    invoice_pdf: string;
    number: string;
    period_start: number;
    total: number;
    paid: boolean;
    currency: string;
    status: string;
};

interface BillingTableProps {
    invoices: Invoice[] | null;
    selectedInvoices: string[];
    loading: boolean;
    checkBoxClicked: (id: string, checked: boolean) => void;
}

const BillingTable: React.FC<BillingTableProps> = ({
    invoices,
    selectedInvoices,
    loading,
    checkBoxClicked,
}) => {
    const data = React.useMemo(
        () =>
            loading
                ? Array(10).fill({} as Invoice)
                : invoices?.filter((inv) => inv.status !== "draft") || [],
        [invoices, loading]
    );

    const filteredInvoices = React.useMemo(
        () => invoices?.filter((inv) => inv.status !== "draft") || [],
        [invoices]
    );

    const isAllSelected =
        filteredInvoices.length > 0 &&
        filteredInvoices.every((inv) => selectedInvoices.includes(inv.invoice_pdf));

    const columns = React.useMemo<ColumnDef<Invoice>[]>(
        () => [
            {
                id: "invoice",
                header: () => (
                    <div className="flex items-center gap-2">
                        <input
                            id="checkbox_id"
                            className="checkbox billing"
                            type="checkbox"
                            checked={isAllSelected}

                            onChange={(e) => checkBoxClicked("all", e.target.checked)}
                        />
                        <label htmlFor="checkbox_id" className="checkboxLabel" />
                        <span>Invoice</span>
                    </div>
                ),
                cell: ({ row }) => {
                    const invoice = row.original;
                    if (loading) {
                        return (
                            <div className="flex items-center gap-2">
                                <Skeleton height={20} width={20} />
                                <Skeleton width={200} />
                            </div>
                        );
                    }

                    return (
                        <div className="flex items-center gap-2">
                            <input
                                id={`checkbox_id${row.index}`}
                                className="checkbox billing"
                                type="checkbox"
                                checked={selectedInvoices.includes(invoice.invoice_pdf)}
                                onChange={(e) =>
                                    checkBoxClicked(invoice.invoice_pdf, e.target.checked)
                                }
                            />
                            <label htmlFor={`checkbox_id${row.index}`} className="checkboxLabel" />
                            <span className="text-sm font-medium text-gray-900 text-left">
                                {`Invoice #${invoice.number || "TBD"} - ${Moment(
                                    invoice.period_start * 1000
                                ).format("MMM YYYY")}`}
                            </span>
                        </div>
                    );
                },
            },
            {
                header: "Billing date",
                accessorKey: "period_start",
                cell: ({ getValue }) => {
                    return loading ? (
                        <Skeleton width={90} />
                    ) : (
                        Moment((getValue() as number) * 1000).format("MMM DD, YYYY")
                    );
                },
            },
            {
                header: "Status",
                id: "status",
                cell: ({ row }) => {
                    const invoice = row.original;
                    if (loading) return <Skeleton width={60} borderRadius={12} />;
                    return invoice.paid ? (
                        <div className="flex items-center gap-1 h-6">
                            <SvgCheckGreenIcon />
                            <span className="text-xs font-medium text-green-700">Paid</span>
                        </div>
                    ) : null;
                },
            },
            {
                header: "Amount",
                accessorKey: "total",
                cell: ({ row, getValue }) => {
                    console.log(row.original);
                    const currency = row.original?.currency || "usd";
                    const symbol = getCurrencySymbol(currency);
                    return loading ? (
                        <Skeleton width={90} />
                    ) : (
                        `${symbol}${(getValue() as number) / 100}`
                    );
                },
            },
            {
                header: "Plan",
                id: "plan",
                cell: () => (loading ? <Skeleton width={90} /> : "Basic plan"),
            },
            {
                header: "",
                id: "download",
                cell: ({ row }) => {
                    const invoice = row.original;
                    return loading ? (
                        <Skeleton width={90} />
                    ) : (
                        <a
                            className="text-sm font-semibold text-primary-700"
                            href={invoice.invoice_pdf}
                        >
                            Download
                        </a>
                    );
                },
            },
        ],
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [loading, selectedInvoices, invoices, checkBoxClicked]
    );

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
    });

    return (
        <div className="rounded-lg border overflow-x-auto">
            <Table>
                <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <TableRow key={headerGroup.id}>
                            {headerGroup.headers.map((header) => (
                                <TableHead key={header.id}>
                                    {flexRender(header.column.columnDef.header, header.getContext())}
                                </TableHead>
                            ))}
                        </TableRow>
                    ))}
                </TableHeader>
                <TableBody>
                    {table.getRowModel().rows.length ? (
                        table.getRowModel().rows.map((row) => (
                            <TableRow key={row.id} className="text-left">
                                {row.getVisibleCells().map((cell) => (
                                    <TableCell key={cell.id}>
                                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                    </TableCell>
                                ))}
                            </TableRow>
                        ))
                    ) : (
                        <TableRow>
                            <TableCell colSpan={columns.length} className="h-24 text-center">
                                No results.
                            </TableCell>
                        </TableRow>
                    )}
                </TableBody>
            </Table>
        </div>
    );
};

export default BillingTable;
