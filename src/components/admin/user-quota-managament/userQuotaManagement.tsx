import { Search } from "lucide-react"
import { But<PERSON> } from "components/shadcn/ui/button"
import { Input } from "components/shadcn/ui/input"
import { Card, CardContent } from "components/shadcn/ui/card"
import { Badge } from "components/shadcn/ui/badge"
import { Alert } from "components/shadcn/ui/alert"
import { Progress } from "components/shadcn/ui/progress"
import { PlusCircle, MinusCircle } from "lucide-react"
import useUserQuotaManagement from "./useUserQoutaManagement"
import { SUBSCRIPTION_STATUS } from "components/subscription/types"

export default function UserQuotaManagement() {
    const {
        searchQuery,
        user,
        additionalQuotas,
        isLoading,
        quotaUpdatedSuccessfully,
        isError,
        errorMessage,
        adminCopy,
        incrementQuota,
        decrementQuota,
        setSearchQuery,
        handleSearch,
        handleQuotaChange,
        handleSaveChanges,
    } = useUserQuotaManagement()

    const calculateUsedPercentage = (remaining: number, total: number) => {
        const used = total - remaining
        return Math.round((used / total) * 100)
    }

    const renderUserDetails = () => (
        <div className="mb-8">
            <h2 className="text-xl font-bold mb-4">👤 {adminCopy.userDetails}</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <p className="text-sm text-gray-500 mb-1">{adminCopy.name}</p>
                        <p className="text-base font-medium">{user?.name || "N/A"}</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <p className="text-sm text-gray-500 mb-1">{adminCopy.email}</p>
                        <p className="text-base font-medium">{user?.email || "N/A"}</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <p className="text-sm text-gray-500 mb-1">{adminCopy.currentSubscription}</p>
                        <p className="text-base font-medium">
                            {user?.subscription?.productDisplayName || "N/A"} -{" "}
                            {user?.subscription?.productPricing.interval === "year" ? "Annually" : "Monthly"}
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <p className="text-sm text-gray-500 mb-1">{adminCopy.userId}</p>
                        <p className="text-base font-medium">{user?.uid || "N/A"}</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <p className="text-sm text-gray-500 mb-1">{adminCopy.subscriptionStatus}</p>
                        <Badge
                            variant="secondary"
                            className={`px-3 py-1 mt-1 rounded-full text-sm ${user?.subscription?.status === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                                }`}
                        >
                            {user?.subscription?.status || "Unknown"}
                        </Badge>
                    </CardContent>
                </Card>
            </div>
        </div>
    )

    const renderQuotaDisplay = () => (
        <div className="mb-6">
            <h2 className="text-lg font-semibold mb-4">{adminCopy.quotaDetails}</h2>
            <div className="flex flex-wrap justify-center gap-6">
                {user?.subscription.userFeatureQuotas.map((quota) => {
                    const usedQuota = quota.totalQuota - quota.remainingQuota
                    const usedPercentage = calculateUsedPercentage(quota.remainingQuota, quota.totalQuota)

                    return (
                        <Card key={quota.featureName} className="flex flex-col items-center p-4 w-[fit-content]">
                            <div className="w-full px-4 mb-2 flex flex-col gap-2">
                                <p className="font-semibold text-gray-700 mb-2 text-md mt-2">{quota.featureName}</p>
                                <div className="flex flex-col justify-between text-sm font-semibold mb-1 gap-2 text-left">
                                    <span className="text-gray-900">{adminCopy.totalQuota} {quota.totalQuota}</span> {/* warm mustard/brown */}
                                    <span className="text-[#A11043]">{adminCopy.usedQuota} {usedQuota}</span> {/* vintage red */}
                                    <span className="text-[#067647]">{adminCopy.remainingQuota} {quota.remainingQuota}</span> {/* earthy green (you might need to extend Tailwind theme for "olive") */}

                                </div>
                                <Progress value={usedPercentage} className="h-3 bg-green-100" />
                            </div>
                        </Card>
                    )
                })}
            </div>
        </div>
    )

    const renderQuotaUpdateForm = () => {
        return (
            <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4 text-center">{adminCopy.updateRemainingQuota}</h2>
                <div className="flex flex-wrap justify-center gap-6">
                    {user?.subscription.userFeatureQuotas.map((quota) => {
                        const value = additionalQuotas[quota.featureName] ?? 0
                        const displayValue = value < 100 ? 100 : value

                        return (
                            <Card key={quota.id} className="flex flex-col items-center p-4 w-[fit-content]">
                                <p className="text-md text-gray-700 mb-2 text-center mt-2 font-semibold">{quota.featureName}</p>
                                <CardContent className="flex flex-row items-center gap-4 justify-center w-full">
                                    <button
                                        onClick={() => {
                                            if (value > 100) {
                                                decrementQuota(quota.featureName)
                                            }
                                        }}
                                        disabled={displayValue <= 100}
                                        className="border rounded-full p-1 disabled:opacity-50"
                                    >
                                        <MinusCircle
                                            className={`h-5 w-5 ${displayValue <= 100 ? "text-gray-300" : "text-gray-600 hover:text-gray-800"}`}
                                        />
                                    </button>
                                    <Input
                                        disabled
                                        value={String(displayValue)}
                                        onChange={(e) => handleQuotaChange(quota.featureName, e.target.value)}
                                        className="w-[100px] text-center disabled:text-gray-700 disabled:bg-gray-50 disabled:opacity-100"
                                    />
                                    <button onClick={() => incrementQuota(quota.featureName)} className="border rounded-full p-1">
                                        <PlusCircle className="h-5 w-5 text-gray-600 hover:text-gray-800" />
                                    </button>
                                </CardContent>
                            </Card>
                        )
                    })}
                </div>
            </div>
        )
    }

    return (
        <div className="w-full max-w-3xl mx-auto p-6 bg-white rounded-lg shadow-sm">
            {isLoading && (
                <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-lg">
                        <div className="w-8 h-8 border-4 border-t-gray-600 border-r-transparent border-b-gray-600 border-l-transparent rounded-full animate-spin"></div>
                    </div>
                </div>
            )}

            <h1 className="text-2xl font-bold mb-6">{adminCopy.adminQuotaManagement}</h1>

            <div className="flex gap-2 mb-6">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                        placeholder="Search by UID or email address"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <Button variant="primary" className="text-white" onClick={handleSearch}>
                    {adminCopy.search}
                </Button>
            </div>

            {isError && errorMessage && (
                <Alert
                    variant="destructive"
                    className="mb-3"
                    title={errorMessage}
                />
            )
            }

            {
                !user ? (
                    <div className="text-center py-12 text-gray-500">{adminCopy.searchSubText}</div>
                ) : (
                    <div className="flex flex-col gap-5">
                        {quotaUpdatedSuccessfully && (
                            <Alert
                                variant={"success"}
                                title={adminCopy.successBannerText}
                            />
                        )}
                        {isError && !errorMessage && (
                            <Alert
                                variant="destructive"
                                className="mb-3"
                                title={adminCopy.errorBannerText}
                            />
                        )}
                        {renderUserDetails()}
                        {renderQuotaDisplay()}
                        {renderQuotaUpdateForm()}

                        <div className="flex w-full justify-end">
                            <Button
                                variant="primary"
                                onClick={handleSaveChanges}
                                className="flex gap-2 text-white"
                                disabled={
                                    Object.values(additionalQuotas).every((value) => value === 0) ||
                                    user?.subscription?.status !== SUBSCRIPTION_STATUS.ACTIVE
                                }
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                >
                                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
                                    <polyline points="17 21 17 13 7 13 7 21" />
                                    <polyline points="7 3 7 8 15 8" />
                                </svg>
                                {adminCopy.saveChanges}
                            </Button>
                        </div>
                    </div>
                )
            }
        </div >
    )
}
