import { useState } from "react";
import { UpdateQuotaPayload, UserSearchResponse } from "../types";
import { searchUser, updateFeatureQuota } from "../services";
import lang from "lang";

export default function useUserQuotaManagement() {
  const { admin: adminCopy } = lang;

  const [searchQuery, setSearchQuery] = useState("");
  const [user, setUser] = useState<UserSearchResponse | null>(null);
  const [additionalQuotas, setAdditionalQuotas] = useState<
    Record<string, number>
  >({});
  const [isLoading, setIsLoading] = useState(false);
  const [quotaUpdatedSuccessfully, setQuotaUpdatedSuccessfully] =
    useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | false>(false);

  const initializeQuotas = (quotas: any[]) => {
    const initialState: Record<string, number> = {};
    quotas.forEach((q) => {
      initialState[q.featureName] = initialState[q.featureName.quota];
      initialState[q.featureName] = q.remainingQuota;
    });
    return initialState;
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    setIsLoading(true);
    setQuotaUpdatedSuccessfully(false);

    try {
      const response = await searchUser(searchQuery);
      setUser(response);
      setAdditionalQuotas(
        initializeQuotas(response.subscription.userFeatureQuotas)
      );
      setIsError(false);
      setErrorMessage(false);
    } catch (error: any) {
      console.error("Search failed", error);
      setUser(null);
      setIsError(true);

      const apiError = error?.response?.data;
      if (apiError?.statusCode === 404) {
        setErrorMessage(apiError.message);
      } else {
        setErrorMessage("An unexpected error occurred during search.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuotaChange = (featureName: string, value: string) => {
    const numericValue = parseInt(value, 10);
    setAdditionalQuotas((prev) => ({
      ...prev,
      [featureName]: isNaN(numericValue) ? 0 : numericValue,
    }));
  };

  const handleSaveChanges = async () => {
    if (!user) return;
    setIsLoading(true);
    setQuotaUpdatedSuccessfully(false);

    try {
      const quotasPayload: UpdateQuotaPayload = {
        quotas: user.subscription.userFeatureQuotas
          .filter((q) => additionalQuotas[q.featureName])
          .map((q) => ({
            userFeatureQuotaId: q.id,
            quota:
              additionalQuotas[q.featureName] < 100
                ? 100
                : additionalQuotas[q.featureName],
          })),
      };

      await updateFeatureQuota(user.uid, quotasPayload);
      await handleSearch(); // Re-fetch user to get updated quotas
      setQuotaUpdatedSuccessfully(true);
      setIsError(false);
    } catch (error: any) {
      console.error("Failed to update quotas", error);
      const apiError = error?.response?.data;
      if (apiError?.statusCode === 400) {
        setErrorMessage(apiError.message);
        setIsError(true);
      } else {
        setIsError(true);
      }
    } finally {
      setIsLoading(false);
      window.scrollTo({
        top: 0,
      });
    }
  };

  const incrementQuota = (featureName: string) => {
    setAdditionalQuotas((prev) => ({
      ...prev,
      [featureName]: (prev[featureName] || 0) + 100,
    }));
  };

  const decrementQuota = (featureName: string) => {
    setAdditionalQuotas((prev) => {
      return {
        ...prev,
        [featureName]: (prev[featureName] || 0) - 100,
      };
    });
  };

  return {
    searchQuery,
    user,
    additionalQuotas,
    isLoading,
    quotaUpdatedSuccessfully,
    isError,
    errorMessage,
    adminCopy,
    incrementQuota,
    decrementQuota,
    setSearchQuery,
    handleSearch,
    handleQuotaChange,
    handleSaveChanges,
  };
}
