import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "components/shadcn/ui/tabs";
import clsx from "clsx";

import lang from "lang";
import UserQuotaManagement from "./user-quota-managament/userQuotaManagement";
import AIDataScreen from "screens/AIData";
import ApiKeys from "./api-keys/apiKeys";

export default function Admin() {
    const { admin: adminCopy } = lang;
    const [activeTab, setActiveTab] = useState(adminCopy.userQuota);

    // const UserProfileTab = () => (
    //     <Card>
    //         <CardContent className="p-4">
    //             <UserProfile />
    //         </CardContent>
    //     </Card>
    // );

    // const BurnerDomainsTab = () => (
    //     <Card>
    //         <CardContent className="p-4">
    //             <h2 className="text-left text-xl font-semibold">{adminCopy.burnerDomains}</h2>
    //             <BurnerDomainsTable />
    //         </CardContent>
    //     </Card>
    // );

    return (
        <div className="fullScreen">
            <div className="container">
                <div className="savedFiltersScreen">
                    <div className="sfTitles flex flex-col gap-5 justify-between border border-b">
                        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                            <TabsList className="flex space-x-4 p-2 bg-white rounded-lg border border-b">
                                {adminCopy.tabOptions.map(({ value, label }) => (
                                    <TabsTrigger
                                        key={value}
                                        value={value}
                                        className={clsx("height-[40px] ", activeTab === value ? "text-primary-700" : "text-gray-500")}
                                    >
                                        {label}
                                    </TabsTrigger>
                                ))}
                            </TabsList>

                            {/* <TabsContent value="profile">
                                <UserProfileTab />
                            </TabsContent>

                            <TabsContent value="domains">
                                <BurnerDomainsTab />
                            </TabsContent> */}

                            <TabsContent value="userQuota">
                                <UserQuotaManagement />
                            </TabsContent>
                            <TabsContent value="aiData">
                                <AIDataScreen />
                            </TabsContent>
                            <TabsContent value="apiKeys">
                                <ApiKeys />
                            </TabsContent>
                        </Tabs>
                    </div>
                </div>
            </div>
        </div>
    );
}


