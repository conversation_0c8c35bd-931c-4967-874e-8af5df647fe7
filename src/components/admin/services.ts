import axiosWithToken from "axiosWithToken";
import {
  AddDkimPayload,
  UpdateQuotaPayload,
  ApiKey,
  CreateApiKeyPayload,
  ApiKeyTokenResponse,
  CreateApiKeyResponse,
} from "./types";

const baseURL2 = process.env.REACT_APP_BASEURL2;

export const getBurnerDomainList = async () => {
  try {
    const response = await axiosWithToken.get(`${baseURL2}api/admin/domains`);
    return response.data;
  } catch (error) {
    console.error("Error posting data:", error);
    throw error;
  }
};

export const addDkimRecord = async (
  userId: string,
  recordId: string,
  payload: AddDkimPayload
) => {
  try {
    const response = await axiosWithToken.post(
      `${baseURL2}api/admin/domains/${userId}/add-dkim-record/${recordId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error("Error posting data:", error);
    throw error;
  }
};

export const searchUser = async (identifier: string) => {
  try {
    const { data } = await axiosWithToken.get(
      `${baseURL2}api/admin/user/search`,
      {
        params: { identifier },
      }
    );
    return data;
  } catch (error) {
    console.error("Failed to fetch user with identifier:", identifier, error);
    throw error;
  }
};

export const updateFeatureQuota = async (
  uid: string,
  payload: UpdateQuotaPayload
) => {
  try {
    const { data } = await axiosWithToken.patch(
      `${baseURL2}api/admin/user/${uid}/update-quota`,
      payload
    );

    return data;
  } catch (error) {
    console.error("Failed to update feature quota:", error);
    throw error;
  }
};

export const getApiKeys = async (): Promise<ApiKey[]> => {
  try {
    const response = await axiosWithToken.get(`${baseURL2}api/admin/api-keys`);
    return response.data;
  } catch (error) {
    console.error("Error fetching API keys:", error);
    throw error;
  }
};

export const createApiKey = async (
  payload: CreateApiKeyPayload
): Promise<CreateApiKeyResponse> => {
  try {
    const response = await axiosWithToken.post(
      `${baseURL2}api/admin/api-keys`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error("Error creating API key:", error);
    throw error;
  }
};

export const getApiKeyToken = async (
  apiKeyId: string
): Promise<ApiKeyTokenResponse> => {
  try {
    const response = await axiosWithToken.get(
      `${baseURL2}api/admin/api-keys/${apiKeyId}/view`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching API key token:", error);
    throw error;
  }
};
