import { useState, useEffect } from "react";
import { Scope, CreateApiKeyPayload, CreateApiKeyResponse } from "../../types";
import lang from "lang";

interface UseCreateApiDialogProps {
  onSubmit: (payload: CreateApiKeyPayload) => Promise<CreateApiKeyResponse>;
  onSuccess?: () => void;
}

export const useCreateApiDialog = ({
  onSubmit,
  onSuccess,
}: UseCreateApiDialogProps) => {
  const { admin: adminCopy } = lang;
  const [name, setName] = useState("");
  const [selectedScopes, setSelectedScopes] = useState<string[]>([]);
  const [errors, setErrors] = useState<{ name?: string; scopes?: string }>({});
  const [createdApiKey, setCreatedApiKey] =
    useState<CreateApiKeyResponse | null>(null);
  const [generatedToken, setGeneratedToken] = useState<string | null>(null);
  const [isTokenVisible, setIsTokenVisible] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [alert, setAlert] = useState<{
    type: "success" | "destructive";
    message: string;
  } | null>(null);

  const scopeOptions = [
    { value: Scope.DealCreate, label: Scope.DealCreate },
    { value: Scope.CampaignRead, label: Scope.CampaignRead },
    { value: Scope.UserRead, label: Scope.UserRead },
  ];

  // Auto-dismiss alert after 5 seconds
  useEffect(() => {
    if (alert) {
      const timer = setTimeout(() => {
        setAlert(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [alert]);

  const showAlert = (type: "success" | "destructive", message: string) => {
    setAlert({ type, message });
  };

  const handleScopeChange = (scope: string, checked: boolean) => {
    if (checked) {
      setSelectedScopes((prev) => [...prev, scope]);
    } else {
      setSelectedScopes((prev) => prev.filter((s) => s !== scope));
    }
    if (errors.scopes) {
      setErrors((prev) => ({ ...prev, scopes: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: { name?: string; scopes?: string } = {};

    if (!name.trim()) {
      newErrors.name = "Name is required";
    }

    if (selectedScopes.length === 0) {
      newErrors.scopes = "At least one scope must be selected";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const result = await onSubmit({
        name: name.trim(),
        scopes: selectedScopes,
      });
      setCreatedApiKey(result);
      setGeneratedToken(result.token);
      showAlert("success", "API key created successfully!");
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error creating API key:", error);
      showAlert("destructive", "Failed to create API key. Please try again.");
    }
  };

  const handleClose = () => {
    setName("");
    setSelectedScopes([]);
    setErrors({});
    setCreatedApiKey(null);
    setGeneratedToken(null);
    setIsTokenVisible(false);
    setIsCopied(false);
    setAlert(null);
  };

  const maskToken = (token: string) => {
    return "•".repeat(token.length);
  };

  const handleCopyToken = async () => {
    if (!generatedToken) return;

    try {
      await navigator.clipboard.writeText(generatedToken);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy token:", error);
    }
  };

  const toggleTokenVisibility = () => {
    setIsTokenVisible(!isTokenVisible);
  };

  const handleNameChange = (value: string) => {
    setName(value);
    if (errors.name) {
      setErrors((prev) => ({ ...prev, name: undefined }));
    }
  };

  return {
    adminCopy,
    name,
    selectedScopes,
    errors,
    createdApiKey,
    generatedToken,
    isTokenVisible,
    isCopied,
    alert,
    scopeOptions,

    handleScopeChange,
    handleSubmit,
    handleClose,
    handleCopyToken,
    toggleTokenVisibility,
    handleNameChange,
    showAlert,
    maskToken,
  };
};
