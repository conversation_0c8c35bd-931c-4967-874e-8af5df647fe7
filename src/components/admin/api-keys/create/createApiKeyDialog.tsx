import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import { Input } from "components/shadcn/ui/input";
import { Label } from "components/shadcn/ui/label";
import { Badge } from "components/shadcn/ui/badge";
import { Checkbox } from "components/shadcn/ui/checkbox";
import { Alert } from "components/shadcn/ui/alert";
import { Eye, EyeOff, Copy, Check, X } from "lucide-react";
import { CreateApiKeyPayload, CreateApiKeyResponse } from "../../types";
import { useCreateApiDialog } from "./useCreateApiDialog";

interface CreateApiKeyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (payload: CreateApiKeyPayload) => Promise<CreateApiKeyResponse>;
  onSuccess?: () => void;
  isLoading?: boolean;
}

const CreateApiKeyDialog: React.FC<CreateApiKeyDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  onSuccess,
  isLoading = false,
}) => {
  const {
    adminCopy,
    name,
    selectedScopes,
    errors,
    createdApiKey,
    generatedToken,
    isTokenVisible,
    isCopied,
    alert,
    scopeOptions,
    handleScopeChange,
    handleSubmit,
    handleClose,
    handleCopyToken,
    toggleTokenVisibility,
    handleNameChange,
    maskToken,
  } = useCreateApiDialog({ onSubmit, onSuccess });

  const handleDialogClose = () => {
    handleClose();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {generatedToken ? `${adminCopy.generatedApiKeyName} ${createdApiKey?.name}` : adminCopy.createNewApiKey}
          </DialogTitle>
          {generatedToken && (
            <button
              onClick={handleDialogClose}
              className="absolute top-2 right-2 transition-all"
              aria-label="Close"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </DialogHeader>

        {alert && (
          <Alert
            variant={alert.type}
            title={alert.type === "success" ? adminCopy.tokenCreatedSuccessfully : adminCopy.failedToCreateToken}
            showCloseIcon
            onClose={() => { }}
          />
        )}

        {!generatedToken ? (
          <>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">{adminCopy.name}</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Enter API key name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <span className="text-sm text-red-500">{errors.name}</span>
                )}
              </div>

              <div className="grid gap-2">
                <Label>{adminCopy.scopes}</Label>
                <div className="space-y-2">
                  {scopeOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={option.value}
                        checked={selectedScopes.includes(option.value)}
                        onCheckedChange={(checked) =>
                          handleScopeChange(option.value, checked as boolean)
                        }
                      />
                      <Label htmlFor={option.value} className="text-sm font-normal">
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
                {errors.scopes && (
                  <span className="text-sm text-red-500">{errors.scopes}</span>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={handleDialogClose} disabled={isLoading}>
                {adminCopy.cancel}
              </Button>
              <Button variant="primary" onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? adminCopy.creating : adminCopy.createApiKey}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label>{adminCopy.scopes}</Label>
                <div className="flex flex-wrap gap-1">
                  {createdApiKey?.scopes.map((scope, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {scope}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="grid gap-2">
                <Label>{adminCopy.generatedApiKeyTOken}</Label>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md border">
                    <code className="flex-1 text-sm font-mono break-all">
                      {isTokenVisible ? generatedToken : maskToken(generatedToken!)}
                    </code>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleTokenVisibility}
                        className="h-8 w-8"
                        title={isTokenVisible ? "Hide token" : "Show token"}
                      >
                        {isTokenVisible ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleCopyToken}
                        className="h-8 w-8"
                        title="Copy token"
                      >
                        {isCopied ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    {adminCopy.keepThisTokenSecure}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreateApiKeyDialog;
