import { useState, useEffect, useRef, useCallback } from "react";
import { Api<PERSON>ey, CreateApiKeyPayload } from "../types";
import { getApiKeys, createApiKey, getApiKeyToken } from "../services";
import lang from "lang";

export const useApiKeys = () => {
  const { admin: adminCopy } = lang;
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoadingToken, setIsLoadingToken] = useState(false);
  const [selectedApiKey, setSelectedApiKey] = useState<ApiKey | null>(null);
  const [selectedToken, setSelectedToken] = useState<string | null>(null);
  const hasFetchedRef = useRef(false);

  const fetchApiKeys = useCallback(async () => {
    try {
      setIsLoading(true);
      const keys = await getApiKeys();
      setApiKeys(keys);
    } catch (error) {
      console.error("Error fetching API keys:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!hasFetchedRef.current) {
      hasFetchedRef.current = true;
      fetchApiKeys();
    }
  }, [fetchApiKeys]);

  const handleCreateApiKey = async (payload: CreateApiKeyPayload) => {
    try {
      setIsCreating(true);
      const result = await createApiKey(payload);
      return result;
    } catch (error) {
      console.error("Error creating API key:", error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  };

  const handleCreateSuccess = async () => {
    await fetchApiKeys();
  };

  const handleRowClick = async (apiKey: ApiKey) => {
    try {
      setSelectedApiKey(apiKey);
      setSelectedToken(null);
      setIsViewDialogOpen(true);
      setIsLoadingToken(true);

      const tokenResponse = await getApiKeyToken(apiKey.id);
      setSelectedToken(tokenResponse.token);
    } catch (error) {
      console.error("Error fetching API key token:", error);
    } finally {
      setIsLoadingToken(false);
    }
  };

  const handleCloseViewDialog = () => {
    setIsViewDialogOpen(false);
    setSelectedApiKey(null);
    setSelectedToken(null);
  };

  const openCreateDialog = () => {
    setIsCreateDialogOpen(true);
  };

  const closeCreateDialog = () => {
    setIsCreateDialogOpen(false);
  };

  return {
    adminCopy,
    apiKeys,
    isLoading,
    isCreateDialogOpen,
    isViewDialogOpen,
    isCreating,
    isLoadingToken,
    selectedApiKey,
    selectedToken,

    handleCreateApiKey,
    handleCreateSuccess,
    handleRowClick,
    handleCloseViewDialog,
    openCreateDialog,
    closeCreateDialog,
    fetchApiKeys,
  };
};
