import React from "react";
import { Button } from "components/shadcn/ui/button";
import { Plus } from "lucide-react";
import ApiKeysTable from "./apiKeysTable";
import CreateApiKeyDialog from "./create/createApiKeyDialog";
import ViewApiKeyDialog from "./view/viewApiKeyDialog";
import { useApiKeys } from "./useApiKeys";

const ApiKeys: React.FC = () => {
  const {
    adminCopy,
    apiKeys,
    isLoading,
    isCreateDialogOpen,
    isViewDialogOpen,
    isCreating,
    isLoadingToken,
    selectedApiKey,
    selectedToken,
    handleCreateApiKey,
    handleCreateSuccess,
    handleRowClick,
    handleCloseViewDialog,
    openCreateDialog,
    closeCreateDialog,
  } = useApiKeys();

  return (
    <div>
      <div className="flex justify-between items-center flex-wrap gap-4 p-8">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 text-left pl-0">{adminCopy.apiKeys}</h2>
          <p className="text-sm text-gray-600 mt-1">
            {adminCopy.manageApiKeys}
          </p>
        </div>
        <Button
          onClick={openCreateDialog}
          className="flex items-center gap-2"
          variant={"primary"}
        >
          <Plus className="h-4 w-4" />
          {adminCopy.createNewApiKey}
        </Button>
      </div>

      <div className="p-10">
        <ApiKeysTable
          apiKeys={apiKeys}
          onViewClick={handleRowClick}
          isLoading={isLoading}
        />
      </div>

      <CreateApiKeyDialog
        isOpen={isCreateDialogOpen}
        onClose={closeCreateDialog}
        onSubmit={handleCreateApiKey}
        onSuccess={handleCreateSuccess}
        isLoading={isCreating}
      />

      <ViewApiKeyDialog
        isOpen={isViewDialogOpen}
        onClose={handleCloseViewDialog}
        apiKey={selectedApiKey}
        token={selectedToken}
        isLoading={isLoadingToken}
      />
    </div>
  );
};

export default ApiKeys;
