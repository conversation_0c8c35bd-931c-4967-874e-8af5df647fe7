import { useState, useEffect } from "react";
import { Api<PERSON><PERSON> } from "../../types";
import lang from "lang";

interface UseViewApiDialogProps {
  isOpen: boolean;
  apiKey: ApiKey | null;
  token: string | null;
}

export const useViewApiDialog = ({
  isOpen,
  apiKey,
  token,
}: UseViewApiDialogProps) => {
  const { admin: adminCopy } = lang;
  const [isTokenVisible, setIsTokenVisible] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setIsTokenVisible(false);
      setIsCopied(false);
    }
  }, [isOpen]);

  const maskToken = (token: string) => {
    return "•".repeat(token.length);
  };

  const handleCopyToken = async () => {
    if (!token) return;

    try {
      await navigator.clipboard.writeText(token);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy token:", error);
    }
  };

  const toggleTokenVisibility = () => {
    setIsTokenVisible(!isTokenVisible);
  };

  return {
    adminCopy,
    isTokenVisible,
    isCopied,

    maskToken,
    handleCopyToken,
    toggleTokenVisibility,
  };
};
